import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>cle, AlertCircle, ArrowLeft } from "lucide-react";
import { CreateInvoiceForm } from "@/components/forms/CreateInvoiceForm";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Invoice } from "@/types";

interface CreateInvoicePageProps {
  onNavigateBack: () => void;
}

export function CreateInvoicePage({ onNavigateBack }: CreateInvoicePageProps) {
  const [createdInvoice, setCreatedInvoice] = useState<Invoice | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSuccess = (invoice: Invoice) => {
    setCreatedInvoice(invoice);
    setError(null);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setCreatedInvoice(null);
  };

  const handleCreateAnother = () => {
    setCreatedInvoice(null);
    setError(null);
  };

  if (createdInvoice) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-6 w-6" />
              Invoice Created Successfully!
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold text-lg mb-3">Invoice Details</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Invoice Number:</span>
                    <span className="font-medium">{createdInvoice.invoiceNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Invoice Date:</span>
                    <span className="font-medium">
                      {new Date(createdInvoice.invoiceDate).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Due Date:</span>
                    <span className="font-medium">
                      {new Date(createdInvoice.dueDate).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Status:</span>
                    <span className="font-medium">
                      {createdInvoice.status === 1 && "Draft"}
                      {createdInvoice.status === 2 && "Sent"}
                      {createdInvoice.status === 3 && "Paid"}
                      {createdInvoice.status === 4 && "Overdue"}
                      {createdInvoice.status === 5 && "Cancelled"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Currency:</span>
                    <span className="font-medium">{createdInvoice.currency}</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-lg mb-3">Financial Summary</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Subtotal:</span>
                    <span className="font-medium">
                      {createdInvoice.subTotal.toFixed(2)} {createdInvoice.currency}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">VAT Amount:</span>
                    <span className="font-medium">
                      {createdInvoice.vatAmount.toFixed(2)} {createdInvoice.currency}
                    </span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total Amount:</span>
                      <span>
                        {createdInvoice.totalAmount.toFixed(2)} {createdInvoice.currency}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {createdInvoice.company && (
              <div>
                <h3 className="font-semibold text-lg mb-3">Company Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="space-y-1">
                      <div className="font-medium">{createdInvoice.company.name}</div>
                      <div className="text-sm text-muted-foreground">
                        ЕИК/Булстат: {createdInvoice.company.bulstatNumber}
                      </div>
                      {createdInvoice.company.vatNumber && (
                        <div className="text-sm text-muted-foreground">
                          VAT: {createdInvoice.company.vatNumber}
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <div className="space-y-1">
                      <div className="text-sm">{createdInvoice.company.address}</div>
                      {createdInvoice.company.city && (
                        <div className="text-sm">
                          {createdInvoice.company.city}
                          {createdInvoice.company.postalCode && `, ${createdInvoice.company.postalCode}`}
                        </div>
                      )}
                      <div className="text-sm">{createdInvoice.company.country}</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {createdInvoice.customer && (
              <div>
                <h3 className="font-semibold text-lg mb-3">Customer Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="space-y-1">
                      <div className="font-medium">{createdInvoice.customer.name}</div>
                      {createdInvoice.customer.bulstatNumber && (
                        <div className="text-sm text-muted-foreground">
                          ЕИК/Булстат: {createdInvoice.customer.bulstatNumber}
                        </div>
                      )}
                      {createdInvoice.customer.personalId && (
                        <div className="text-sm text-muted-foreground">
                          ЕГН: {createdInvoice.customer.personalId}
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <div className="space-y-1">
                      <div className="text-sm">{createdInvoice.customer.address}</div>
                      {createdInvoice.customer.city && (
                        <div className="text-sm">
                          {createdInvoice.customer.city}
                          {createdInvoice.customer.postalCode && `, ${createdInvoice.customer.postalCode}`}
                        </div>
                      )}
                      <div className="text-sm">{createdInvoice.customer.country}</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {createdInvoice.invoiceItems && createdInvoice.invoiceItems.length > 0 && (
              <div>
                <h3 className="font-semibold text-lg mb-3">Invoice Items</h3>
                <div className="text-sm text-muted-foreground mb-2">
                  {createdInvoice.invoiceItems.length} item(s) added to this invoice
                </div>
              </div>
            )}

            {(createdInvoice.notes || createdInvoice.terms) && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {createdInvoice.notes && (
                  <div>
                    <h4 className="font-medium mb-2">Notes</h4>
                    <p className="text-sm text-muted-foreground">{createdInvoice.notes}</p>
                  </div>
                )}
                {createdInvoice.terms && (
                  <div>
                    <h4 className="font-medium mb-2">Terms & Conditions</h4>
                    <p className="text-sm text-muted-foreground">{createdInvoice.terms}</p>
                  </div>
                )}
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button onClick={handleCreateAnother} variant="outline">
                Create Another Invoice
              </Button>
              <Button onClick={onNavigateBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <Button onClick={onNavigateBack} variant="outline" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Home
        </Button>
      </div>

      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">Error creating invoice</span>
            </div>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </CardContent>
        </Card>
      )}

      <CreateInvoiceForm onSuccess={handleSuccess} onError={handleError} />
    </div>
  );
}
