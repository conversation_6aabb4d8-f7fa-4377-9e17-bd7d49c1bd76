import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { apiService } from "@/services/api";
import { CreateCustomerRequest, CustomerType, ApiError } from "@/types";

const customerSchema = z
  .object({
    customerType: z.nativeEnum(CustomerType, {
      errorMap: () => ({ message: "Please select a customer type" }),
    }),
    name: z.string().min(1, "Customer name is required").max(200, "Customer name cannot exceed 200 characters"),
    bulstatNumber: z.string().optional(),
    vatNumber: z.string().max(20, "VAT number cannot exceed 20 characters").optional().or(z.literal("")),
    personalId: z.string().optional(),
    address: z.string().min(1, "Address is required").max(500, "Address cannot exceed 500 characters"),
    city: z.string().max(100, "City cannot exceed 100 characters").optional().or(z.literal("")),
    postalCode: z.string().max(10, "Postal code cannot exceed 10 characters").optional().or(z.literal("")),
    country: z.string().max(100, "Country cannot exceed 100 characters").default("Bulgaria"),
    phone: z.string().max(20, "Phone number cannot exceed 20 characters").optional().or(z.literal("")),
    email: z.string().email("Invalid email format").max(100, "Email cannot exceed 100 characters").optional().or(z.literal("")),
    contactPerson: z.string().max(100, "Contact person name cannot exceed 100 characters").optional().or(z.literal("")),
  })
  .refine(
    (data) => {
      if (data.customerType === CustomerType.Business) {
        return data.bulstatNumber && data.bulstatNumber.length >= 9 && data.bulstatNumber.length <= 13 && /^\d{9,13}$/.test(data.bulstatNumber);
      }
      if (data.customerType === CustomerType.Residential) {
        return data.personalId && data.personalId.length === 10 && /^\d{10}$/.test(data.personalId);
      }
      return false;
    },
    {
      message: "Business customers require a valid ЕИК/Булстат (9-13 digits), residential customers require a valid ЕГН (10 digits)",
      path: ["bulstatNumber"], // This will show the error on the bulstatNumber field
    }
  );

type CustomerFormData = z.infer<typeof customerSchema>;

interface CreateCustomerFormProps {
  onSuccess?: (customer: any) => void;
  onCancel?: () => void;
}

export function CreateCustomerForm({ onSuccess, onCancel }: CreateCustomerFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const form = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      customerType: CustomerType.Business,
      name: "",
      bulstatNumber: "",
      vatNumber: "",
      personalId: "",
      address: "",
      city: "",
      postalCode: "",
      country: "Bulgaria",
      phone: "",
      email: "",
      contactPerson: "",
    },
  });

  const customerType = form.watch("customerType");

  const onSubmit = async (data: CustomerFormData) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Convert empty strings to undefined for optional fields
      const requestData: CreateCustomerRequest = {
        name: data.name,
        customerType: data.customerType,
        address: data.address,
        country: data.country,
        bulstatNumber: data.customerType === CustomerType.Business ? data.bulstatNumber : undefined,
        personalId: data.customerType === CustomerType.Residential ? data.personalId : undefined,
        vatNumber: data.vatNumber || undefined,
        city: data.city || undefined,
        postalCode: data.postalCode || undefined,
        phone: data.phone || undefined,
        email: data.email || undefined,
        contactPerson: data.contactPerson || undefined,
      };

      const customer = await apiService.createCustomer(requestData);
      onSuccess?.(customer);
      form.reset();
    } catch (error) {
      const apiError = error as ApiError;
      setSubmitError(apiError.message);

      // Set field-specific errors if available
      if (apiError.errors) {
        apiError.errors.forEach((err) => {
          form.setError(err.field as keyof CustomerFormData, {
            type: "server",
            message: err.message,
          });
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Create New Customer</CardTitle>
        <CardDescription>Add a new customer to your invoicing system. Choose between residential and business customers.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {submitError && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">{submitError}</div>}

            <FormField
              control={form.control}
              name="customerType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer Type *</FormLabel>
                  <Select onValueChange={(value) => field.onChange(Number(value))} defaultValue={field.value?.toString()}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select customer type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={CustomerType.Business.toString()}>Business Customer</SelectItem>
                      <SelectItem value={CustomerType.Residential.toString()}>Residential Customer</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>Business customers require ЕИК/Булстат, residential customers require ЕГН</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter customer name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {customerType === CustomerType.Business && (
                <>
                  <FormField
                    control={form.control}
                    name="bulstatNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>ЕИК/Булстат Number *</FormLabel>
                        <FormControl>
                          <Input placeholder="123456789" {...field} />
                        </FormControl>
                        <FormDescription>9-13 digit Bulgarian business identification number</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vatNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>VAT Number</FormLabel>
                        <FormControl>
                          <Input placeholder="BG123456789" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              {customerType === CustomerType.Residential && (
                <FormField
                  control={form.control}
                  name="personalId"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Personal ID (ЕГН) *</FormLabel>
                      <FormControl>
                        <Input placeholder="1234567890" {...field} />
                      </FormControl>
                      <FormDescription>10-digit Bulgarian personal identification number</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address *</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter customer address" className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>City</FormLabel>
                    <FormControl>
                      <Input placeholder="Sofia" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="postalCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Postal Code</FormLabel>
                    <FormControl>
                      <Input placeholder="1000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+359 2 123 4567" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {customerType === CustomerType.Business && (
              <FormField
                control={form.control}
                name="contactPerson"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contact Person</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="flex gap-4 pt-4">
              <Button type="submit" disabled={isSubmitting} className="flex-1">
                {isSubmitting ? "Creating..." : "Create Customer"}
              </Button>
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
