using System.ComponentModel.DataAnnotations;

namespace invoicer_ws.Models.Validation
{
    /// <summary>
    /// Custom validation attribute for Customer model to ensure proper validation based on customer type
    /// </summary>
    public class CustomerValidationAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value is not Customer customer)
                return false;

            return customer.CustomerType switch
            {
                CustomerType.Business => ValidateBusinessCustomer(customer),
                CustomerType.Residential => ValidateResidentialCustomer(customer),
                _ => false
            };
        }

        private static bool ValidateBusinessCustomer(Customer customer)
        {
            // Business customers must have Bulstat number
            if (string.IsNullOrEmpty(customer.BulstatNumber))
                return false;

            // Validate Bulstat number format (9-13 digits)
            if (!System.Text.RegularExpressions.Regex.IsMatch(customer.BulstatNumber, @"^\d{9,13}$"))
                return false;

            // Personal ID should be null for business customers
            if (!string.IsNullOrEmpty(customer.PersonalId))
                return false;

            return true;
        }

        private static bool ValidateResidentialCustomer(Customer customer)
        {
            // Residential customers must have Personal ID (ЕГН)
            if (string.IsNullOrEmpty(customer.PersonalId))
                return false;

            // Validate Personal ID format (exactly 10 digits)
            if (!System.Text.RegularExpressions.Regex.IsMatch(customer.PersonalId, @"^\d{10}$"))
                return false;

            // Bulstat number should be null for residential customers
            if (!string.IsNullOrEmpty(customer.BulstatNumber))
                return false;

            return true;
        }

        public override string FormatErrorMessage(string name)
        {
            return "Customer validation failed. Business customers must have Bulstat number, residential customers must have Personal ID.";
        }
    }
}
