import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { apiService } from "@/services/api";
import { CreateCompanyRequest, ApiError } from "@/types";

const companySchema = z.object({
  name: z.string().min(1, "Company name is required").max(200, "Company name cannot exceed 200 characters"),
  bulstatNumber: z
    .string()
    .min(9, "ЕИК/Булстат must be at least 9 digits")
    .max(13, "ЕИК/Булстат cannot exceed 13 digits")
    .regex(/^\d{9,13}$/, "ЕИК/Булстат must contain only digits"),
  vatNumber: z.string().max(20, "VAT number cannot exceed 20 characters").optional().or(z.literal("")),
  address: z.string().min(1, "Address is required").max(500, "Address cannot exceed 500 characters"),
  city: z.string().max(100, "City cannot exceed 100 characters").optional().or(z.literal("")),
  postalCode: z.string().max(10, "Postal code cannot exceed 10 characters").optional().or(z.literal("")),
  country: z.string().max(100, "Country cannot exceed 100 characters").default("Bulgaria"),
  phone: z.string().max(20, "Phone number cannot exceed 20 characters").optional().or(z.literal("")),
  email: z.string().email("Invalid email format").max(100, "Email cannot exceed 100 characters").optional().or(z.literal("")),
  website: z.string().url("Invalid website URL format").max(200, "Website URL cannot exceed 200 characters").optional().or(z.literal("")),
  contactPerson: z.string().max(100, "Contact person name cannot exceed 100 characters").optional().or(z.literal("")),
});

type CompanyFormData = z.infer<typeof companySchema>;

interface CreateCompanyFormProps {
  onSuccess?: (company: any) => void;
  onCancel?: () => void;
}

export function CreateCompanyForm({ onSuccess, onCancel }: CreateCompanyFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const form = useForm<CompanyFormData>({
    resolver: zodResolver(companySchema),
    defaultValues: {
      name: "",
      bulstatNumber: "",
      vatNumber: "",
      address: "",
      city: "",
      postalCode: "",
      country: "Bulgaria",
      phone: "",
      email: "",
      website: "",
      contactPerson: "",
    },
  });

  const onSubmit = async (data: CompanyFormData) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Convert empty strings to undefined for optional fields
      const requestData: CreateCompanyRequest = {
        name: data.name,
        bulstatNumber: data.bulstatNumber,
        address: data.address,
        country: data.country,
        vatNumber: data.vatNumber || undefined,
        city: data.city || undefined,
        postalCode: data.postalCode || undefined,
        phone: data.phone || undefined,
        email: data.email || undefined,
        website: data.website || undefined,
        contactPerson: data.contactPerson || undefined,
      };

      const company = await apiService.createCompany(requestData);
      onSuccess?.(company);
      form.reset();
    } catch (error) {
      const apiError = error as ApiError;
      setSubmitError(apiError.message);

      // Set field-specific errors if available
      if (apiError.errors) {
        apiError.errors.forEach((err) => {
          form.setError(err.field as keyof CompanyFormData, {
            type: "server",
            message: err.message,
          });
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Create New Company</CardTitle>
        <CardDescription>Add a new company (доставчик на услугите/продуктите) to your invoicing system.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {submitError && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">{submitError}</div>}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Company Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter company name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bulstatNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ЕИК/Булстат Number *</FormLabel>
                    <FormControl>
                      <Input placeholder="*********" {...field} />
                    </FormControl>
                    <FormDescription>9-13 digit Bulgarian business identification number</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="vatNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>VAT Number</FormLabel>
                    <FormControl>
                      <Input placeholder="BG*********" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address *</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter company address" className="min-h-[80px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>City</FormLabel>
                    <FormControl>
                      <Input placeholder="Sofia" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="postalCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Postal Code</FormLabel>
                    <FormControl>
                      <Input placeholder="1000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+359 2 123 4567" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input placeholder="https://www.company.bg" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contactPerson"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contact Person</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex gap-4 pt-4">
              <Button type="submit" disabled={isSubmitting} className="flex-1">
                {isSubmitting ? "Creating..." : "Create Company"}
              </Button>
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
