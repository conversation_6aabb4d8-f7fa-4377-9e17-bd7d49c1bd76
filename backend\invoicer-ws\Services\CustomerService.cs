using Microsoft.EntityFrameworkCore;
using invoicer_ws.Data;
using invoicer_ws.Models;
using invoicer_ws.Services.Interfaces;

namespace invoicer_ws.Services
{
    public class CustomerService : ICustomerService
    {
        private readonly InvoicerDbContext _context;
        private readonly ILogger<CustomerService> _logger;

        public CustomerService(InvoicerDbContext context, ILogger<CustomerService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<Customer>> GetAllCustomersAsync()
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all customers");
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> GetCustomersByTypeAsync(CustomerType customerType)
        {
            try
            {
                return await _context.Customers
                    .Where(c => c.<PERSON> && c.CustomerType == customerType)
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving customers by type {CustomerType}", customerType);
                throw;
            }
        }

        public async Task<Customer?> GetCustomerByIdAsync(int id)
        {
            try
            {
                return await _context.Customers
                    .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving customer with ID {CustomerId}", id);
                throw;
            }
        }

        public async Task<Customer?> GetCustomerByBulstatAsync(string bulstatNumber)
        {
            try
            {
                return await _context.Customers
                    .FirstOrDefaultAsync(c => c.BulstatNumber == bulstatNumber && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving customer with Bulstat {BulstatNumber}", bulstatNumber);
                throw;
            }
        }

        public async Task<Customer?> GetCustomerByPersonalIdAsync(string personalId)
        {
            try
            {
                return await _context.Customers
                    .FirstOrDefaultAsync(c => c.PersonalId == personalId && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving customer with Personal ID {PersonalId}", personalId);
                throw;
            }
        }

        public async Task<Customer> CreateCustomerAsync(Customer customer)
        {
            try
            {
                // Validate customer
                if (!await ValidateCustomerAsync(customer))
                {
                    throw new InvalidOperationException("Customer validation failed");
                }

                // Check for duplicates
                if (customer.CustomerType == CustomerType.Business &&
                    !string.IsNullOrEmpty(customer.BulstatNumber) &&
                    await BulstatExistsAsync(customer.BulstatNumber))
                {
                    throw new InvalidOperationException($"Customer with Bulstat number {customer.BulstatNumber} already exists");
                }

                if (customer.CustomerType == CustomerType.Residential &&
                    !string.IsNullOrEmpty(customer.PersonalId) &&
                    await PersonalIdExistsAsync(customer.PersonalId))
                {
                    throw new InvalidOperationException($"Customer with Personal ID {customer.PersonalId} already exists");
                }

                customer.CreatedAt = DateTime.UtcNow;
                customer.UpdatedAt = DateTime.UtcNow;

                _context.Customers.Add(customer);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created customer with ID {CustomerId}", customer.Id);
                return customer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating customer");
                throw;
            }
        }

        public async Task<Customer?> UpdateCustomerAsync(int id, Customer customer)
        {
            try
            {
                var existingCustomer = await _context.Customers.FindAsync(id);
                if (existingCustomer == null || !existingCustomer.IsActive)
                {
                    return null;
                }

                // Validate customer
                if (!await ValidateCustomerAsync(customer))
                {
                    throw new InvalidOperationException("Customer validation failed");
                }

                // Check for duplicates (excluding current customer)
                if (customer.CustomerType == CustomerType.Business &&
                    !string.IsNullOrEmpty(customer.BulstatNumber) &&
                    await BulstatExistsAsync(customer.BulstatNumber, id))
                {
                    throw new InvalidOperationException($"Customer with Bulstat number {customer.BulstatNumber} already exists");
                }

                if (customer.CustomerType == CustomerType.Residential &&
                    !string.IsNullOrEmpty(customer.PersonalId) &&
                    await PersonalIdExistsAsync(customer.PersonalId, id))
                {
                    throw new InvalidOperationException($"Customer with Personal ID {customer.PersonalId} already exists");
                }

                // Update properties
                existingCustomer.CustomerType = customer.CustomerType;
                existingCustomer.Name = customer.Name;
                existingCustomer.BulstatNumber = customer.BulstatNumber;
                existingCustomer.VatNumber = customer.VatNumber;
                existingCustomer.PersonalId = customer.PersonalId;
                existingCustomer.Address = customer.Address;
                existingCustomer.City = customer.City;
                existingCustomer.PostalCode = customer.PostalCode;
                existingCustomer.Country = customer.Country;
                existingCustomer.Phone = customer.Phone;
                existingCustomer.Email = customer.Email;
                existingCustomer.ContactPerson = customer.ContactPerson;
                existingCustomer.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated customer with ID {CustomerId}", id);
                return existingCustomer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer with ID {CustomerId}", id);
                throw;
            }
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null || !customer.IsActive)
                {
                    return false;
                }

                // Soft delete
                customer.IsActive = false;
                customer.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted customer with ID {CustomerId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting customer with ID {CustomerId}", id);
                throw;
            }
        }

        public async Task<bool> CustomerExistsAsync(int id)
        {
            try
            {
                return await _context.Customers
                    .AnyAsync(c => c.Id == id && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if customer exists with ID {CustomerId}", id);
                throw;
            }
        }

        public async Task<bool> BulstatExistsAsync(string bulstatNumber, int? excludeId = null)
        {
            try
            {
                var query = _context.Customers
                    .Where(c => c.BulstatNumber == bulstatNumber && c.IsActive);

                if (excludeId.HasValue)
                {
                    query = query.Where(c => c.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if Bulstat exists {BulstatNumber}", bulstatNumber);
                throw;
            }
        }

        public async Task<bool> PersonalIdExistsAsync(string personalId, int? excludeId = null)
        {
            try
            {
                var query = _context.Customers
                    .Where(c => c.PersonalId == personalId && c.IsActive);

                if (excludeId.HasValue)
                {
                    query = query.Where(c => c.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if Personal ID exists {PersonalId}", personalId);
                throw;
            }
        }

        public Task<bool> ValidateCustomerAsync(Customer customer)
        {
            try
            {
                var result = customer.CustomerType switch
                {
                    CustomerType.Business => !string.IsNullOrEmpty(customer.BulstatNumber) &&
                                           string.IsNullOrEmpty(customer.PersonalId),
                    CustomerType.Residential => !string.IsNullOrEmpty(customer.PersonalId) &&
                                              string.IsNullOrEmpty(customer.BulstatNumber),
                    _ => false
                };
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating customer");
                throw;
            }
        }
    }
}
