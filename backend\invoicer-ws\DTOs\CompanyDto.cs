using System.ComponentModel.DataAnnotations;

namespace invoicer_ws.DTOs
{
    public class CompanyDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Company name is required")]
        [StringLength(200, ErrorMessage = "Company name cannot exceed 200 characters")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "ЕИК/Булстат number is required")]
        [StringLength(13, MinimumLength = 9, ErrorMessage = "ЕИК/Булстат must be between 9 and 13 characters")]
        [RegularExpression(@"^\d{9,13}$", ErrorMessage = "ЕИК/Булстат must contain only digits")]
        public string BulstatNumber { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "VAT number cannot exceed 20 characters")]
        public string? VatNumber { get; set; }

        [Required(ErrorMessage = "Address is required")]
        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string Address { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        public string? City { get; set; }

        [StringLength(10, ErrorMessage = "Postal code cannot exceed 10 characters")]
        public string? PostalCode { get; set; }

        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        public string Country { get; set; } = "Bulgaria";

        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? Phone { get; set; }

        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string? Email { get; set; }

        [Url(ErrorMessage = "Invalid website URL format")]
        [StringLength(200, ErrorMessage = "Website URL cannot exceed 200 characters")]
        public string? Website { get; set; }

        [StringLength(100, ErrorMessage = "Contact person name cannot exceed 100 characters")]
        public string? ContactPerson { get; set; }

        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateCompanyDto
    {
        [Required(ErrorMessage = "Company name is required")]
        [StringLength(200, ErrorMessage = "Company name cannot exceed 200 characters")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "ЕИК/Булстат number is required")]
        [StringLength(13, MinimumLength = 9, ErrorMessage = "ЕИК/Булстат must be between 9 and 13 characters")]
        [RegularExpression(@"^\d{9,13}$", ErrorMessage = "ЕИК/Булстат must contain only digits")]
        public string BulstatNumber { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "VAT number cannot exceed 20 characters")]
        public string? VatNumber { get; set; }

        [Required(ErrorMessage = "Address is required")]
        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string Address { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        public string? City { get; set; }

        [StringLength(10, ErrorMessage = "Postal code cannot exceed 10 characters")]
        public string? PostalCode { get; set; }

        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        public string Country { get; set; } = "Bulgaria";

        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? Phone { get; set; }

        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string? Email { get; set; }

        [Url(ErrorMessage = "Invalid website URL format")]
        [StringLength(200, ErrorMessage = "Website URL cannot exceed 200 characters")]
        public string? Website { get; set; }

        [StringLength(100, ErrorMessage = "Contact person name cannot exceed 100 characters")]
        public string? ContactPerson { get; set; }
    }

    public class UpdateCompanyDto : CreateCompanyDto
    {
        // Inherits all properties from CreateCompanyDto
    }
}
