using invoicer_ws.Models;

namespace invoicer_ws.Services.Interfaces
{
    public interface IInvoiceItemService
    {
        Task<IEnumerable<InvoiceItem>> GetInvoiceItemsByInvoiceAsync(int invoiceId);
        Task<InvoiceItem?> GetInvoiceItemByIdAsync(int id);
        Task<InvoiceItem> CreateInvoiceItemAsync(InvoiceItem invoiceItem);
        Task<InvoiceItem?> UpdateInvoiceItemAsync(int id, InvoiceItem invoiceItem);
        Task<bool> DeleteInvoiceItemAsync(int id);
        Task<bool> InvoiceItemExistsAsync(int id);
        Task<IEnumerable<InvoiceItem>> CreateMultipleInvoiceItemsAsync(IEnumerable<InvoiceItem> invoiceItems);
        Task<bool> DeleteAllInvoiceItemsAsync(int invoiceId);
        Task<decimal> CalculateInvoiceSubTotalAsync(int invoiceId);
        Task<decimal> CalculateInvoiceVatAmountAsync(int invoiceId);
        Task<decimal> CalculateInvoiceTotalAsync(int invoiceId);
    }
}
