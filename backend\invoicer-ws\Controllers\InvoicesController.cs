using Microsoft.AspNetCore.Mvc;
using invoicer_ws.Models;
using invoicer_ws.Services.Interfaces;

namespace invoicer_ws.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class InvoicesController : ControllerBase
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ILogger<InvoicesController> _logger;

        public InvoicesController(IInvoiceService invoiceService, ILogger<InvoicesController> logger)
        {
            _invoiceService = invoiceService;
            _logger = logger;
        }

        /// <summary>
        /// Get all invoices
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Invoice>>> GetInvoices()
        {
            try
            {
                var invoices = await _invoiceService.GetAllInvoicesAsync();
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoices");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get invoice by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<Invoice>> GetInvoice(int id)
        {
            try
            {
                var invoice = await _invoiceService.GetInvoiceByIdAsync(id);
                if (invoice == null)
                {
                    return NotFound($"Invoice with ID {id} not found");
                }
                return Ok(invoice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoice with ID {InvoiceId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get invoice by number
        /// </summary>
        [HttpGet("number/{invoiceNumber}")]
        public async Task<ActionResult<Invoice>> GetInvoiceByNumber(string invoiceNumber)
        {
            try
            {
                var invoice = await _invoiceService.GetInvoiceByNumberAsync(invoiceNumber);
                if (invoice == null)
                {
                    return NotFound($"Invoice with number {invoiceNumber} not found");
                }
                return Ok(invoice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoice with number {InvoiceNumber}", invoiceNumber);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get invoices by company
        /// </summary>
        [HttpGet("company/{companyId}")]
        public async Task<ActionResult<IEnumerable<Invoice>>> GetInvoicesByCompany(int companyId)
        {
            try
            {
                var invoices = await _invoiceService.GetInvoicesByCompanyAsync(companyId);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoices for company {CompanyId}", companyId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get invoices by customer
        /// </summary>
        [HttpGet("customer/{customerId}")]
        public async Task<ActionResult<IEnumerable<Invoice>>> GetInvoicesByCustomer(int customerId)
        {
            try
            {
                var invoices = await _invoiceService.GetInvoicesByCustomerAsync(customerId);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoices for customer {CustomerId}", customerId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get invoices by status
        /// </summary>
        [HttpGet("status/{status}")]
        public async Task<ActionResult<IEnumerable<Invoice>>> GetInvoicesByStatus(InvoiceStatus status)
        {
            try
            {
                var invoices = await _invoiceService.GetInvoicesByStatusAsync(status);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoices with status {Status}", status);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get overdue invoices
        /// </summary>
        [HttpGet("overdue")]
        public async Task<ActionResult<IEnumerable<Invoice>>> GetOverdueInvoices()
        {
            try
            {
                var invoices = await _invoiceService.GetOverdueInvoicesAsync();
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving overdue invoices");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Create a new invoice
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<Invoice>> CreateInvoice(Invoice invoice)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var createdInvoice = await _invoiceService.CreateInvoiceAsync(invoice);
                return CreatedAtAction(nameof(GetInvoice), new { id = createdInvoice.Id }, createdInvoice);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation when creating invoice");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update an existing invoice
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<Invoice>> UpdateInvoice(int id, Invoice invoice)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var updatedInvoice = await _invoiceService.UpdateInvoiceAsync(id, invoice);
                if (updatedInvoice == null)
                {
                    return NotFound($"Invoice with ID {id} not found");
                }

                return Ok(updatedInvoice);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation when updating invoice with ID {InvoiceId}", id);
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice with ID {InvoiceId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Delete an invoice
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteInvoice(int id)
        {
            try
            {
                var deleted = await _invoiceService.DeleteInvoiceAsync(id);
                if (!deleted)
                {
                    return NotFound($"Invoice with ID {id} not found");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice with ID {InvoiceId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if invoice number exists
        /// </summary>
        [HttpGet("number/{invoiceNumber}/exists")]
        public async Task<ActionResult<object>> CheckInvoiceNumberExists(string invoiceNumber, [FromQuery] int? excludeId = null)
        {
            try
            {
                var exists = await _invoiceService.InvoiceNumberExistsAsync(invoiceNumber, excludeId);
                return Ok(new { exists });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if invoice number exists: {InvoiceNumber}", invoiceNumber);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Generate a new invoice number for a company
        /// </summary>
        [HttpGet("generate-number/{companyId}")]
        public async Task<ActionResult<object>> GenerateInvoiceNumber(int companyId)
        {
            try
            {
                var invoiceNumber = await _invoiceService.GenerateInvoiceNumberAsync(companyId);
                return Ok(new { invoiceNumber });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating invoice number for company {CompanyId}", companyId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update invoice status
        /// </summary>
        [HttpPatch("{id}/status")]
        public async Task<ActionResult<Invoice>> UpdateInvoiceStatus(int id, [FromBody] InvoiceStatus status)
        {
            try
            {
                var updatedInvoice = await _invoiceService.UpdateInvoiceStatusAsync(id, status);
                if (updatedInvoice == null)
                {
                    return NotFound($"Invoice with ID {id} not found");
                }

                return Ok(updatedInvoice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice status for ID {InvoiceId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Recalculate invoice totals
        /// </summary>
        [HttpPost("{id}/calculate-totals")]
        public async Task<ActionResult<Invoice>> CalculateInvoiceTotals(int id)
        {
            try
            {
                var invoice = await _invoiceService.CalculateInvoiceTotalsAsync(id);
                if (invoice == null)
                {
                    return NotFound($"Invoice with ID {id} not found");
                }

                return Ok(invoice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating totals for invoice ID {InvoiceId}", id);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
