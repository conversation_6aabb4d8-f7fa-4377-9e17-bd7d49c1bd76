// Company types
export interface Company {
  id: number;
  name: string;
  bulstatNumber: string;
  vatNumber?: string;
  address: string;
  city?: string;
  postalCode?: string;
  country: string;
  phone?: string;
  email?: string;
  website?: string;
  contactPerson?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCompanyRequest {
  name: string;
  bulstatNumber: string;
  vatNumber?: string;
  address: string;
  city?: string;
  postalCode?: string;
  country: string;
  phone?: string;
  email?: string;
  website?: string;
  contactPerson?: string;
}

// Customer types
export enum CustomerType {
  Residential = 1,
  Business = 2,
}

export interface Customer {
  id: number;
  customerType: CustomerType;
  name: string;
  bulstatNumber?: string;
  vatNumber?: string;
  personalId?: string;
  address: string;
  city?: string;
  postalCode?: string;
  country: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomerRequest {
  customerType: CustomerType;
  name: string;
  bulstatNumber?: string;
  vatNumber?: string;
  personalId?: string;
  address: string;
  city?: string;
  postalCode?: string;
  country: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
}

// Invoice types
export enum InvoiceStatus {
  Draft = 1,
  Sent = 2,
  Paid = 3,
  Overdue = 4,
  Cancelled = 5,
}

export interface InvoiceItem {
  id: number;
  invoiceId: number;
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  vatRate: number;
  lineTotal: number;
  vatAmount: number;
  lineTotalWithVat: number;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateInvoiceItemRequest {
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  vatRate: number;
  sortOrder: number;
}

export interface Invoice {
  id: number;
  invoiceNumber: string;
  companyId: number;
  customerId: number;
  invoiceDate: string;
  dueDate: string;
  status: InvoiceStatus;
  notes?: string;
  terms?: string;
  subTotal: number;
  vatAmount: number;
  totalAmount: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
  company?: Company;
  customer?: Customer;
  invoiceItems: InvoiceItem[];
}

export interface CreateInvoiceRequest {
  invoiceNumber: string;
  companyId: number;
  customerId: number;
  invoiceDate: string;
  dueDate: string;
  status?: InvoiceStatus;
  notes?: string;
  terms?: string;
  currency?: string;
  invoiceItems: CreateInvoiceItemRequest[];
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiError {
  message: string;
  errors?: ValidationError[];
}
