// Company types
export interface Company {
  id: number;
  name: string;
  bulstatNumber: string;
  vatNumber?: string;
  address: string;
  city?: string;
  postalCode?: string;
  country: string;
  phone?: string;
  email?: string;
  website?: string;
  contactPerson?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCompanyRequest {
  name: string;
  bulstatNumber: string;
  vatNumber?: string;
  address: string;
  city?: string;
  postalCode?: string;
  country: string;
  phone?: string;
  email?: string;
  website?: string;
  contactPerson?: string;
}

// Customer types
export enum CustomerType {
  Residential = 1,
  Business = 2,
}

export interface Customer {
  id: number;
  customerType: CustomerType;
  name: string;
  bulstatNumber?: string;
  vatNumber?: string;
  personalId?: string;
  address: string;
  city?: string;
  postalCode?: string;
  country: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomerRequest {
  customerType: CustomerType;
  name: string;
  bulstatNumber?: string;
  vatNumber?: string;
  personalId?: string;
  address: string;
  city?: string;
  postalCode?: string;
  country: string;
  phone?: string;
  email?: string;
  contactPerson?: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiError {
  message: string;
  errors?: ValidationError[];
}
