using Microsoft.AspNetCore.Mvc;
using invoicer_ws.Models;
using invoicer_ws.Services.Interfaces;

namespace invoicer_ws.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CompaniesController : ControllerBase
    {
        private readonly ICompanyService _companyService;
        private readonly ILogger<CompaniesController> _logger;

        public CompaniesController(ICompanyService companyService, ILogger<CompaniesController> logger)
        {
            _companyService = companyService;
            _logger = logger;
        }

        /// <summary>
        /// Get all companies
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Company>>> GetCompanies()
        {
            try
            {
                var companies = await _companyService.GetAllCompaniesAsync();
                return Ok(companies);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving companies");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get company by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<Company>> GetCompany(int id)
        {
            try
            {
                var company = await _companyService.GetCompanyByIdAsync(id);
                if (company == null)
                {
                    return NotFound($"Company with ID {id} not found");
                }

                return Ok(company);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving company with ID {CompanyId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get company by Bulstat number
        /// </summary>
        [HttpGet("bulstat/{bulstatNumber}")]
        public async Task<ActionResult<Company>> GetCompanyByBulstat(string bulstatNumber)
        {
            try
            {
                var company = await _companyService.GetCompanyByBulstatAsync(bulstatNumber);
                if (company == null)
                {
                    return NotFound($"Company with Bulstat {bulstatNumber} not found");
                }

                return Ok(company);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving company with Bulstat {BulstatNumber}", bulstatNumber);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Create a new company
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<Company>> CreateCompany(Company company)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var createdCompany = await _companyService.CreateCompanyAsync(company);
                return CreatedAtAction(nameof(GetCompany), new { id = createdCompany.Id }, createdCompany);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation when creating company");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating company");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update an existing company
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<Company>> UpdateCompany(int id, Company company)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var updatedCompany = await _companyService.UpdateCompanyAsync(id, company);
                if (updatedCompany == null)
                {
                    return NotFound($"Company with ID {id} not found");
                }

                return Ok(updatedCompany);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation when updating company with ID {CompanyId}", id);
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating company with ID {CompanyId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Delete a company (soft delete)
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCompany(int id)
        {
            try
            {
                var result = await _companyService.DeleteCompanyAsync(id);
                if (!result)
                {
                    return NotFound($"Company with ID {id} not found");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting company with ID {CompanyId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if company exists
        /// </summary>
        [HttpHead("{id}")]
        public async Task<IActionResult> CompanyExists(int id)
        {
            try
            {
                var exists = await _companyService.CompanyExistsAsync(id);
                return exists ? Ok() : NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if company exists with ID {CompanyId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if Bulstat number exists
        /// </summary>
        [HttpGet("bulstat/{bulstatNumber}/exists")]
        public async Task<ActionResult<bool>> BulstatExists(string bulstatNumber, [FromQuery] int? excludeId = null)
        {
            try
            {
                var exists = await _companyService.BulstatExistsAsync(bulstatNumber, excludeId);
                return Ok(new { exists });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if Bulstat exists {BulstatNumber}", bulstatNumber);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
