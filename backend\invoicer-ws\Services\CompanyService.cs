using Microsoft.EntityFrameworkCore;
using invoicer_ws.Data;
using invoicer_ws.Models;
using invoicer_ws.Services.Interfaces;

namespace invoicer_ws.Services
{
    public class CompanyService : ICompanyService
    {
        private readonly InvoicerDbContext _context;
        private readonly ILogger<CompanyService> _logger;

        public CompanyService(InvoicerDbContext context, ILogger<CompanyService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<Company>> GetAllCompaniesAsync()
        {
            try
            {
                return await _context.Companies
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all companies");
                throw;
            }
        }

        public async Task<Company?> GetCompanyByIdAsync(int id)
        {
            try
            {
                return await _context.Companies
                    .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving company with ID {CompanyId}", id);
                throw;
            }
        }

        public async Task<Company?> GetCompanyByBulstatAsync(string bulstatNumber)
        {
            try
            {
                return await _context.Companies
                    .FirstOrDefaultAsync(c => c.BulstatNumber == bulstatNumber && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving company with Bulstat {BulstatNumber}", bulstatNumber);
                throw;
            }
        }

        public async Task<Company> CreateCompanyAsync(Company company)
        {
            try
            {
                // Check if Bulstat already exists
                if (await BulstatExistsAsync(company.BulstatNumber))
                {
                    throw new InvalidOperationException($"Company with Bulstat number {company.BulstatNumber} already exists");
                }

                company.CreatedAt = DateTime.UtcNow;
                company.UpdatedAt = DateTime.UtcNow;

                _context.Companies.Add(company);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created company with ID {CompanyId}", company.Id);
                return company;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating company");
                throw;
            }
        }

        public async Task<Company?> UpdateCompanyAsync(int id, Company company)
        {
            try
            {
                var existingCompany = await _context.Companies.FindAsync(id);
                if (existingCompany == null || !existingCompany.IsActive)
                {
                    return null;
                }

                // Check if Bulstat already exists (excluding current company)
                if (await BulstatExistsAsync(company.BulstatNumber, id))
                {
                    throw new InvalidOperationException($"Company with Bulstat number {company.BulstatNumber} already exists");
                }

                // Update properties
                existingCompany.Name = company.Name;
                existingCompany.BulstatNumber = company.BulstatNumber;
                existingCompany.VatNumber = company.VatNumber;
                existingCompany.Address = company.Address;
                existingCompany.City = company.City;
                existingCompany.PostalCode = company.PostalCode;
                existingCompany.Country = company.Country;
                existingCompany.Phone = company.Phone;
                existingCompany.Email = company.Email;
                existingCompany.Website = company.Website;
                existingCompany.ContactPerson = company.ContactPerson;
                existingCompany.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated company with ID {CompanyId}", id);
                return existingCompany;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating company with ID {CompanyId}", id);
                throw;
            }
        }

        public async Task<bool> DeleteCompanyAsync(int id)
        {
            try
            {
                var company = await _context.Companies.FindAsync(id);
                if (company == null || !company.IsActive)
                {
                    return false;
                }

                // Soft delete
                company.IsActive = false;
                company.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted company with ID {CompanyId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting company with ID {CompanyId}", id);
                throw;
            }
        }

        public async Task<bool> CompanyExistsAsync(int id)
        {
            try
            {
                return await _context.Companies
                    .AnyAsync(c => c.Id == id && c.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if company exists with ID {CompanyId}", id);
                throw;
            }
        }

        public async Task<bool> BulstatExistsAsync(string bulstatNumber, int? excludeId = null)
        {
            try
            {
                var query = _context.Companies
                    .Where(c => c.BulstatNumber == bulstatNumber && c.IsActive);

                if (excludeId.HasValue)
                {
                    query = query.Where(c => c.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if Bulstat exists {BulstatNumber}", bulstatNumber);
                throw;
            }
        }
    }
}
