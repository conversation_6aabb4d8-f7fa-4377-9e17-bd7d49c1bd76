using Microsoft.EntityFrameworkCore;
using invoicer_ws.Data;
using invoicer_ws.Models;
using invoicer_ws.Services.Interfaces;

namespace invoicer_ws.Services
{
    public class InvoiceItemService : IInvoiceItemService
    {
        private readonly InvoicerDbContext _context;
        private readonly ILogger<InvoiceItemService> _logger;

        public InvoiceItemService(InvoicerDbContext context, ILogger<InvoiceItemService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<InvoiceItem>> GetInvoiceItemsByInvoiceAsync(int invoiceId)
        {
            try
            {
                return await _context.InvoiceItems
                    .Where(item => item.InvoiceId == invoiceId)
                    .OrderBy(item => item.SortOrder)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoice items for invoice {InvoiceId}", invoiceId);
                throw;
            }
        }

        public async Task<InvoiceItem?> GetInvoiceItemByIdAsync(int id)
        {
            try
            {
                return await _context.InvoiceItems
                    .Include(item => item.Invoice)
                    .FirstOrDefaultAsync(item => item.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoice item with ID {InvoiceItemId}", id);
                throw;
            }
        }

        public async Task<InvoiceItem> CreateInvoiceItemAsync(InvoiceItem invoiceItem)
        {
            try
            {
                // Validate that the invoice exists
                var invoiceExists = await _context.Invoices.AnyAsync(i => i.Id == invoiceItem.InvoiceId);
                if (!invoiceExists)
                {
                    throw new InvalidOperationException($"Invoice with ID {invoiceItem.InvoiceId} does not exist");
                }

                // Calculate line totals
                invoiceItem.CalculateLineTotals();
                invoiceItem.CreatedAt = DateTime.UtcNow;
                invoiceItem.UpdatedAt = DateTime.UtcNow;

                _context.InvoiceItems.Add(invoiceItem);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created invoice item with ID {InvoiceItemId} for invoice {InvoiceId}", 
                    invoiceItem.Id, invoiceItem.InvoiceId);

                return invoiceItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice item");
                throw;
            }
        }

        public async Task<InvoiceItem?> UpdateInvoiceItemAsync(int id, InvoiceItem invoiceItem)
        {
            try
            {
                var existingItem = await _context.InvoiceItems.FindAsync(id);
                if (existingItem == null)
                {
                    return null;
                }

                // Update properties
                existingItem.Description = invoiceItem.Description;
                existingItem.Quantity = invoiceItem.Quantity;
                existingItem.Unit = invoiceItem.Unit;
                existingItem.UnitPrice = invoiceItem.UnitPrice;
                existingItem.VatRate = invoiceItem.VatRate;
                existingItem.SortOrder = invoiceItem.SortOrder;
                existingItem.UpdatedAt = DateTime.UtcNow;

                // Recalculate line totals
                existingItem.CalculateLineTotals();

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated invoice item with ID {InvoiceItemId}", id);
                return existingItem;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice item with ID {InvoiceItemId}", id);
                throw;
            }
        }

        public async Task<bool> DeleteInvoiceItemAsync(int id)
        {
            try
            {
                var invoiceItem = await _context.InvoiceItems.FindAsync(id);
                if (invoiceItem == null)
                {
                    return false;
                }

                _context.InvoiceItems.Remove(invoiceItem);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted invoice item with ID {InvoiceItemId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice item with ID {InvoiceItemId}", id);
                throw;
            }
        }

        public async Task<bool> InvoiceItemExistsAsync(int id)
        {
            try
            {
                return await _context.InvoiceItems.AnyAsync(item => item.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if invoice item exists with ID {InvoiceItemId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<InvoiceItem>> CreateMultipleInvoiceItemsAsync(IEnumerable<InvoiceItem> invoiceItems)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var itemsList = invoiceItems.ToList();
                
                foreach (var item in itemsList)
                {
                    // Calculate line totals
                    item.CalculateLineTotals();
                    item.CreatedAt = DateTime.UtcNow;
                    item.UpdatedAt = DateTime.UtcNow;
                }

                _context.InvoiceItems.AddRange(itemsList);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("Created {Count} invoice items", itemsList.Count);
                return itemsList;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error creating multiple invoice items");
                throw;
            }
        }

        public async Task<bool> DeleteAllInvoiceItemsAsync(int invoiceId)
        {
            try
            {
                var items = await _context.InvoiceItems
                    .Where(item => item.InvoiceId == invoiceId)
                    .ToListAsync();

                if (!items.Any())
                {
                    return true; // No items to delete, consider it successful
                }

                _context.InvoiceItems.RemoveRange(items);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted all invoice items for invoice {InvoiceId}", invoiceId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting all invoice items for invoice {InvoiceId}", invoiceId);
                throw;
            }
        }

        public async Task<decimal> CalculateInvoiceSubTotalAsync(int invoiceId)
        {
            try
            {
                return await _context.InvoiceItems
                    .Where(item => item.InvoiceId == invoiceId)
                    .SumAsync(item => item.LineTotal);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating subtotal for invoice {InvoiceId}", invoiceId);
                throw;
            }
        }

        public async Task<decimal> CalculateInvoiceVatAmountAsync(int invoiceId)
        {
            try
            {
                return await _context.InvoiceItems
                    .Where(item => item.InvoiceId == invoiceId)
                    .SumAsync(item => item.VatAmount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating VAT amount for invoice {InvoiceId}", invoiceId);
                throw;
            }
        }

        public async Task<decimal> CalculateInvoiceTotalAmountAsync(int invoiceId)
        {
            try
            {
                return await _context.InvoiceItems
                    .Where(item => item.InvoiceId == invoiceId)
                    .SumAsync(item => item.LineTotalWithVat);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating total amount for invoice {InvoiceId}", invoiceId);
                throw;
            }
        }
    }
}
