using Microsoft.EntityFrameworkCore;
using invoicer_ws.Data;
using invoicer_ws.Models;
using invoicer_ws.Services.Interfaces;

namespace invoicer_ws.Services
{
    public class InvoiceService : IInvoiceService
    {
        private readonly InvoicerDbContext _context;
        private readonly ILogger<InvoiceService> _logger;

        public InvoiceService(InvoicerDbContext context, ILogger<InvoiceService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<Invoice>> GetAllInvoicesAsync()
        {
            try
            {
                return await _context.Invoices
                    .Include(i => i.Company)
                    .Include(i => i.Customer)
                    .Include(i => i.InvoiceItems)
                    .OrderByDescending(i => i.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all invoices");
                throw;
            }
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByCompanyAsync(int companyId)
        {
            try
            {
                return await _context.Invoices
                    .Include(i => i.Company)
                    .Include(i => i.Customer)
                    .Include(i => i.InvoiceItems)
                    .Where(i => i.CompanyId == companyId)
                    .OrderByDescending(i => i.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoices for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByCustomerAsync(int customerId)
        {
            try
            {
                return await _context.Invoices
                    .Include(i => i.Company)
                    .Include(i => i.Customer)
                    .Include(i => i.InvoiceItems)
                    .Where(i => i.CustomerId == customerId)
                    .OrderByDescending(i => i.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoices for customer {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<IEnumerable<Invoice>> GetInvoicesByStatusAsync(InvoiceStatus status)
        {
            try
            {
                return await _context.Invoices
                    .Include(i => i.Company)
                    .Include(i => i.Customer)
                    .Include(i => i.InvoiceItems)
                    .Where(i => i.Status == status)
                    .OrderByDescending(i => i.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoices with status {Status}", status);
                throw;
            }
        }

        public async Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync()
        {
            try
            {
                var today = DateTime.UtcNow.Date;
                return await _context.Invoices
                    .Include(i => i.Company)
                    .Include(i => i.Customer)
                    .Include(i => i.InvoiceItems)
                    .Where(i => i.DueDate.Date < today && i.Status != InvoiceStatus.Paid && i.Status != InvoiceStatus.Cancelled)
                    .OrderByDescending(i => i.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving overdue invoices");
                throw;
            }
        }

        public async Task<Invoice?> GetInvoiceByIdAsync(int id)
        {
            try
            {
                return await _context.Invoices
                    .Include(i => i.Company)
                    .Include(i => i.Customer)
                    .Include(i => i.InvoiceItems.OrderBy(item => item.SortOrder))
                    .FirstOrDefaultAsync(i => i.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoice with ID {InvoiceId}", id);
                throw;
            }
        }

        public async Task<Invoice?> GetInvoiceByNumberAsync(string invoiceNumber)
        {
            try
            {
                return await _context.Invoices
                    .Include(i => i.Company)
                    .Include(i => i.Customer)
                    .Include(i => i.InvoiceItems.OrderBy(item => item.SortOrder))
                    .FirstOrDefaultAsync(i => i.InvoiceNumber == invoiceNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoice with number {InvoiceNumber}", invoiceNumber);
                throw;
            }
        }

        public async Task<Invoice> CreateInvoiceAsync(Invoice invoice)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Check if invoice number already exists
                if (await InvoiceNumberExistsAsync(invoice.InvoiceNumber))
                {
                    throw new InvalidOperationException($"Invoice with number {invoice.InvoiceNumber} already exists");
                }

                // Validate company and customer exist
                var companyExists = await _context.Companies.AnyAsync(c => c.Id == invoice.CompanyId && c.IsActive);
                if (!companyExists)
                {
                    throw new InvalidOperationException($"Company with ID {invoice.CompanyId} does not exist or is inactive");
                }

                var customerExists = await _context.Customers.AnyAsync(c => c.Id == invoice.CustomerId && c.IsActive);
                if (!customerExists)
                {
                    throw new InvalidOperationException($"Customer with ID {invoice.CustomerId} does not exist or is inactive");
                }

                // Set timestamps
                invoice.CreatedAt = DateTime.UtcNow;
                invoice.UpdatedAt = DateTime.UtcNow;

                // Calculate line totals for each invoice item
                foreach (var item in invoice.InvoiceItems)
                {
                    item.CalculateLineTotals();
                    item.CreatedAt = DateTime.UtcNow;
                    item.UpdatedAt = DateTime.UtcNow;
                }

                // Calculate invoice totals
                invoice.CalculateTotals();

                _context.Invoices.Add(invoice);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();

                _logger.LogInformation("Created invoice with ID {InvoiceId} and number {InvoiceNumber}", invoice.Id, invoice.InvoiceNumber);

                // Return the invoice with all related data
                return await GetInvoiceByIdAsync(invoice.Id) ?? invoice;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error creating invoice");
                throw;
            }
        }

        public async Task<Invoice?> UpdateInvoiceAsync(int id, Invoice invoice)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var existingInvoice = await _context.Invoices
                    .Include(i => i.InvoiceItems)
                    .FirstOrDefaultAsync(i => i.Id == id);

                if (existingInvoice == null)
                {
                    return null;
                }

                // Check if invoice number already exists (excluding current invoice)
                if (await InvoiceNumberExistsAsync(invoice.InvoiceNumber, id))
                {
                    throw new InvalidOperationException($"Invoice with number {invoice.InvoiceNumber} already exists");
                }

                // Update invoice properties
                existingInvoice.InvoiceNumber = invoice.InvoiceNumber;
                existingInvoice.CompanyId = invoice.CompanyId;
                existingInvoice.CustomerId = invoice.CustomerId;
                existingInvoice.InvoiceDate = invoice.InvoiceDate;
                existingInvoice.DueDate = invoice.DueDate;
                existingInvoice.Status = invoice.Status;
                existingInvoice.Notes = invoice.Notes;
                existingInvoice.Terms = invoice.Terms;
                existingInvoice.Currency = invoice.Currency;
                existingInvoice.UpdatedAt = DateTime.UtcNow;

                // Remove existing invoice items
                _context.InvoiceItems.RemoveRange(existingInvoice.InvoiceItems);

                // Add new invoice items
                foreach (var item in invoice.InvoiceItems)
                {
                    item.InvoiceId = existingInvoice.Id;
                    item.CalculateLineTotals();
                    item.CreatedAt = DateTime.UtcNow;
                    item.UpdatedAt = DateTime.UtcNow;
                    existingInvoice.InvoiceItems.Add(item);
                }

                // Recalculate totals
                existingInvoice.CalculateTotals();

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("Updated invoice with ID {InvoiceId}", id);

                return await GetInvoiceByIdAsync(id);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error updating invoice with ID {InvoiceId}", id);
                throw;
            }
        }

        public async Task<bool> DeleteInvoiceAsync(int id)
        {
            try
            {
                var invoice = await _context.Invoices
                    .Include(i => i.InvoiceItems)
                    .FirstOrDefaultAsync(i => i.Id == id);

                if (invoice == null)
                {
                    return false;
                }

                _context.InvoiceItems.RemoveRange(invoice.InvoiceItems);
                _context.Invoices.Remove(invoice);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted invoice with ID {InvoiceId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice with ID {InvoiceId}", id);
                throw;
            }
        }

        public async Task<bool> InvoiceExistsAsync(int id)
        {
            try
            {
                return await _context.Invoices.AnyAsync(i => i.Id == id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if invoice exists with ID {InvoiceId}", id);
                throw;
            }
        }

        public async Task<bool> InvoiceNumberExistsAsync(string invoiceNumber, int? excludeId = null)
        {
            try
            {
                var query = _context.Invoices.Where(i => i.InvoiceNumber == invoiceNumber);
                if (excludeId.HasValue)
                {
                    query = query.Where(i => i.Id != excludeId.Value);
                }
                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if invoice number exists: {InvoiceNumber}", invoiceNumber);
                throw;
            }
        }

        public async Task<string> GenerateInvoiceNumberAsync(int companyId)
        {
            try
            {
                var year = DateTime.UtcNow.Year;
                var company = await _context.Companies.FindAsync(companyId);
                var companyPrefix = company?.Name.Substring(0, Math.Min(3, company.Name.Length)).ToUpper() ?? "INV";

                var lastInvoice = await _context.Invoices
                    .Where(i => i.CompanyId == companyId && i.InvoiceNumber.StartsWith($"{companyPrefix}-{year}-"))
                    .OrderByDescending(i => i.InvoiceNumber)
                    .FirstOrDefaultAsync();

                int nextNumber = 1;
                if (lastInvoice != null)
                {
                    var parts = lastInvoice.InvoiceNumber.Split('-');
                    if (parts.Length >= 3 && int.TryParse(parts[2], out int lastNumber))
                    {
                        nextNumber = lastNumber + 1;
                    }
                }

                return $"{companyPrefix}-{year}-{nextNumber:D3}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating invoice number for company {CompanyId}", companyId);
                throw;
            }
        }

        public async Task<Invoice?> UpdateInvoiceStatusAsync(int id, InvoiceStatus status)
        {
            try
            {
                var invoice = await _context.Invoices.FindAsync(id);
                if (invoice == null)
                {
                    return null;
                }

                invoice.Status = status;
                invoice.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Updated invoice status for ID {InvoiceId} to {Status}", id, status);
                return await GetInvoiceByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice status for ID {InvoiceId}", id);
                throw;
            }
        }

        public async Task<Invoice?> CalculateInvoiceTotalsAsync(int id)
        {
            try
            {
                var invoice = await _context.Invoices
                    .Include(i => i.InvoiceItems)
                    .FirstOrDefaultAsync(i => i.Id == id);

                if (invoice == null)
                {
                    return null;
                }

                invoice.CalculateTotals();
                invoice.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Recalculated totals for invoice ID {InvoiceId}", id);
                return invoice;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating totals for invoice ID {InvoiceId}", id);
                throw;
            }
        }
    }
}
