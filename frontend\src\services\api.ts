import { Company, CreateCompanyRequest, Customer, CreateCustomerRequest, Invoice, CreateInvoiceRequest, InvoiceStatus, ApiError } from "../types";

const API_BASE_URL = "api";

class ApiService {
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error: ApiError = {
          message: errorData.message || `HTTP error! status: ${response.status}`,
          errors: errorData.errors || [],
        };
        throw error;
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw {
          message: error.message,
          errors: [],
        } as ApiError;
      }
      throw error;
    }
  }

  // Company API methods
  async getCompanies(): Promise<Company[]> {
    return this.request<Company[]>("/companies");
  }

  async getCompany(id: number): Promise<Company> {
    return this.request<Company>(`/companies/${id}`);
  }

  async createCompany(company: CreateCompanyRequest): Promise<Company> {
    return this.request<Company>("/companies", {
      method: "POST",
      body: JSON.stringify(company),
    });
  }

  async updateCompany(id: number, company: CreateCompanyRequest): Promise<Company> {
    return this.request<Company>(`/companies/${id}`, {
      method: "PUT",
      body: JSON.stringify(company),
    });
  }

  async deleteCompany(id: number): Promise<void> {
    return this.request<void>(`/companies/${id}`, {
      method: "DELETE",
    });
  }

  async checkBulstatExists(bulstatNumber: string, excludeId?: number): Promise<{ exists: boolean }> {
    const query = excludeId ? `?excludeId=${excludeId}` : "";
    return this.request<{ exists: boolean }>(`/companies/bulstat/${bulstatNumber}/exists${query}`);
  }

  // Customer API methods
  async getCustomers(): Promise<Customer[]> {
    return this.request<Customer[]>("/customers");
  }

  async getCustomer(id: number): Promise<Customer> {
    return this.request<Customer>(`/customers/${id}`);
  }

  async createCustomer(customer: CreateCustomerRequest): Promise<Customer> {
    return this.request<Customer>("/customers", {
      method: "POST",
      body: JSON.stringify(customer),
    });
  }

  async updateCustomer(id: number, customer: CreateCustomerRequest): Promise<Customer> {
    return this.request<Customer>(`/customers/${id}`, {
      method: "PUT",
      body: JSON.stringify(customer),
    });
  }

  async deleteCustomer(id: number): Promise<void> {
    return this.request<void>(`/customers/${id}`, {
      method: "DELETE",
    });
  }

  async checkCustomerBulstatExists(bulstatNumber: string, excludeId?: number): Promise<{ exists: boolean }> {
    const query = excludeId ? `?excludeId=${excludeId}` : "";
    return this.request<{ exists: boolean }>(`/customers/bulstat/${bulstatNumber}/exists${query}`);
  }

  async checkPersonalIdExists(personalId: string, excludeId?: number): Promise<{ exists: boolean }> {
    const query = excludeId ? `?excludeId=${excludeId}` : "";
    return this.request<{ exists: boolean }>(`/customers/personal-id/${personalId}/exists${query}`);
  }

  // Invoice API methods
  async getInvoices(): Promise<Invoice[]> {
    return this.request<Invoice[]>("/invoices");
  }

  async getInvoice(id: number): Promise<Invoice> {
    return this.request<Invoice>(`/invoices/${id}`);
  }

  async getInvoiceByNumber(invoiceNumber: string): Promise<Invoice> {
    return this.request<Invoice>(`/invoices/number/${invoiceNumber}`);
  }

  async getInvoicesByCompany(companyId: number): Promise<Invoice[]> {
    return this.request<Invoice[]>(`/invoices/company/${companyId}`);
  }

  async getInvoicesByCustomer(customerId: number): Promise<Invoice[]> {
    return this.request<Invoice[]>(`/invoices/customer/${customerId}`);
  }

  async getInvoicesByStatus(status: InvoiceStatus): Promise<Invoice[]> {
    return this.request<Invoice[]>(`/invoices/status/${status}`);
  }

  async getOverdueInvoices(): Promise<Invoice[]> {
    return this.request<Invoice[]>("/invoices/overdue");
  }

  async createInvoice(invoice: CreateInvoiceRequest): Promise<Invoice> {
    return this.request<Invoice>("/invoices", {
      method: "POST",
      body: JSON.stringify(invoice),
    });
  }

  async updateInvoice(id: number, invoice: CreateInvoiceRequest): Promise<Invoice> {
    return this.request<Invoice>(`/invoices/${id}`, {
      method: "PUT",
      body: JSON.stringify(invoice),
    });
  }

  async deleteInvoice(id: number): Promise<void> {
    return this.request<void>(`/invoices/${id}`, {
      method: "DELETE",
    });
  }

  async checkInvoiceNumberExists(invoiceNumber: string, excludeId?: number): Promise<{ exists: boolean }> {
    const query = excludeId ? `?excludeId=${excludeId}` : "";
    return this.request<{ exists: boolean }>(`/invoices/number/${invoiceNumber}/exists${query}`);
  }
}

export const apiService = new ApiService();
