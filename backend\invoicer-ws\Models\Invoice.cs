using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace invoicer_ws.Models
{
    /// <summary>
    /// Represents an invoice
    /// </summary>
    public class Invoice
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "Invoice number is required")]
        [StringLength(50, ErrorMessage = "Invoice number cannot exceed 50 characters")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Company is required")]
        public int CompanyId { get; set; }

        [Required(ErrorMessage = "Customer is required")]
        public int CustomerId { get; set; }

        [Required(ErrorMessage = "Invoice date is required")]
        public DateTime InvoiceDate { get; set; } = DateTime.UtcNow;

        [Required(ErrorMessage = "Due date is required")]
        public DateTime DueDate { get; set; }

        public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;

        [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
        public string? Notes { get; set; }

        [StringLength(1000, ErrorMessage = "Terms cannot exceed 1000 characters")]
        public string? Terms { get; set; }

        // Calculated fields
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal VatAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        [StringLength(3, ErrorMessage = "Currency code cannot exceed 3 characters")]
        public string Currency { get; set; } = "BGN";

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("CompanyId")]
        public virtual Company Company { get; set; } = null!;

        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; } = null!;

        public virtual ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();

        /// <summary>
        /// Calculates the totals based on invoice items
        /// </summary>
        public void CalculateTotals()
        {
            SubTotal = InvoiceItems.Sum(item => item.LineTotal);
            VatAmount = InvoiceItems.Sum(item => item.VatAmount);
            TotalAmount = SubTotal + VatAmount;
        }

        /// <summary>
        /// Checks if the invoice is overdue
        /// </summary>
        public bool IsOverdue => Status != InvoiceStatus.Paid && 
                                Status != InvoiceStatus.Cancelled && 
                                DueDate < DateTime.UtcNow;
    }
}
