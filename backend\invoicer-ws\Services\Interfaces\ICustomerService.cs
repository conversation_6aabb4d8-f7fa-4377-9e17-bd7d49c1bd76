using invoicer_ws.Models;

namespace invoicer_ws.Services.Interfaces
{
    public interface ICustomerService
    {
        Task<IEnumerable<Customer>> GetAllCustomersAsync();
        Task<IEnumerable<Customer>> GetCustomersByTypeAsync(CustomerType customerType);
        Task<Customer?> GetCustomerByIdAsync(int id);
        Task<Customer?> GetCustomerByBulstatAsync(string bulstatNumber);
        Task<Customer?> GetCustomerByPersonalIdAsync(string personalId);
        Task<Customer> CreateCustomerAsync(Customer customer);
        Task<Customer?> UpdateCustomerAsync(int id, Customer customer);
        Task<bool> DeleteCustomerAsync(int id);
        Task<bool> CustomerExistsAsync(int id);
        Task<bool> BulstatExistsAsync(string bulstatNumber, int? excludeId = null);
        Task<bool> PersonalIdExistsAsync(string personalId, int? excludeId = null);
        Task<bool> ValidateCustomerAsync(Customer customer);
    }
}
