import { useState } from "react";
import { CreateCustomerForm } from "@/components/forms/CreateCustomerForm";
import { Customer, CustomerType } from "@/types";

export function CreateCustomerPage() {
  const [createdCustomer, setCreatedCustomer] = useState<Customer | null>(null);

  const handleSuccess = (customer: Customer) => {
    setCreatedCustomer(customer);
    // You could also navigate to a different page here
    // For example: navigate('/customers')
  };

  const handleCreateAnother = () => {
    setCreatedCustomer(null);
  };

  if (createdCustomer) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-lg mb-6">
            <h2 className="text-lg font-semibold mb-2">✅ Customer Created Successfully!</h2>
            <p className="mb-4">
              <strong>{createdCustomer.name}</strong> has been added to your system.
            </p>
            <div className="space-y-1 text-sm">
              <p>
                <strong>Type:</strong> {createdCustomer.customerType === CustomerType.Business ? "Business Customer" : "Residential Customer"}
              </p>
              {createdCustomer.bulstatNumber && (
                <p>
                  <strong>ЕИК/Булстат:</strong> {createdCustomer.bulstatNumber}
                </p>
              )}
              {createdCustomer.personalId && (
                <p>
                  <strong>Personal ID (ЕГН):</strong> {createdCustomer.personalId}
                </p>
              )}
              {createdCustomer.vatNumber && (
                <p>
                  <strong>VAT Number:</strong> {createdCustomer.vatNumber}
                </p>
              )}
              <p>
                <strong>Address:</strong> {createdCustomer.address}
              </p>
              {createdCustomer.city && (
                <p>
                  <strong>City:</strong> {createdCustomer.city}, {createdCustomer.country}
                </p>
              )}
              {createdCustomer.phone && (
                <p>
                  <strong>Phone:</strong> {createdCustomer.phone}
                </p>
              )}
              {createdCustomer.email && (
                <p>
                  <strong>Email:</strong> {createdCustomer.email}
                </p>
              )}
              {createdCustomer.contactPerson && (
                <p>
                  <strong>Contact Person:</strong> {createdCustomer.contactPerson}
                </p>
              )}
            </div>
          </div>

          <div className="flex gap-4 justify-center">
            <button onClick={handleCreateAnother} className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors">
              Create Another Customer
            </button>
            <button
              onClick={() => {
                // Navigate to customers list or dashboard
                console.log("Navigate to customers list");
              }}
              className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md transition-colors"
            >
              View All Customers
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Create New Customer</h1>
        <p className="text-gray-600">Add a new customer to your invoicing system. Choose between residential and business customers.</p>
      </div>

      <CreateCustomerForm
        onSuccess={handleSuccess}
        onCancel={() => {
          // Navigate back or handle cancel
          console.log("Cancel customer creation");
        }}
      />
    </div>
  );
}
