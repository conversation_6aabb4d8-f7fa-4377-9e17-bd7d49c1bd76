import { Button } from '@/components/ui/button';

interface NavigationProps {
  currentPage: 'home' | 'create-company' | 'create-customer';
  onNavigate: (page: 'home' | 'create-company' | 'create-customer') => void;
}

export function Navigation({ currentPage, onNavigate }: NavigationProps) {
  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold text-gray-900">Invoicer</h1>
            <span className="text-sm text-gray-500">Bulgarian Invoicing System</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button
              variant={currentPage === 'home' ? 'default' : 'ghost'}
              onClick={() => onNavigate('home')}
            >
              Home
            </Button>
            <Button
              variant={currentPage === 'create-company' ? 'default' : 'ghost'}
              onClick={() => onNavigate('create-company')}
            >
              Create Company
            </Button>
            <Button
              variant={currentPage === 'create-customer' ? 'default' : 'ghost'}
              onClick={() => onNavigate('create-customer')}
            >
              Create Customer
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
}
