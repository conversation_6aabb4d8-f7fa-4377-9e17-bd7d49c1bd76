using System.ComponentModel.DataAnnotations;
using invoicer_ws.Models.Validation;

namespace invoicer_ws.Models
{
    /// <summary>
    /// Represents a customer (both residential and business)
    /// </summary>
    [CustomerValidation]
    public class Customer
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "Customer type is required")]
        public CustomerType CustomerType { get; set; }

        [Required(ErrorMessage = "Customer name is required")]
        [StringLength(200, ErrorMessage = "Customer name cannot exceed 200 characters")]
        public string Name { get; set; } = string.Empty;

        // For business customers - ЕИК/Булстат number
        [StringLength(13, MinimumLength = 9, ErrorMessage = "ЕИК/Булстат must be between 9 and 13 characters")]
        [RegularExpression(@"^\d{9,13}$", ErrorMessage = "ЕИК/Булстат must contain only digits")]
        public string? BulstatNumber { get; set; }

        // For business customers - VAT number
        [StringLength(20, ErrorMessage = "VAT number cannot exceed 20 characters")]
        public string? VatNumber { get; set; }

        // For residential customers - Personal ID (ЕГН)
        [StringLength(10, MinimumLength = 10, ErrorMessage = "Personal ID must be exactly 10 characters")]
        [RegularExpression(@"^\d{10}$", ErrorMessage = "Personal ID must contain only digits")]
        public string? PersonalId { get; set; }

        [Required(ErrorMessage = "Address is required")]
        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string Address { get; set; } = string.Empty;

        [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
        public string? City { get; set; }

        [StringLength(10, ErrorMessage = "Postal code cannot exceed 10 characters")]
        public string? PostalCode { get; set; }

        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        public string Country { get; set; } = "Bulgaria";

        [Phone(ErrorMessage = "Invalid phone number format")]
        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? Phone { get; set; }

        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        public string? Email { get; set; }

        [StringLength(100, ErrorMessage = "Contact person name cannot exceed 100 characters")]
        public string? ContactPerson { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();

        /// <summary>
        /// Validates that business customers have Bulstat number and residential customers have Personal ID
        /// </summary>
        public bool IsValid()
        {
            return CustomerType switch
            {
                CustomerType.Business => !string.IsNullOrEmpty(BulstatNumber),
                CustomerType.Residential => !string.IsNullOrEmpty(PersonalId),
                _ => false
            };
        }
    }
}
