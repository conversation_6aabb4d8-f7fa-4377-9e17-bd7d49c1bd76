using Microsoft.AspNetCore.Mvc;
using invoicer_ws.Models;
using invoicer_ws.Services.Interfaces;

namespace invoicer_ws.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CustomersController : ControllerBase
    {
        private readonly ICustomerService _customerService;
        private readonly ILogger<CustomersController> _logger;

        public CustomersController(ICustomerService customerService, ILogger<CustomersController> logger)
        {
            _customerService = customerService;
            _logger = logger;
        }

        /// <summary>
        /// Get all customers
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Customer>>> GetCustomers([FromQuery] CustomerType? type = null)
        {
            try
            {
                var customers = type.HasValue 
                    ? await _customerService.GetCustomersByTypeAsync(type.Value)
                    : await _customerService.GetAllCustomersAsync();
                
                return Ok(customers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving customers");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get customer by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<Customer>> GetCustomer(int id)
        {
            try
            {
                var customer = await _customerService.GetCustomerByIdAsync(id);
                if (customer == null)
                {
                    return NotFound($"Customer with ID {id} not found");
                }

                return Ok(customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving customer with ID {CustomerId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get customer by Bulstat number
        /// </summary>
        [HttpGet("bulstat/{bulstatNumber}")]
        public async Task<ActionResult<Customer>> GetCustomerByBulstat(string bulstatNumber)
        {
            try
            {
                var customer = await _customerService.GetCustomerByBulstatAsync(bulstatNumber);
                if (customer == null)
                {
                    return NotFound($"Customer with Bulstat {bulstatNumber} not found");
                }

                return Ok(customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving customer with Bulstat {BulstatNumber}", bulstatNumber);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get customer by Personal ID
        /// </summary>
        [HttpGet("personal-id/{personalId}")]
        public async Task<ActionResult<Customer>> GetCustomerByPersonalId(string personalId)
        {
            try
            {
                var customer = await _customerService.GetCustomerByPersonalIdAsync(personalId);
                if (customer == null)
                {
                    return NotFound($"Customer with Personal ID {personalId} not found");
                }

                return Ok(customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving customer with Personal ID {PersonalId}", personalId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Create a new customer
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<Customer>> CreateCustomer(Customer customer)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var createdCustomer = await _customerService.CreateCustomerAsync(customer);
                return CreatedAtAction(nameof(GetCustomer), new { id = createdCustomer.Id }, createdCustomer);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation when creating customer");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating customer");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update an existing customer
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<Customer>> UpdateCustomer(int id, Customer customer)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var updatedCustomer = await _customerService.UpdateCustomerAsync(id, customer);
                if (updatedCustomer == null)
                {
                    return NotFound($"Customer with ID {id} not found");
                }

                return Ok(updatedCustomer);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Invalid operation when updating customer with ID {CustomerId}", id);
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer with ID {CustomerId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Delete a customer (soft delete)
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCustomer(int id)
        {
            try
            {
                var result = await _customerService.DeleteCustomerAsync(id);
                if (!result)
                {
                    return NotFound($"Customer with ID {id} not found");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting customer with ID {CustomerId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if customer exists
        /// </summary>
        [HttpHead("{id}")]
        public async Task<IActionResult> CustomerExists(int id)
        {
            try
            {
                var exists = await _customerService.CustomerExistsAsync(id);
                return exists ? Ok() : NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if customer exists with ID {CustomerId}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if Bulstat number exists
        /// </summary>
        [HttpGet("bulstat/{bulstatNumber}/exists")]
        public async Task<ActionResult<bool>> BulstatExists(string bulstatNumber, [FromQuery] int? excludeId = null)
        {
            try
            {
                var exists = await _customerService.BulstatExistsAsync(bulstatNumber, excludeId);
                return Ok(new { exists });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if Bulstat exists {BulstatNumber}", bulstatNumber);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Check if Personal ID exists
        /// </summary>
        [HttpGet("personal-id/{personalId}/exists")]
        public async Task<ActionResult<bool>> PersonalIdExists(string personalId, [FromQuery] int? excludeId = null)
        {
            try
            {
                var exists = await _customerService.PersonalIdExistsAsync(personalId, excludeId);
                return Ok(new { exists });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if Personal ID exists {PersonalId}", personalId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Validate customer data
        /// </summary>
        [HttpPost("validate")]
        public async Task<ActionResult<bool>> ValidateCustomer(Customer customer)
        {
            try
            {
                var isValid = await _customerService.ValidateCustomerAsync(customer);
                return Ok(new { isValid });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating customer");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
