namespace invoicer_ws.Models
{
    /// <summary>
    /// Defines the status of an invoice
    /// </summary>
    public enum InvoiceStatus
    {
        /// <summary>
        /// Invoice is in draft state
        /// </summary>
        Draft = 1,

        /// <summary>
        /// Invoice has been sent to customer
        /// </summary>
        Sent = 2,

        /// <summary>
        /// Invoice has been paid
        /// </summary>
        Paid = 3,

        /// <summary>
        /// Invoice is overdue
        /// </summary>
        Overdue = 4,

        /// <summary>
        /// Invoice has been cancelled
        /// </summary>
        Cancelled = 5
    }
}
