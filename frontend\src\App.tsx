import { useState } from "react";
import { Navigation } from "@/components/Navigation";
import { HomePage } from "@/pages/HomePage";
import { CreateCompanyPage } from "@/pages/CreateCompanyPage";
import { CreateCustomerPage } from "@/pages/CreateCustomerPage";

type Page = "home" | "create-company" | "create-customer";

function App() {
  const [currentPage, setCurrentPage] = useState<Page>("home");

  const handleNavigate = (page: Page) => {
    setCurrentPage(page);
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case "home":
        return <HomePage onNavigate={handleNavigate} />;
      case "create-company":
        return <CreateCompanyPage />;
      case "create-customer":
        return <CreateCustomerPage />;
      default:
        return <HomePage onNavigate={handleNavigate} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation currentPage={currentPage} onNavigate={handleNavigate} />
      <main>{renderCurrentPage()}</main>
    </div>
  );
}

export default App;
