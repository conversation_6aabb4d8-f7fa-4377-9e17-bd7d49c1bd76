import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface HomePageProps {
  onNavigate: (page: 'create-company' | 'create-customer') => void;
}

export function HomePage({ onNavigate }: HomePageProps) {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to Invoicer
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            Bulgarian Invoicing System
          </p>
          <p className="text-gray-500">
            Manage your companies, customers, and invoices with Bulgarian business compliance
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => onNavigate('create-company')}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🏢 Create Company
              </CardTitle>
              <CardDescription>
                Add a new company (доставчик на услугите/продуктите) to your system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Register companies with Bulgarian ЕИК/Булстат numbers, VAT information, and contact details.
              </p>
              <Button className="w-full">
                Create New Company
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => onNavigate('create-customer')}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                👥 Create Customer
              </CardTitle>
              <CardDescription>
                Add residential or business customers to your system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Register customers with proper Bulgarian identification - ЕИК/Булстат for businesses, ЕГН for individuals.
              </p>
              <Button className="w-full">
                Create New Customer
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">
            🇧🇬 Bulgarian Business Compliance
          </h2>
          <div className="text-sm text-blue-800 space-y-2">
            <p>
              <strong>Companies:</strong> Must have valid ЕИК/Булстат identification numbers (9-13 digits)
            </p>
            <p>
              <strong>Business Customers:</strong> Require ЕИК/Булстат numbers for proper invoicing
            </p>
            <p>
              <strong>Residential Customers:</strong> Use ЕГН (Personal ID) numbers for identification
            </p>
            <p>
              <strong>VAT:</strong> Default Bulgarian VAT rate of 20% applied to invoices
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
