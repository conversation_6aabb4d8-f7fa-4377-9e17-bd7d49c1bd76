using invoicer_ws.Models;

namespace invoicer_ws.Services.Interfaces
{
    public interface IInvoiceService
    {
        Task<IEnumerable<Invoice>> GetAllInvoicesAsync();
        Task<IEnumerable<Invoice>> GetInvoicesByCompanyAsync(int companyId);
        Task<IEnumerable<Invoice>> GetInvoicesByCustomerAsync(int customerId);
        Task<IEnumerable<Invoice>> GetInvoicesByStatusAsync(InvoiceStatus status);
        Task<IEnumerable<Invoice>> GetOverdueInvoicesAsync();
        Task<Invoice?> GetInvoiceByIdAsync(int id);
        Task<Invoice?> GetInvoiceByNumberAsync(string invoiceNumber);
        Task<Invoice> CreateInvoiceAsync(Invoice invoice);
        Task<Invoice?> UpdateInvoiceAsync(int id, Invoice invoice);
        Task<bool> DeleteInvoiceAsync(int id);
        Task<bool> InvoiceExistsAsync(int id);
        Task<bool> InvoiceNumberExistsAsync(string invoiceNumber, int? excludeId = null);
        Task<string> GenerateInvoiceNumberAsync(int companyId);
        Task<Invoice?> UpdateInvoiceStatusAsync(int id, InvoiceStatus status);
        Task<Invoice?> CalculateInvoiceTotalsAsync(int id);
    }
}
