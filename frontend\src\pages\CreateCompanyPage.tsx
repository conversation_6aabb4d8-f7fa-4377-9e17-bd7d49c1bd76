import { useState } from "react";
import { CreateCompanyForm } from "@/components/forms/CreateCompanyForm";
import { Company } from "@/types";

export function CreateCompanyPage() {
  const [createdCompany, setCreatedCompany] = useState<Company | null>(null);

  const handleSuccess = (company: Company) => {
    setCreatedCompany(company);
    // You could also navigate to a different page here
    // For example: navigate('/companies')
  };

  const handleCreateAnother = () => {
    setCreatedCompany(null);
  };

  if (createdCompany) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-lg mb-6">
            <h2 className="text-lg font-semibold mb-2">✅ Company Created Successfully!</h2>
            <p className="mb-4">
              <strong>{createdCompany.name}</strong> has been added to your system.
            </p>
            <div className="space-y-1 text-sm">
              <p>
                <strong>ЕИК/Булстат:</strong> {createdCompany.bulstatNumber}
              </p>
              {createdCompany.vatNumber && (
                <p>
                  <strong>VAT Number:</strong> {createdCompany.vatNumber}
                </p>
              )}
              <p>
                <strong>Address:</strong> {createdCompany.address}
              </p>
              {createdCompany.city && (
                <p>
                  <strong>City:</strong> {createdCompany.city}, {createdCompany.country}
                </p>
              )}
              {createdCompany.phone && (
                <p>
                  <strong>Phone:</strong> {createdCompany.phone}
                </p>
              )}
              {createdCompany.email && (
                <p>
                  <strong>Email:</strong> {createdCompany.email}
                </p>
              )}
            </div>
          </div>

          <div className="flex gap-4 justify-center">
            <button onClick={handleCreateAnother} className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors">
              Create Another Company
            </button>
            <button
              onClick={() => {
                // Navigate to companies list or dashboard
                console.log("Navigate to companies list");
              }}
              className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md transition-colors"
            >
              View All Companies
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Create New Company</h1>
        <p className="text-gray-600">Add a new company (доставчик на услугите/продуктите) to your invoicing system</p>
      </div>

      <CreateCompanyForm
        onSuccess={handleSuccess}
        onCancel={() => {
          // Navigate back or handle cancel
          console.log("Cancel company creation");
        }}
      />
    </div>
  );
}
