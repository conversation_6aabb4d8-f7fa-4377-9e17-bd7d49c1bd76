function Lx(a,l){for(var i=0;i<l.length;i++){const s=l[i];if(typeof s!="string"&&!Array.isArray(s)){for(const u in s)if(u!=="default"&&!(u in a)){const f=Object.getOwnPropertyDescriptor(s,u);f&&Object.defineProperty(a,u,f.get?f:{enumerable:!0,get:()=>s[u]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))s(u);new MutationObserver(u=>{for(const f of u)if(f.type==="childList")for(const d of f.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&s(d)}).observe(document,{childList:!0,subtree:!0});function i(u){const f={};return u.integrity&&(f.integrity=u.integrity),u.referrerPolicy&&(f.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?f.credentials="include":u.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function s(u){if(u.ep)return;u.ep=!0;const f=i(u);fetch(u.href,f)}})();function _g(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var tf={exports:{}},Ai={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ap;function Hx(){if(ap)return Ai;ap=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function i(s,u,f){var d=null;if(f!==void 0&&(d=""+f),u.key!==void 0&&(d=""+u.key),"key"in u){f={};for(var h in u)h!=="key"&&(f[h]=u[h])}else f=u;return u=f.ref,{$$typeof:a,type:s,key:d,ref:u!==void 0?u:null,props:f}}return Ai.Fragment=l,Ai.jsx=i,Ai.jsxs=i,Ai}var lp;function qx(){return lp||(lp=1,tf.exports=Hx()),tf.exports}var g=qx(),nf={exports:{}},De={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rp;function Zx(){if(rp)return De;rp=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),S=Symbol.iterator;function T(w){return w===null||typeof w!="object"?null:(w=S&&w[S]||w["@@iterator"],typeof w=="function"?w:null)}var z={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},L=Object.assign,_={};function N(w,K,ae){this.props=w,this.context=K,this.refs=_,this.updater=ae||z}N.prototype.isReactComponent={},N.prototype.setState=function(w,K){if(typeof w!="object"&&typeof w!="function"&&w!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,w,K,"setState")},N.prototype.forceUpdate=function(w){this.updater.enqueueForceUpdate(this,w,"forceUpdate")};function q(){}q.prototype=N.prototype;function j(w,K,ae){this.props=w,this.context=K,this.refs=_,this.updater=ae||z}var H=j.prototype=new q;H.constructor=j,L(H,N.prototype),H.isPureReactComponent=!0;var P=Array.isArray,Z={H:null,A:null,T:null,S:null,V:null},se=Object.prototype.hasOwnProperty;function $(w,K,ae,W,re,Re){return ae=Re.ref,{$$typeof:a,type:w,key:K,ref:ae!==void 0?ae:null,props:Re}}function J(w,K){return $(w.type,K,void 0,void 0,void 0,w.props)}function ge(w){return typeof w=="object"&&w!==null&&w.$$typeof===a}function Te(w){var K={"=":"=0",":":"=2"};return"$"+w.replace(/[=:]/g,function(ae){return K[ae]})}var Ce=/\/+/g;function ne(w,K){return typeof w=="object"&&w!==null&&w.key!=null?Te(""+w.key):K.toString(36)}function ce(){}function fe(w){switch(w.status){case"fulfilled":return w.value;case"rejected":throw w.reason;default:switch(typeof w.status=="string"?w.then(ce,ce):(w.status="pending",w.then(function(K){w.status==="pending"&&(w.status="fulfilled",w.value=K)},function(K){w.status==="pending"&&(w.status="rejected",w.reason=K)})),w.status){case"fulfilled":return w.value;case"rejected":throw w.reason}}throw w}function ve(w,K,ae,W,re){var Re=typeof w;(Re==="undefined"||Re==="boolean")&&(w=null);var Se=!1;if(w===null)Se=!0;else switch(Re){case"bigint":case"string":case"number":Se=!0;break;case"object":switch(w.$$typeof){case a:case l:Se=!0;break;case b:return Se=w._init,ve(Se(w._payload),K,ae,W,re)}}if(Se)return re=re(w),Se=W===""?"."+ne(w,0):W,P(re)?(ae="",Se!=null&&(ae=Se.replace(Ce,"$&/")+"/"),ve(re,K,ae,"",function(Xe){return Xe})):re!=null&&(ge(re)&&(re=J(re,ae+(re.key==null||w&&w.key===re.key?"":(""+re.key).replace(Ce,"$&/")+"/")+Se)),K.push(re)),1;Se=0;var te=W===""?".":W+":";if(P(w))for(var we=0;we<w.length;we++)W=w[we],Re=te+ne(W,we),Se+=ve(W,K,ae,Re,re);else if(we=T(w),typeof we=="function")for(w=we.call(w),we=0;!(W=w.next()).done;)W=W.value,Re=te+ne(W,we++),Se+=ve(W,K,ae,Re,re);else if(Re==="object"){if(typeof w.then=="function")return ve(fe(w),K,ae,W,re);throw K=String(w),Error("Objects are not valid as a React child (found: "+(K==="[object Object]"?"object with keys {"+Object.keys(w).join(", ")+"}":K)+"). If you meant to render a collection of children, use an array instead.")}return Se}function O(w,K,ae){if(w==null)return w;var W=[],re=0;return ve(w,W,"","",function(Re){return K.call(ae,Re,re++)}),W}function G(w){if(w._status===-1){var K=w._result;K=K(),K.then(function(ae){(w._status===0||w._status===-1)&&(w._status=1,w._result=ae)},function(ae){(w._status===0||w._status===-1)&&(w._status=2,w._result=ae)}),w._status===-1&&(w._status=0,w._result=K)}if(w._status===1)return w._result.default;throw w._result}var V=typeof reportError=="function"?reportError:function(w){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var K=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof w=="object"&&w!==null&&typeof w.message=="string"?String(w.message):String(w),error:w});if(!window.dispatchEvent(K))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",w);return}console.error(w)};function pe(){}return De.Children={map:O,forEach:function(w,K,ae){O(w,function(){K.apply(this,arguments)},ae)},count:function(w){var K=0;return O(w,function(){K++}),K},toArray:function(w){return O(w,function(K){return K})||[]},only:function(w){if(!ge(w))throw Error("React.Children.only expected to receive a single React element child.");return w}},De.Component=N,De.Fragment=i,De.Profiler=u,De.PureComponent=j,De.StrictMode=s,De.Suspense=p,De.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Z,De.__COMPILER_RUNTIME={__proto__:null,c:function(w){return Z.H.useMemoCache(w)}},De.cache=function(w){return function(){return w.apply(null,arguments)}},De.cloneElement=function(w,K,ae){if(w==null)throw Error("The argument must be a React element, but you passed "+w+".");var W=L({},w.props),re=w.key,Re=void 0;if(K!=null)for(Se in K.ref!==void 0&&(Re=void 0),K.key!==void 0&&(re=""+K.key),K)!se.call(K,Se)||Se==="key"||Se==="__self"||Se==="__source"||Se==="ref"&&K.ref===void 0||(W[Se]=K[Se]);var Se=arguments.length-2;if(Se===1)W.children=ae;else if(1<Se){for(var te=Array(Se),we=0;we<Se;we++)te[we]=arguments[we+2];W.children=te}return $(w.type,re,void 0,void 0,Re,W)},De.createContext=function(w){return w={$$typeof:d,_currentValue:w,_currentValue2:w,_threadCount:0,Provider:null,Consumer:null},w.Provider=w,w.Consumer={$$typeof:f,_context:w},w},De.createElement=function(w,K,ae){var W,re={},Re=null;if(K!=null)for(W in K.key!==void 0&&(Re=""+K.key),K)se.call(K,W)&&W!=="key"&&W!=="__self"&&W!=="__source"&&(re[W]=K[W]);var Se=arguments.length-2;if(Se===1)re.children=ae;else if(1<Se){for(var te=Array(Se),we=0;we<Se;we++)te[we]=arguments[we+2];re.children=te}if(w&&w.defaultProps)for(W in Se=w.defaultProps,Se)re[W]===void 0&&(re[W]=Se[W]);return $(w,Re,void 0,void 0,null,re)},De.createRef=function(){return{current:null}},De.forwardRef=function(w){return{$$typeof:h,render:w}},De.isValidElement=ge,De.lazy=function(w){return{$$typeof:b,_payload:{_status:-1,_result:w},_init:G}},De.memo=function(w,K){return{$$typeof:v,type:w,compare:K===void 0?null:K}},De.startTransition=function(w){var K=Z.T,ae={};Z.T=ae;try{var W=w(),re=Z.S;re!==null&&re(ae,W),typeof W=="object"&&W!==null&&typeof W.then=="function"&&W.then(pe,V)}catch(Re){V(Re)}finally{Z.T=K}},De.unstable_useCacheRefresh=function(){return Z.H.useCacheRefresh()},De.use=function(w){return Z.H.use(w)},De.useActionState=function(w,K,ae){return Z.H.useActionState(w,K,ae)},De.useCallback=function(w,K){return Z.H.useCallback(w,K)},De.useContext=function(w){return Z.H.useContext(w)},De.useDebugValue=function(){},De.useDeferredValue=function(w,K){return Z.H.useDeferredValue(w,K)},De.useEffect=function(w,K,ae){var W=Z.H;if(typeof ae=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return W.useEffect(w,K)},De.useId=function(){return Z.H.useId()},De.useImperativeHandle=function(w,K,ae){return Z.H.useImperativeHandle(w,K,ae)},De.useInsertionEffect=function(w,K){return Z.H.useInsertionEffect(w,K)},De.useLayoutEffect=function(w,K){return Z.H.useLayoutEffect(w,K)},De.useMemo=function(w,K){return Z.H.useMemo(w,K)},De.useOptimistic=function(w,K){return Z.H.useOptimistic(w,K)},De.useReducer=function(w,K,ae){return Z.H.useReducer(w,K,ae)},De.useRef=function(w){return Z.H.useRef(w)},De.useState=function(w){return Z.H.useState(w)},De.useSyncExternalStore=function(w,K,ae){return Z.H.useSyncExternalStore(w,K,ae)},De.useTransition=function(){return Z.H.useTransition()},De.version="19.1.0",De}var ip;function Yf(){return ip||(ip=1,nf.exports=Zx()),nf.exports}var E=Yf();const Be=_g(E),wg=Lx({__proto__:null,default:Be},[E]);var af={exports:{}},Ci={},lf={exports:{}},rf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sp;function Yx(){return sp||(sp=1,function(a){function l(O,G){var V=O.length;O.push(G);e:for(;0<V;){var pe=V-1>>>1,w=O[pe];if(0<u(w,G))O[pe]=G,O[V]=w,V=pe;else break e}}function i(O){return O.length===0?null:O[0]}function s(O){if(O.length===0)return null;var G=O[0],V=O.pop();if(V!==G){O[0]=V;e:for(var pe=0,w=O.length,K=w>>>1;pe<K;){var ae=2*(pe+1)-1,W=O[ae],re=ae+1,Re=O[re];if(0>u(W,V))re<w&&0>u(Re,W)?(O[pe]=Re,O[re]=V,pe=re):(O[pe]=W,O[ae]=V,pe=ae);else if(re<w&&0>u(Re,V))O[pe]=Re,O[re]=V,pe=re;else break e}}return G}function u(O,G){var V=O.sortIndex-G.sortIndex;return V!==0?V:O.id-G.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var d=Date,h=d.now();a.unstable_now=function(){return d.now()-h}}var p=[],v=[],b=1,S=null,T=3,z=!1,L=!1,_=!1,N=!1,q=typeof setTimeout=="function"?setTimeout:null,j=typeof clearTimeout=="function"?clearTimeout:null,H=typeof setImmediate<"u"?setImmediate:null;function P(O){for(var G=i(v);G!==null;){if(G.callback===null)s(v);else if(G.startTime<=O)s(v),G.sortIndex=G.expirationTime,l(p,G);else break;G=i(v)}}function Z(O){if(_=!1,P(O),!L)if(i(p)!==null)L=!0,se||(se=!0,ne());else{var G=i(v);G!==null&&ve(Z,G.startTime-O)}}var se=!1,$=-1,J=5,ge=-1;function Te(){return N?!0:!(a.unstable_now()-ge<J)}function Ce(){if(N=!1,se){var O=a.unstable_now();ge=O;var G=!0;try{e:{L=!1,_&&(_=!1,j($),$=-1),z=!0;var V=T;try{t:{for(P(O),S=i(p);S!==null&&!(S.expirationTime>O&&Te());){var pe=S.callback;if(typeof pe=="function"){S.callback=null,T=S.priorityLevel;var w=pe(S.expirationTime<=O);if(O=a.unstable_now(),typeof w=="function"){S.callback=w,P(O),G=!0;break t}S===i(p)&&s(p),P(O)}else s(p);S=i(p)}if(S!==null)G=!0;else{var K=i(v);K!==null&&ve(Z,K.startTime-O),G=!1}}break e}finally{S=null,T=V,z=!1}G=void 0}}finally{G?ne():se=!1}}}var ne;if(typeof H=="function")ne=function(){H(Ce)};else if(typeof MessageChannel<"u"){var ce=new MessageChannel,fe=ce.port2;ce.port1.onmessage=Ce,ne=function(){fe.postMessage(null)}}else ne=function(){q(Ce,0)};function ve(O,G){$=q(function(){O(a.unstable_now())},G)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(O){O.callback=null},a.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):J=0<O?Math.floor(1e3/O):5},a.unstable_getCurrentPriorityLevel=function(){return T},a.unstable_next=function(O){switch(T){case 1:case 2:case 3:var G=3;break;default:G=T}var V=T;T=G;try{return O()}finally{T=V}},a.unstable_requestPaint=function(){N=!0},a.unstable_runWithPriority=function(O,G){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var V=T;T=O;try{return G()}finally{T=V}},a.unstable_scheduleCallback=function(O,G,V){var pe=a.unstable_now();switch(typeof V=="object"&&V!==null?(V=V.delay,V=typeof V=="number"&&0<V?pe+V:pe):V=pe,O){case 1:var w=-1;break;case 2:w=250;break;case 5:w=1073741823;break;case 4:w=1e4;break;default:w=5e3}return w=V+w,O={id:b++,callback:G,priorityLevel:O,startTime:V,expirationTime:w,sortIndex:-1},V>pe?(O.sortIndex=V,l(v,O),i(p)===null&&O===i(v)&&(_?(j($),$=-1):_=!0,ve(Z,V-pe))):(O.sortIndex=w,l(p,O),L||z||(L=!0,se||(se=!0,ne()))),O},a.unstable_shouldYield=Te,a.unstable_wrapCallback=function(O){var G=T;return function(){var V=T;T=G;try{return O.apply(this,arguments)}finally{T=V}}}}(rf)),rf}var op;function Gx(){return op||(op=1,lf.exports=Yx()),lf.exports}var sf={exports:{}},Gt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var up;function Xx(){if(up)return Gt;up=1;var a=Yf();function l(p){var v="https://react.dev/errors/"+p;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)v+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+p+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var s={d:{f:i,r:function(){throw Error(l(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},u=Symbol.for("react.portal");function f(p,v,b){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:u,key:S==null?null:""+S,children:p,containerInfo:v,implementation:b}}var d=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(p,v){if(p==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return Gt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,Gt.createPortal=function(p,v){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(l(299));return f(p,v,null,b)},Gt.flushSync=function(p){var v=d.T,b=s.p;try{if(d.T=null,s.p=2,p)return p()}finally{d.T=v,s.p=b,s.d.f()}},Gt.preconnect=function(p,v){typeof p=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,s.d.C(p,v))},Gt.prefetchDNS=function(p){typeof p=="string"&&s.d.D(p)},Gt.preinit=function(p,v){if(typeof p=="string"&&v&&typeof v.as=="string"){var b=v.as,S=h(b,v.crossOrigin),T=typeof v.integrity=="string"?v.integrity:void 0,z=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;b==="style"?s.d.S(p,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:S,integrity:T,fetchPriority:z}):b==="script"&&s.d.X(p,{crossOrigin:S,integrity:T,fetchPriority:z,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},Gt.preinitModule=function(p,v){if(typeof p=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var b=h(v.as,v.crossOrigin);s.d.M(p,{crossOrigin:b,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&s.d.M(p)},Gt.preload=function(p,v){if(typeof p=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var b=v.as,S=h(b,v.crossOrigin);s.d.L(p,b,{crossOrigin:S,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},Gt.preloadModule=function(p,v){if(typeof p=="string")if(v){var b=h(v.as,v.crossOrigin);s.d.m(p,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:b,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else s.d.m(p)},Gt.requestFormReset=function(p){s.d.r(p)},Gt.unstable_batchedUpdates=function(p,v){return p(v)},Gt.useFormState=function(p,v,b){return d.H.useFormState(p,v,b)},Gt.useFormStatus=function(){return d.H.useHostTransitionStatus()},Gt.version="19.1.0",Gt}var cp;function Eg(){if(cp)return sf.exports;cp=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),sf.exports=Xx(),sf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fp;function Qx(){if(fp)return Ci;fp=1;var a=Gx(),l=Yf(),i=Eg();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(f(e)!==e)throw Error(s(188))}function p(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(s(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var c=o.alternate;if(c===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===c.child){for(c=o.child;c;){if(c===n)return h(o),e;if(c===r)return h(o),t;c=c.sibling}throw Error(s(188))}if(n.return!==r.return)n=o,r=c;else{for(var m=!1,y=o.child;y;){if(y===n){m=!0,n=o,r=c;break}if(y===r){m=!0,r=o,n=c;break}y=y.sibling}if(!m){for(y=c.child;y;){if(y===n){m=!0,n=c,r=o;break}if(y===r){m=!0,r=c,n=o;break}y=y.sibling}if(!m)throw Error(s(189))}}if(n.alternate!==r)throw Error(s(190))}if(n.tag!==3)throw Error(s(188));return n.stateNode.current===n?e:t}function v(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=v(e),t!==null)return t;e=e.sibling}return null}var b=Object.assign,S=Symbol.for("react.element"),T=Symbol.for("react.transitional.element"),z=Symbol.for("react.portal"),L=Symbol.for("react.fragment"),_=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),q=Symbol.for("react.provider"),j=Symbol.for("react.consumer"),H=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),Z=Symbol.for("react.suspense"),se=Symbol.for("react.suspense_list"),$=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),ge=Symbol.for("react.activity"),Te=Symbol.for("react.memo_cache_sentinel"),Ce=Symbol.iterator;function ne(e){return e===null||typeof e!="object"?null:(e=Ce&&e[Ce]||e["@@iterator"],typeof e=="function"?e:null)}var ce=Symbol.for("react.client.reference");function fe(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ce?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case L:return"Fragment";case N:return"Profiler";case _:return"StrictMode";case Z:return"Suspense";case se:return"SuspenseList";case ge:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case z:return"Portal";case H:return(e.displayName||"Context")+".Provider";case j:return(e._context.displayName||"Context")+".Consumer";case P:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case $:return t=e.displayName||null,t!==null?t:fe(e.type)||"Memo";case J:t=e._payload,e=e._init;try{return fe(e(t))}catch{}}return null}var ve=Array.isArray,O=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G=i.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,V={pending:!1,data:null,method:null,action:null},pe=[],w=-1;function K(e){return{current:e}}function ae(e){0>w||(e.current=pe[w],pe[w]=null,w--)}function W(e,t){w++,pe[w]=e.current,e.current=t}var re=K(null),Re=K(null),Se=K(null),te=K(null);function we(e,t){switch(W(Se,t),W(Re,e),W(re,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?jv(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=jv(t),e=Mv(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}ae(re),W(re,e)}function Xe(){ae(re),ae(Re),ae(Se)}function Ue(e){e.memoizedState!==null&&W(te,e);var t=re.current,n=Mv(t,e.type);t!==n&&(W(Re,e),W(re,n))}function Ze(e){Re.current===e&&(ae(re),ae(Re)),te.current===e&&(ae(te),xi._currentValue=V)}var Ke=Object.prototype.hasOwnProperty,Mt=a.unstable_scheduleCallback,Kt=a.unstable_cancelCallback,ma=a.unstable_shouldYield,Wa=a.unstable_requestPaint,qt=a.unstable_now,hd=a.unstable_getCurrentPriorityLevel,Rr=a.unstable_ImmediatePriority,A=a.unstable_UserBlockingPriority,k=a.unstable_NormalPriority,X=a.unstable_LowPriority,oe=a.unstable_IdlePriority,le=a.log,ee=a.unstable_setDisableYieldValue,be=null,Me=null;function Ye(e){if(typeof le=="function"&&ee(e),Me&&typeof Me.setStrictMode=="function")try{Me.setStrictMode(be,e)}catch{}}var lt=Math.clz32?Math.clz32:Yo,Al=Math.log,Dn=Math.LN2;function Yo(e){return e>>>=0,e===0?32:31-(Al(e)/Dn|0)|0}var va=256,pa=4194304;function Qn(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Ia(e,t,n){var r=e.pendingLanes;if(r===0)return 0;var o=0,c=e.suspendedLanes,m=e.pingedLanes;e=e.warmLanes;var y=r&134217727;return y!==0?(r=y&~c,r!==0?o=Qn(r):(m&=y,m!==0?o=Qn(m):n||(n=y&~e,n!==0&&(o=Qn(n))))):(y=r&~c,y!==0?o=Qn(y):m!==0?o=Qn(m):n||(n=r&~e,n!==0&&(o=Qn(n)))),o===0?0:t!==0&&t!==o&&(t&c)===0&&(c=o&-o,n=t&-t,c>=n||c===32&&(n&4194048)!==0)?t:o}function el(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Yi(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function md(){var e=va;return va<<=1,(va&4194048)===0&&(va=256),e}function vd(){var e=pa;return pa<<=1,(pa&62914560)===0&&(pa=4194304),e}function Go(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Nr(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Tb(e,t,n,r,o,c){var m=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var y=e.entanglements,x=e.expirationTimes,D=e.hiddenUpdates;for(n=m&~n;0<n;){var Y=31-lt(n),F=1<<Y;y[Y]=0,x[Y]=-1;var U=D[Y];if(U!==null)for(D[Y]=null,Y=0;Y<U.length;Y++){var B=U[Y];B!==null&&(B.lane&=-536870913)}n&=~F}r!==0&&pd(e,r,0),c!==0&&o===0&&e.tag!==0&&(e.suspendedLanes|=c&~(m&~t))}function pd(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-lt(t);e.entangledLanes|=t,e.entanglements[r]=e.entanglements[r]|1073741824|n&4194090}function gd(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-lt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}function Xo(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Qo(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function yd(){var e=G.p;return e!==0?e:(e=window.event,e===void 0?32:Jv(e.type))}function Ob(e,t){var n=G.p;try{return G.p=e,t()}finally{G.p=n}}var ga=Math.random().toString(36).slice(2),Zt="__reactFiber$"+ga,Pt="__reactProps$"+ga,Cl="__reactContainer$"+ga,Ko="__reactEvents$"+ga,Rb="__reactListeners$"+ga,Nb="__reactHandles$"+ga,bd="__reactResources$"+ga,jr="__reactMarker$"+ga;function Po(e){delete e[Zt],delete e[Pt],delete e[Ko],delete e[Rb],delete e[Nb]}function Tl(e){var t=e[Zt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Cl]||n[Zt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Uv(e);e!==null;){if(n=e[Zt])return n;e=Uv(e)}return t}e=n,n=e.parentNode}return null}function Ol(e){if(e=e[Zt]||e[Cl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Mr(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function Rl(e){var t=e[bd];return t||(t=e[bd]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Dt(e){e[jr]=!0}var xd=new Set,Sd={};function tl(e,t){Nl(e,t),Nl(e+"Capture",t)}function Nl(e,t){for(Sd[e]=t,e=0;e<t.length;e++)xd.add(t[e])}var jb=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),_d={},wd={};function Mb(e){return Ke.call(wd,e)?!0:Ke.call(_d,e)?!1:jb.test(e)?wd[e]=!0:(_d[e]=!0,!1)}function Gi(e,t,n){if(Mb(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var r=t.toLowerCase().slice(0,5);if(r!=="data-"&&r!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Xi(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function Kn(e,t,n,r){if(r===null)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+r)}}var Fo,Ed;function jl(e){if(Fo===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Fo=t&&t[1]||"",Ed=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Fo+e+Ed}var $o=!1;function Jo(e,t){if(!e||$o)return"";$o=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var F=function(){throw Error()};if(Object.defineProperty(F.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(F,[])}catch(B){var U=B}Reflect.construct(e,[],F)}else{try{F.call()}catch(B){U=B}e.call(F.prototype)}}else{try{throw Error()}catch(B){U=B}(F=e())&&typeof F.catch=="function"&&F.catch(function(){})}}catch(B){if(B&&U&&typeof B.stack=="string")return[B.stack,U.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=r.DetermineComponentFrameRoot(),m=c[0],y=c[1];if(m&&y){var x=m.split(`
`),D=y.split(`
`);for(o=r=0;r<x.length&&!x[r].includes("DetermineComponentFrameRoot");)r++;for(;o<D.length&&!D[o].includes("DetermineComponentFrameRoot");)o++;if(r===x.length||o===D.length)for(r=x.length-1,o=D.length-1;1<=r&&0<=o&&x[r]!==D[o];)o--;for(;1<=r&&0<=o;r--,o--)if(x[r]!==D[o]){if(r!==1||o!==1)do if(r--,o--,0>o||x[r]!==D[o]){var Y=`
`+x[r].replace(" at new "," at ");return e.displayName&&Y.includes("<anonymous>")&&(Y=Y.replace("<anonymous>",e.displayName)),Y}while(1<=r&&0<=o);break}}}finally{$o=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?jl(n):""}function Db(e){switch(e.tag){case 26:case 27:case 5:return jl(e.type);case 16:return jl("Lazy");case 13:return jl("Suspense");case 19:return jl("SuspenseList");case 0:case 15:return Jo(e.type,!1);case 11:return Jo(e.type.render,!1);case 1:return Jo(e.type,!0);case 31:return jl("Activity");default:return""}}function Ad(e){try{var t="";do t+=Db(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function vn(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Cd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function zb(e){var t=Cd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(m){r=""+m,c.call(this,m)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(m){r=""+m},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Qi(e){e._valueTracker||(e._valueTracker=zb(e))}function Td(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Cd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ki(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var kb=/[\n"\\]/g;function pn(e){return e.replace(kb,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Wo(e,t,n,r,o,c,m,y){e.name="",m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.type=m:e.removeAttribute("type"),t!=null?m==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+vn(t)):e.value!==""+vn(t)&&(e.value=""+vn(t)):m!=="submit"&&m!=="reset"||e.removeAttribute("value"),t!=null?Io(e,m,vn(t)):n!=null?Io(e,m,vn(n)):r!=null&&e.removeAttribute("value"),o==null&&c!=null&&(e.defaultChecked=!!c),o!=null&&(e.checked=o&&typeof o!="function"&&typeof o!="symbol"),y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.name=""+vn(y):e.removeAttribute("name")}function Od(e,t,n,r,o,c,m,y){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;n=n!=null?""+vn(n):"",t=t!=null?""+vn(t):n,y||t===e.value||(e.value=t),e.defaultValue=t}r=r??o,r=typeof r!="function"&&typeof r!="symbol"&&!!r,e.checked=y?e.checked:!!r,e.defaultChecked=!!r,m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"&&(e.name=m)}function Io(e,t,n){t==="number"&&Ki(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Ml(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+vn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Rd(e,t,n){if(t!=null&&(t=""+vn(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+vn(n):""}function Nd(e,t,n,r){if(t==null){if(r!=null){if(n!=null)throw Error(s(92));if(ve(r)){if(1<r.length)throw Error(s(93));r=r[0]}n=r}n==null&&(n=""),t=n}n=vn(t),e.defaultValue=n,r=e.textContent,r===n&&r!==""&&r!==null&&(e.value=r)}function Dl(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ub=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function jd(e,t,n){var r=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?r?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":r?e.setProperty(t,n):typeof n!="number"||n===0||Ub.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Md(e,t,n){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,n!=null){for(var r in n)!n.hasOwnProperty(r)||t!=null&&t.hasOwnProperty(r)||(r.indexOf("--")===0?e.setProperty(r,""):r==="float"?e.cssFloat="":e[r]="");for(var o in t)r=t[o],t.hasOwnProperty(o)&&n[o]!==r&&jd(e,o,r)}else for(var c in t)t.hasOwnProperty(c)&&jd(e,c,t[c])}function eu(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Vb=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Bb=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Pi(e){return Bb.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var tu=null;function nu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var zl=null,kl=null;function Dd(e){var t=Ol(e);if(t&&(e=t.stateNode)){var n=e[Pt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Wo(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+pn(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=r[Pt]||null;if(!o)throw Error(s(90));Wo(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(t=0;t<n.length;t++)r=n[t],r.form===e.form&&Td(r)}break e;case"textarea":Rd(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Ml(e,!!n.multiple,t,!1)}}}var au=!1;function zd(e,t,n){if(au)return e(t,n);au=!0;try{var r=e(t);return r}finally{if(au=!1,(zl!==null||kl!==null)&&(Ds(),zl&&(t=zl,e=kl,kl=zl=null,Dd(t),e)))for(t=0;t<e.length;t++)Dd(e[t])}}function Dr(e,t){var n=e.stateNode;if(n===null)return null;var r=n[Pt]||null;if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(s(231,t,typeof n));return n}var Pn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),lu=!1;if(Pn)try{var zr={};Object.defineProperty(zr,"passive",{get:function(){lu=!0}}),window.addEventListener("test",zr,zr),window.removeEventListener("test",zr,zr)}catch{lu=!1}var ya=null,ru=null,Fi=null;function kd(){if(Fi)return Fi;var e,t=ru,n=t.length,r,o="value"in ya?ya.value:ya.textContent,c=o.length;for(e=0;e<n&&t[e]===o[e];e++);var m=n-e;for(r=1;r<=m&&t[n-r]===o[c-r];r++);return Fi=o.slice(e,1<r?1-r:void 0)}function $i(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ji(){return!0}function Ud(){return!1}function Ft(e){function t(n,r,o,c,m){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=c,this.target=m,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(n=e[y],this[y]=n?n(c):c[y]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Ji:Ud,this.isPropagationStopped=Ud,this}return b(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ji)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ji)},persist:function(){},isPersistent:Ji}),t}var nl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Wi=Ft(nl),kr=b({},nl,{view:0,detail:0}),Lb=Ft(kr),iu,su,Ur,Ii=b({},kr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:uu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ur&&(Ur&&e.type==="mousemove"?(iu=e.screenX-Ur.screenX,su=e.screenY-Ur.screenY):su=iu=0,Ur=e),iu)},movementY:function(e){return"movementY"in e?e.movementY:su}}),Vd=Ft(Ii),Hb=b({},Ii,{dataTransfer:0}),qb=Ft(Hb),Zb=b({},kr,{relatedTarget:0}),ou=Ft(Zb),Yb=b({},nl,{animationName:0,elapsedTime:0,pseudoElement:0}),Gb=Ft(Yb),Xb=b({},nl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Qb=Ft(Xb),Kb=b({},nl,{data:0}),Bd=Ft(Kb),Pb={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Fb={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},$b={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Jb(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=$b[e])?!!t[e]:!1}function uu(){return Jb}var Wb=b({},kr,{key:function(e){if(e.key){var t=Pb[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=$i(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Fb[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:uu,charCode:function(e){return e.type==="keypress"?$i(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?$i(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Ib=Ft(Wb),e0=b({},Ii,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ld=Ft(e0),t0=b({},kr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:uu}),n0=Ft(t0),a0=b({},nl,{propertyName:0,elapsedTime:0,pseudoElement:0}),l0=Ft(a0),r0=b({},Ii,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),i0=Ft(r0),s0=b({},nl,{newState:0,oldState:0}),o0=Ft(s0),u0=[9,13,27,32],cu=Pn&&"CompositionEvent"in window,Vr=null;Pn&&"documentMode"in document&&(Vr=document.documentMode);var c0=Pn&&"TextEvent"in window&&!Vr,Hd=Pn&&(!cu||Vr&&8<Vr&&11>=Vr),qd=" ",Zd=!1;function Yd(e,t){switch(e){case"keyup":return u0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Gd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ul=!1;function f0(e,t){switch(e){case"compositionend":return Gd(t);case"keypress":return t.which!==32?null:(Zd=!0,qd);case"textInput":return e=t.data,e===qd&&Zd?null:e;default:return null}}function d0(e,t){if(Ul)return e==="compositionend"||!cu&&Yd(e,t)?(e=kd(),Fi=ru=ya=null,Ul=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Hd&&t.locale!=="ko"?null:t.data;default:return null}}var h0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Xd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!h0[e.type]:t==="textarea"}function Qd(e,t,n,r){zl?kl?kl.push(r):kl=[r]:zl=r,t=Ls(t,"onChange"),0<t.length&&(n=new Wi("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Br=null,Lr=null;function m0(e){Cv(e,0)}function es(e){var t=Mr(e);if(Td(t))return e}function Kd(e,t){if(e==="change")return t}var Pd=!1;if(Pn){var fu;if(Pn){var du="oninput"in document;if(!du){var Fd=document.createElement("div");Fd.setAttribute("oninput","return;"),du=typeof Fd.oninput=="function"}fu=du}else fu=!1;Pd=fu&&(!document.documentMode||9<document.documentMode)}function $d(){Br&&(Br.detachEvent("onpropertychange",Jd),Lr=Br=null)}function Jd(e){if(e.propertyName==="value"&&es(Lr)){var t=[];Qd(t,Lr,e,nu(e)),zd(m0,t)}}function v0(e,t,n){e==="focusin"?($d(),Br=t,Lr=n,Br.attachEvent("onpropertychange",Jd)):e==="focusout"&&$d()}function p0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return es(Lr)}function g0(e,t){if(e==="click")return es(t)}function y0(e,t){if(e==="input"||e==="change")return es(t)}function b0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var tn=typeof Object.is=="function"?Object.is:b0;function Hr(e,t){if(tn(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Ke.call(t,o)||!tn(e[o],t[o]))return!1}return!0}function Wd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Id(e,t){var n=Wd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Wd(n)}}function eh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?eh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function th(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Ki(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ki(e.document)}return t}function hu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var x0=Pn&&"documentMode"in document&&11>=document.documentMode,Vl=null,mu=null,qr=null,vu=!1;function nh(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;vu||Vl==null||Vl!==Ki(r)||(r=Vl,"selectionStart"in r&&hu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),qr&&Hr(qr,r)||(qr=r,r=Ls(mu,"onSelect"),0<r.length&&(t=new Wi("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Vl)))}function al(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Bl={animationend:al("Animation","AnimationEnd"),animationiteration:al("Animation","AnimationIteration"),animationstart:al("Animation","AnimationStart"),transitionrun:al("Transition","TransitionRun"),transitionstart:al("Transition","TransitionStart"),transitioncancel:al("Transition","TransitionCancel"),transitionend:al("Transition","TransitionEnd")},pu={},ah={};Pn&&(ah=document.createElement("div").style,"AnimationEvent"in window||(delete Bl.animationend.animation,delete Bl.animationiteration.animation,delete Bl.animationstart.animation),"TransitionEvent"in window||delete Bl.transitionend.transition);function ll(e){if(pu[e])return pu[e];if(!Bl[e])return e;var t=Bl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ah)return pu[e]=t[n];return e}var lh=ll("animationend"),rh=ll("animationiteration"),ih=ll("animationstart"),S0=ll("transitionrun"),_0=ll("transitionstart"),w0=ll("transitioncancel"),sh=ll("transitionend"),oh=new Map,gu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");gu.push("scrollEnd");function An(e,t){oh.set(e,t),tl(t,[e])}var uh=new WeakMap;function gn(e,t){if(typeof e=="object"&&e!==null){var n=uh.get(e);return n!==void 0?n:(t={value:e,source:t,stack:Ad(t)},uh.set(e,t),t)}return{value:e,source:t,stack:Ad(t)}}var yn=[],Ll=0,yu=0;function ts(){for(var e=Ll,t=yu=Ll=0;t<e;){var n=yn[t];yn[t++]=null;var r=yn[t];yn[t++]=null;var o=yn[t];yn[t++]=null;var c=yn[t];if(yn[t++]=null,r!==null&&o!==null){var m=r.pending;m===null?o.next=o:(o.next=m.next,m.next=o),r.pending=o}c!==0&&ch(n,o,c)}}function ns(e,t,n,r){yn[Ll++]=e,yn[Ll++]=t,yn[Ll++]=n,yn[Ll++]=r,yu|=r,e.lanes|=r,e=e.alternate,e!==null&&(e.lanes|=r)}function bu(e,t,n,r){return ns(e,t,n,r),as(e)}function Hl(e,t){return ns(e,null,null,t),as(e)}function ch(e,t,n){e.lanes|=n;var r=e.alternate;r!==null&&(r.lanes|=n);for(var o=!1,c=e.return;c!==null;)c.childLanes|=n,r=c.alternate,r!==null&&(r.childLanes|=n),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(o=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,o&&t!==null&&(o=31-lt(n),e=c.hiddenUpdates,r=e[o],r===null?e[o]=[t]:r.push(t),t.lane=n|536870912),c):null}function as(e){if(50<di)throw di=0,Ac=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var ql={};function E0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nn(e,t,n,r){return new E0(e,t,n,r)}function xu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Fn(e,t){var n=e.alternate;return n===null?(n=nn(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function fh(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ls(e,t,n,r,o,c){var m=0;if(r=e,typeof e=="function")xu(e)&&(m=1);else if(typeof e=="string")m=Cx(e,n,re.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ge:return e=nn(31,n,t,o),e.elementType=ge,e.lanes=c,e;case L:return rl(n.children,o,c,t);case _:m=8,o|=24;break;case N:return e=nn(12,n,t,o|2),e.elementType=N,e.lanes=c,e;case Z:return e=nn(13,n,t,o),e.elementType=Z,e.lanes=c,e;case se:return e=nn(19,n,t,o),e.elementType=se,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case q:case H:m=10;break e;case j:m=9;break e;case P:m=11;break e;case $:m=14;break e;case J:m=16,r=null;break e}m=29,n=Error(s(130,e===null?"null":typeof e,"")),r=null}return t=nn(m,n,t,o),t.elementType=e,t.type=r,t.lanes=c,t}function rl(e,t,n,r){return e=nn(7,e,r,t),e.lanes=n,e}function Su(e,t,n){return e=nn(6,e,null,t),e.lanes=n,e}function _u(e,t,n){return t=nn(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Zl=[],Yl=0,rs=null,is=0,bn=[],xn=0,il=null,$n=1,Jn="";function sl(e,t){Zl[Yl++]=is,Zl[Yl++]=rs,rs=e,is=t}function dh(e,t,n){bn[xn++]=$n,bn[xn++]=Jn,bn[xn++]=il,il=e;var r=$n;e=Jn;var o=32-lt(r)-1;r&=~(1<<o),n+=1;var c=32-lt(t)+o;if(30<c){var m=o-o%5;c=(r&(1<<m)-1).toString(32),r>>=m,o-=m,$n=1<<32-lt(t)+o|n<<o|r,Jn=c+e}else $n=1<<c|n<<o|r,Jn=e}function wu(e){e.return!==null&&(sl(e,1),dh(e,1,0))}function Eu(e){for(;e===rs;)rs=Zl[--Yl],Zl[Yl]=null,is=Zl[--Yl],Zl[Yl]=null;for(;e===il;)il=bn[--xn],bn[xn]=null,Jn=bn[--xn],bn[xn]=null,$n=bn[--xn],bn[xn]=null}var Xt=null,ht=null,$e=!1,ol=null,zn=!1,Au=Error(s(519));function ul(e){var t=Error(s(418,""));throw Gr(gn(t,e)),Au}function hh(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Zt]=e,t[Pt]=r,n){case"dialog":qe("cancel",t),qe("close",t);break;case"iframe":case"object":case"embed":qe("load",t);break;case"video":case"audio":for(n=0;n<mi.length;n++)qe(mi[n],t);break;case"source":qe("error",t);break;case"img":case"image":case"link":qe("error",t),qe("load",t);break;case"details":qe("toggle",t);break;case"input":qe("invalid",t),Od(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),Qi(t);break;case"select":qe("invalid",t);break;case"textarea":qe("invalid",t),Nd(t,r.value,r.defaultValue,r.children),Qi(t)}n=r.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||r.suppressHydrationWarning===!0||Nv(t.textContent,n)?(r.popover!=null&&(qe("beforetoggle",t),qe("toggle",t)),r.onScroll!=null&&qe("scroll",t),r.onScrollEnd!=null&&qe("scrollend",t),r.onClick!=null&&(t.onclick=Hs),t=!0):t=!1,t||ul(e)}function mh(e){for(Xt=e.return;Xt;)switch(Xt.tag){case 5:case 13:zn=!1;return;case 27:case 3:zn=!0;return;default:Xt=Xt.return}}function Zr(e){if(e!==Xt)return!1;if(!$e)return mh(e),$e=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||qc(e.type,e.memoizedProps)),n=!n),n&&ht&&ul(e),mh(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){ht=Tn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}ht=null}}else t===27?(t=ht,za(e.type)?(e=Xc,Xc=null,ht=e):ht=t):ht=Xt?Tn(e.stateNode.nextSibling):null;return!0}function Yr(){ht=Xt=null,$e=!1}function vh(){var e=ol;return e!==null&&(Wt===null?Wt=e:Wt.push.apply(Wt,e),ol=null),e}function Gr(e){ol===null?ol=[e]:ol.push(e)}var Cu=K(null),cl=null,Wn=null;function ba(e,t,n){W(Cu,t._currentValue),t._currentValue=n}function In(e){e._currentValue=Cu.current,ae(Cu)}function Tu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ou(e,t,n,r){var o=e.child;for(o!==null&&(o.return=e);o!==null;){var c=o.dependencies;if(c!==null){var m=o.child;c=c.firstContext;e:for(;c!==null;){var y=c;c=o;for(var x=0;x<t.length;x++)if(y.context===t[x]){c.lanes|=n,y=c.alternate,y!==null&&(y.lanes|=n),Tu(c.return,n,e),r||(m=null);break e}c=y.next}}else if(o.tag===18){if(m=o.return,m===null)throw Error(s(341));m.lanes|=n,c=m.alternate,c!==null&&(c.lanes|=n),Tu(m,n,e),m=null}else m=o.child;if(m!==null)m.return=o;else for(m=o;m!==null;){if(m===e){m=null;break}if(o=m.sibling,o!==null){o.return=m.return,m=o;break}m=m.return}o=m}}function Xr(e,t,n,r){e=null;for(var o=t,c=!1;o!==null;){if(!c){if((o.flags&524288)!==0)c=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var m=o.alternate;if(m===null)throw Error(s(387));if(m=m.memoizedProps,m!==null){var y=o.type;tn(o.pendingProps.value,m.value)||(e!==null?e.push(y):e=[y])}}else if(o===te.current){if(m=o.alternate,m===null)throw Error(s(387));m.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(e!==null?e.push(xi):e=[xi])}o=o.return}e!==null&&Ou(t,e,n,r),t.flags|=262144}function ss(e){for(e=e.firstContext;e!==null;){if(!tn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function fl(e){cl=e,Wn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Yt(e){return ph(cl,e)}function os(e,t){return cl===null&&fl(e),ph(e,t)}function ph(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},Wn===null){if(e===null)throw Error(s(308));Wn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Wn=Wn.next=t;return n}var A0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,r){e.push(r)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},C0=a.unstable_scheduleCallback,T0=a.unstable_NormalPriority,Tt={$$typeof:H,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ru(){return{controller:new A0,data:new Map,refCount:0}}function Qr(e){e.refCount--,e.refCount===0&&C0(T0,function(){e.controller.abort()})}var Kr=null,Nu=0,Gl=0,Xl=null;function O0(e,t){if(Kr===null){var n=Kr=[];Nu=0,Gl=Mc(),Xl={status:"pending",value:void 0,then:function(r){n.push(r)}}}return Nu++,t.then(gh,gh),t}function gh(){if(--Nu===0&&Kr!==null){Xl!==null&&(Xl.status="fulfilled");var e=Kr;Kr=null,Gl=0,Xl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function R0(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(o){n.push(o)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var o=0;o<n.length;o++)(0,n[o])(t)},function(o){for(r.status="rejected",r.reason=o,o=0;o<n.length;o++)(0,n[o])(void 0)}),r}var yh=O.S;O.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&O0(e,t),yh!==null&&yh(e,t)};var dl=K(null);function ju(){var e=dl.current;return e!==null?e:rt.pooledCache}function us(e,t){t===null?W(dl,dl.current):W(dl,t.pool)}function bh(){var e=ju();return e===null?null:{parent:Tt._currentValue,pool:e}}var Pr=Error(s(460)),xh=Error(s(474)),cs=Error(s(542)),Mu={then:function(){}};function Sh(e){return e=e.status,e==="fulfilled"||e==="rejected"}function fs(){}function _h(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(fs,fs),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Eh(e),e;default:if(typeof t.status=="string")t.then(fs,fs);else{if(e=rt,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(r){if(t.status==="pending"){var o=t;o.status="fulfilled",o.value=r}},function(r){if(t.status==="pending"){var o=t;o.status="rejected",o.reason=r}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Eh(e),e}throw Fr=t,Pr}}var Fr=null;function wh(){if(Fr===null)throw Error(s(459));var e=Fr;return Fr=null,e}function Eh(e){if(e===Pr||e===cs)throw Error(s(483))}var xa=!1;function Du(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function zu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Sa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function _a(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,(Je&2)!==0){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,t=as(e),ch(e,null,n),t}return ns(e,r,t,n),as(e)}function $r(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,gd(e,n)}}function ku(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var m={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?o=c=m:c=c.next=m,n=n.next}while(n!==null);c===null?o=c=t:c=c.next=t}else o=c=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:c,shared:r.shared,callbacks:r.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Uu=!1;function Jr(){if(Uu){var e=Xl;if(e!==null)throw e}}function Wr(e,t,n,r){Uu=!1;var o=e.updateQueue;xa=!1;var c=o.firstBaseUpdate,m=o.lastBaseUpdate,y=o.shared.pending;if(y!==null){o.shared.pending=null;var x=y,D=x.next;x.next=null,m===null?c=D:m.next=D,m=x;var Y=e.alternate;Y!==null&&(Y=Y.updateQueue,y=Y.lastBaseUpdate,y!==m&&(y===null?Y.firstBaseUpdate=D:y.next=D,Y.lastBaseUpdate=x))}if(c!==null){var F=o.baseState;m=0,Y=D=x=null,y=c;do{var U=y.lane&-536870913,B=U!==y.lane;if(B?(Ge&U)===U:(r&U)===U){U!==0&&U===Gl&&(Uu=!0),Y!==null&&(Y=Y.next={lane:0,tag:y.tag,payload:y.payload,callback:null,next:null});e:{var Oe=e,Ee=y;U=t;var nt=n;switch(Ee.tag){case 1:if(Oe=Ee.payload,typeof Oe=="function"){F=Oe.call(nt,F,U);break e}F=Oe;break e;case 3:Oe.flags=Oe.flags&-65537|128;case 0:if(Oe=Ee.payload,U=typeof Oe=="function"?Oe.call(nt,F,U):Oe,U==null)break e;F=b({},F,U);break e;case 2:xa=!0}}U=y.callback,U!==null&&(e.flags|=64,B&&(e.flags|=8192),B=o.callbacks,B===null?o.callbacks=[U]:B.push(U))}else B={lane:U,tag:y.tag,payload:y.payload,callback:y.callback,next:null},Y===null?(D=Y=B,x=F):Y=Y.next=B,m|=U;if(y=y.next,y===null){if(y=o.shared.pending,y===null)break;B=y,y=B.next,B.next=null,o.lastBaseUpdate=B,o.shared.pending=null}}while(!0);Y===null&&(x=F),o.baseState=x,o.firstBaseUpdate=D,o.lastBaseUpdate=Y,c===null&&(o.shared.lanes=0),Na|=m,e.lanes=m,e.memoizedState=F}}function Ah(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function Ch(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Ah(n[e],t)}var Ql=K(null),ds=K(0);function Th(e,t){e=ia,W(ds,e),W(Ql,t),ia=e|t.baseLanes}function Vu(){W(ds,ia),W(Ql,Ql.current)}function Bu(){ia=ds.current,ae(Ql),ae(ds)}var wa=0,ze=null,et=null,St=null,hs=!1,Kl=!1,hl=!1,ms=0,Ir=0,Pl=null,N0=0;function gt(){throw Error(s(321))}function Lu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!tn(e[n],t[n]))return!1;return!0}function Hu(e,t,n,r,o,c){return wa=c,ze=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,O.H=e===null||e.memoizedState===null?cm:fm,hl=!1,c=n(r,o),hl=!1,Kl&&(c=Rh(t,n,r,o)),Oh(e),c}function Oh(e){O.H=xs;var t=et!==null&&et.next!==null;if(wa=0,St=et=ze=null,hs=!1,Ir=0,Pl=null,t)throw Error(s(300));e===null||zt||(e=e.dependencies,e!==null&&ss(e)&&(zt=!0))}function Rh(e,t,n,r){ze=e;var o=0;do{if(Kl&&(Pl=null),Ir=0,Kl=!1,25<=o)throw Error(s(301));if(o+=1,St=et=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}O.H=V0,c=t(n,r)}while(Kl);return c}function j0(){var e=O.H,t=e.useState()[0];return t=typeof t.then=="function"?ei(t):t,e=e.useState()[0],(et!==null?et.memoizedState:null)!==e&&(ze.flags|=1024),t}function qu(){var e=ms!==0;return ms=0,e}function Zu(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Yu(e){if(hs){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}hs=!1}wa=0,St=et=ze=null,Kl=!1,Ir=ms=0,Pl=null}function $t(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return St===null?ze.memoizedState=St=e:St=St.next=e,St}function _t(){if(et===null){var e=ze.alternate;e=e!==null?e.memoizedState:null}else e=et.next;var t=St===null?ze.memoizedState:St.next;if(t!==null)St=t,et=e;else{if(e===null)throw ze.alternate===null?Error(s(467)):Error(s(310));et=e,e={memoizedState:et.memoizedState,baseState:et.baseState,baseQueue:et.baseQueue,queue:et.queue,next:null},St===null?ze.memoizedState=St=e:St=St.next=e}return St}function Gu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ei(e){var t=Ir;return Ir+=1,Pl===null&&(Pl=[]),e=_h(Pl,e,t),t=ze,(St===null?t.memoizedState:St.next)===null&&(t=t.alternate,O.H=t===null||t.memoizedState===null?cm:fm),e}function vs(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ei(e);if(e.$$typeof===H)return Yt(e)}throw Error(s(438,String(e)))}function Xu(e){var t=null,n=ze.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var r=ze.alternate;r!==null&&(r=r.updateQueue,r!==null&&(r=r.memoCache,r!=null&&(t={data:r.data.map(function(o){return o.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Gu(),ze.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=Te;return t.index++,n}function ea(e,t){return typeof t=="function"?t(e):t}function ps(e){var t=_t();return Qu(t,et,e)}function Qu(e,t,n){var r=e.queue;if(r===null)throw Error(s(311));r.lastRenderedReducer=n;var o=e.baseQueue,c=r.pending;if(c!==null){if(o!==null){var m=o.next;o.next=c.next,c.next=m}t.baseQueue=o=c,r.pending=null}if(c=e.baseState,o===null)e.memoizedState=c;else{t=o.next;var y=m=null,x=null,D=t,Y=!1;do{var F=D.lane&-536870913;if(F!==D.lane?(Ge&F)===F:(wa&F)===F){var U=D.revertLane;if(U===0)x!==null&&(x=x.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),F===Gl&&(Y=!0);else if((wa&U)===U){D=D.next,U===Gl&&(Y=!0);continue}else F={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},x===null?(y=x=F,m=c):x=x.next=F,ze.lanes|=U,Na|=U;F=D.action,hl&&n(c,F),c=D.hasEagerState?D.eagerState:n(c,F)}else U={lane:F,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},x===null?(y=x=U,m=c):x=x.next=U,ze.lanes|=F,Na|=F;D=D.next}while(D!==null&&D!==t);if(x===null?m=c:x.next=y,!tn(c,e.memoizedState)&&(zt=!0,Y&&(n=Xl,n!==null)))throw n;e.memoizedState=c,e.baseState=m,e.baseQueue=x,r.lastRenderedState=c}return o===null&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Ku(e){var t=_t(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,c=t.memoizedState;if(o!==null){n.pending=null;var m=o=o.next;do c=e(c,m.action),m=m.next;while(m!==o);tn(c,t.memoizedState)||(zt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,r]}function Nh(e,t,n){var r=ze,o=_t(),c=$e;if(c){if(n===void 0)throw Error(s(407));n=n()}else n=t();var m=!tn((et||o).memoizedState,n);m&&(o.memoizedState=n,zt=!0),o=o.queue;var y=Dh.bind(null,r,o,e);if(ti(2048,8,y,[e]),o.getSnapshot!==t||m||St!==null&&St.memoizedState.tag&1){if(r.flags|=2048,Fl(9,gs(),Mh.bind(null,r,o,n,t),null),rt===null)throw Error(s(349));c||(wa&124)!==0||jh(r,t,n)}return n}function jh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ze.updateQueue,t===null?(t=Gu(),ze.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Mh(e,t,n,r){t.value=n,t.getSnapshot=r,zh(t)&&kh(e)}function Dh(e,t,n){return n(function(){zh(t)&&kh(e)})}function zh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!tn(e,n)}catch{return!0}}function kh(e){var t=Hl(e,2);t!==null&&on(t,e,2)}function Pu(e){var t=$t();if(typeof e=="function"){var n=e;if(e=n(),hl){Ye(!0);try{n()}finally{Ye(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ea,lastRenderedState:e},t}function Uh(e,t,n,r){return e.baseState=n,Qu(e,et,typeof r=="function"?r:ea)}function M0(e,t,n,r,o){if(bs(e))throw Error(s(485));if(e=t.action,e!==null){var c={payload:o,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(m){c.listeners.push(m)}};O.T!==null?n(!0):c.isTransition=!1,r(c),n=t.pending,n===null?(c.next=t.pending=c,Vh(t,c)):(c.next=n.next,t.pending=n.next=c)}}function Vh(e,t){var n=t.action,r=t.payload,o=e.state;if(t.isTransition){var c=O.T,m={};O.T=m;try{var y=n(o,r),x=O.S;x!==null&&x(m,y),Bh(e,t,y)}catch(D){Fu(e,t,D)}finally{O.T=c}}else try{c=n(o,r),Bh(e,t,c)}catch(D){Fu(e,t,D)}}function Bh(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(r){Lh(e,t,r)},function(r){return Fu(e,t,r)}):Lh(e,t,n)}function Lh(e,t,n){t.status="fulfilled",t.value=n,Hh(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Vh(e,n)))}function Fu(e,t,n){var r=e.pending;if(e.pending=null,r!==null){r=r.next;do t.status="rejected",t.reason=n,Hh(t),t=t.next;while(t!==r)}e.action=null}function Hh(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function qh(e,t){return t}function Zh(e,t){if($e){var n=rt.formState;if(n!==null){e:{var r=ze;if($e){if(ht){t:{for(var o=ht,c=zn;o.nodeType!==8;){if(!c){o=null;break t}if(o=Tn(o.nextSibling),o===null){o=null;break t}}c=o.data,o=c==="F!"||c==="F"?o:null}if(o){ht=Tn(o.nextSibling),r=o.data==="F!";break e}}ul(r)}r=!1}r&&(t=n[0])}}return n=$t(),n.memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:qh,lastRenderedState:t},n.queue=r,n=sm.bind(null,ze,r),r.dispatch=n,r=Pu(!1),c=ec.bind(null,ze,!1,r.queue),r=$t(),o={state:t,dispatch:null,action:e,pending:null},r.queue=o,n=M0.bind(null,ze,o,c,n),o.dispatch=n,r.memoizedState=e,[t,n,!1]}function Yh(e){var t=_t();return Gh(t,et,e)}function Gh(e,t,n){if(t=Qu(e,t,qh)[0],e=ps(ea)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var r=ei(t)}catch(m){throw m===Pr?cs:m}else r=t;t=_t();var o=t.queue,c=o.dispatch;return n!==t.memoizedState&&(ze.flags|=2048,Fl(9,gs(),D0.bind(null,o,n),null)),[r,c,e]}function D0(e,t){e.action=t}function Xh(e){var t=_t(),n=et;if(n!==null)return Gh(t,n,e);_t(),t=t.memoizedState,n=_t();var r=n.queue.dispatch;return n.memoizedState=e,[t,r,!1]}function Fl(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},t=ze.updateQueue,t===null&&(t=Gu(),ze.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function gs(){return{destroy:void 0,resource:void 0}}function Qh(){return _t().memoizedState}function ys(e,t,n,r){var o=$t();r=r===void 0?null:r,ze.flags|=e,o.memoizedState=Fl(1|t,gs(),n,r)}function ti(e,t,n,r){var o=_t();r=r===void 0?null:r;var c=o.memoizedState.inst;et!==null&&r!==null&&Lu(r,et.memoizedState.deps)?o.memoizedState=Fl(t,c,n,r):(ze.flags|=e,o.memoizedState=Fl(1|t,c,n,r))}function Kh(e,t){ys(8390656,8,e,t)}function Ph(e,t){ti(2048,8,e,t)}function Fh(e,t){return ti(4,2,e,t)}function $h(e,t){return ti(4,4,e,t)}function Jh(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Wh(e,t,n){n=n!=null?n.concat([e]):null,ti(4,4,Jh.bind(null,t,e),n)}function $u(){}function Ih(e,t){var n=_t();t=t===void 0?null:t;var r=n.memoizedState;return t!==null&&Lu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function em(e,t){var n=_t();t=t===void 0?null:t;var r=n.memoizedState;if(t!==null&&Lu(t,r[1]))return r[0];if(r=e(),hl){Ye(!0);try{e()}finally{Ye(!1)}}return n.memoizedState=[r,t],r}function Ju(e,t,n){return n===void 0||(wa&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=av(),ze.lanes|=e,Na|=e,n)}function tm(e,t,n,r){return tn(n,t)?n:Ql.current!==null?(e=Ju(e,n,r),tn(e,t)||(zt=!0),e):(wa&42)===0?(zt=!0,e.memoizedState=n):(e=av(),ze.lanes|=e,Na|=e,t)}function nm(e,t,n,r,o){var c=G.p;G.p=c!==0&&8>c?c:8;var m=O.T,y={};O.T=y,ec(e,!1,t,n);try{var x=o(),D=O.S;if(D!==null&&D(y,x),x!==null&&typeof x=="object"&&typeof x.then=="function"){var Y=R0(x,r);ni(e,t,Y,sn(e))}else ni(e,t,r,sn(e))}catch(F){ni(e,t,{then:function(){},status:"rejected",reason:F},sn())}finally{G.p=c,O.T=m}}function z0(){}function Wu(e,t,n,r){if(e.tag!==5)throw Error(s(476));var o=am(e).queue;nm(e,o,t,V,n===null?z0:function(){return lm(e),n(r)})}function am(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:V,baseState:V,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ea,lastRenderedState:V},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ea,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function lm(e){var t=am(e).next.queue;ni(e,t,{},sn())}function Iu(){return Yt(xi)}function rm(){return _t().memoizedState}function im(){return _t().memoizedState}function k0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=sn();e=Sa(n);var r=_a(t,e,n);r!==null&&(on(r,t,n),$r(r,t,n)),t={cache:Ru()},e.payload=t;return}t=t.return}}function U0(e,t,n){var r=sn();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},bs(e)?om(t,n):(n=bu(e,t,n,r),n!==null&&(on(n,e,r),um(n,t,r)))}function sm(e,t,n){var r=sn();ni(e,t,n,r)}function ni(e,t,n,r){var o={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(bs(e))om(t,o);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var m=t.lastRenderedState,y=c(m,n);if(o.hasEagerState=!0,o.eagerState=y,tn(y,m))return ns(e,t,o,0),rt===null&&ts(),!1}catch{}finally{}if(n=bu(e,t,o,r),n!==null)return on(n,e,r),um(n,t,r),!0}return!1}function ec(e,t,n,r){if(r={lane:2,revertLane:Mc(),action:r,hasEagerState:!1,eagerState:null,next:null},bs(e)){if(t)throw Error(s(479))}else t=bu(e,n,r,2),t!==null&&on(t,e,2)}function bs(e){var t=e.alternate;return e===ze||t!==null&&t===ze}function om(e,t){Kl=hs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function um(e,t,n){if((n&4194048)!==0){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,gd(e,n)}}var xs={readContext:Yt,use:vs,useCallback:gt,useContext:gt,useEffect:gt,useImperativeHandle:gt,useLayoutEffect:gt,useInsertionEffect:gt,useMemo:gt,useReducer:gt,useRef:gt,useState:gt,useDebugValue:gt,useDeferredValue:gt,useTransition:gt,useSyncExternalStore:gt,useId:gt,useHostTransitionStatus:gt,useFormState:gt,useActionState:gt,useOptimistic:gt,useMemoCache:gt,useCacheRefresh:gt},cm={readContext:Yt,use:vs,useCallback:function(e,t){return $t().memoizedState=[e,t===void 0?null:t],e},useContext:Yt,useEffect:Kh,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,ys(4194308,4,Jh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ys(4194308,4,e,t)},useInsertionEffect:function(e,t){ys(4,2,e,t)},useMemo:function(e,t){var n=$t();t=t===void 0?null:t;var r=e();if(hl){Ye(!0);try{e()}finally{Ye(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=$t();if(n!==void 0){var o=n(t);if(hl){Ye(!0);try{n(t)}finally{Ye(!1)}}}else o=t;return r.memoizedState=r.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},r.queue=e,e=e.dispatch=U0.bind(null,ze,e),[r.memoizedState,e]},useRef:function(e){var t=$t();return e={current:e},t.memoizedState=e},useState:function(e){e=Pu(e);var t=e.queue,n=sm.bind(null,ze,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:$u,useDeferredValue:function(e,t){var n=$t();return Ju(n,e,t)},useTransition:function(){var e=Pu(!1);return e=nm.bind(null,ze,e.queue,!0,!1),$t().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=ze,o=$t();if($e){if(n===void 0)throw Error(s(407));n=n()}else{if(n=t(),rt===null)throw Error(s(349));(Ge&124)!==0||jh(r,t,n)}o.memoizedState=n;var c={value:n,getSnapshot:t};return o.queue=c,Kh(Dh.bind(null,r,c,e),[e]),r.flags|=2048,Fl(9,gs(),Mh.bind(null,r,c,n,t),null),n},useId:function(){var e=$t(),t=rt.identifierPrefix;if($e){var n=Jn,r=$n;n=(r&~(1<<32-lt(r)-1)).toString(32)+n,t="«"+t+"R"+n,n=ms++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=N0++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Iu,useFormState:Zh,useActionState:Zh,useOptimistic:function(e){var t=$t();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=ec.bind(null,ze,!0,n),n.dispatch=t,[e,t]},useMemoCache:Xu,useCacheRefresh:function(){return $t().memoizedState=k0.bind(null,ze)}},fm={readContext:Yt,use:vs,useCallback:Ih,useContext:Yt,useEffect:Ph,useImperativeHandle:Wh,useInsertionEffect:Fh,useLayoutEffect:$h,useMemo:em,useReducer:ps,useRef:Qh,useState:function(){return ps(ea)},useDebugValue:$u,useDeferredValue:function(e,t){var n=_t();return tm(n,et.memoizedState,e,t)},useTransition:function(){var e=ps(ea)[0],t=_t().memoizedState;return[typeof e=="boolean"?e:ei(e),t]},useSyncExternalStore:Nh,useId:rm,useHostTransitionStatus:Iu,useFormState:Yh,useActionState:Yh,useOptimistic:function(e,t){var n=_t();return Uh(n,et,e,t)},useMemoCache:Xu,useCacheRefresh:im},V0={readContext:Yt,use:vs,useCallback:Ih,useContext:Yt,useEffect:Ph,useImperativeHandle:Wh,useInsertionEffect:Fh,useLayoutEffect:$h,useMemo:em,useReducer:Ku,useRef:Qh,useState:function(){return Ku(ea)},useDebugValue:$u,useDeferredValue:function(e,t){var n=_t();return et===null?Ju(n,e,t):tm(n,et.memoizedState,e,t)},useTransition:function(){var e=Ku(ea)[0],t=_t().memoizedState;return[typeof e=="boolean"?e:ei(e),t]},useSyncExternalStore:Nh,useId:rm,useHostTransitionStatus:Iu,useFormState:Xh,useActionState:Xh,useOptimistic:function(e,t){var n=_t();return et!==null?Uh(n,et,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Xu,useCacheRefresh:im},$l=null,ai=0;function Ss(e){var t=ai;return ai+=1,$l===null&&($l=[]),_h($l,e,t)}function li(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function _s(e,t){throw t.$$typeof===S?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function dm(e){var t=e._init;return t(e._payload)}function hm(e){function t(R,C){if(e){var M=R.deletions;M===null?(R.deletions=[C],R.flags|=16):M.push(C)}}function n(R,C){if(!e)return null;for(;C!==null;)t(R,C),C=C.sibling;return null}function r(R){for(var C=new Map;R!==null;)R.key!==null?C.set(R.key,R):C.set(R.index,R),R=R.sibling;return C}function o(R,C){return R=Fn(R,C),R.index=0,R.sibling=null,R}function c(R,C,M){return R.index=M,e?(M=R.alternate,M!==null?(M=M.index,M<C?(R.flags|=67108866,C):M):(R.flags|=67108866,C)):(R.flags|=1048576,C)}function m(R){return e&&R.alternate===null&&(R.flags|=67108866),R}function y(R,C,M,Q){return C===null||C.tag!==6?(C=Su(M,R.mode,Q),C.return=R,C):(C=o(C,M),C.return=R,C)}function x(R,C,M,Q){var de=M.type;return de===L?Y(R,C,M.props.children,Q,M.key):C!==null&&(C.elementType===de||typeof de=="object"&&de!==null&&de.$$typeof===J&&dm(de)===C.type)?(C=o(C,M.props),li(C,M),C.return=R,C):(C=ls(M.type,M.key,M.props,null,R.mode,Q),li(C,M),C.return=R,C)}function D(R,C,M,Q){return C===null||C.tag!==4||C.stateNode.containerInfo!==M.containerInfo||C.stateNode.implementation!==M.implementation?(C=_u(M,R.mode,Q),C.return=R,C):(C=o(C,M.children||[]),C.return=R,C)}function Y(R,C,M,Q,de){return C===null||C.tag!==7?(C=rl(M,R.mode,Q,de),C.return=R,C):(C=o(C,M),C.return=R,C)}function F(R,C,M){if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return C=Su(""+C,R.mode,M),C.return=R,C;if(typeof C=="object"&&C!==null){switch(C.$$typeof){case T:return M=ls(C.type,C.key,C.props,null,R.mode,M),li(M,C),M.return=R,M;case z:return C=_u(C,R.mode,M),C.return=R,C;case J:var Q=C._init;return C=Q(C._payload),F(R,C,M)}if(ve(C)||ne(C))return C=rl(C,R.mode,M,null),C.return=R,C;if(typeof C.then=="function")return F(R,Ss(C),M);if(C.$$typeof===H)return F(R,os(R,C),M);_s(R,C)}return null}function U(R,C,M,Q){var de=C!==null?C.key:null;if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return de!==null?null:y(R,C,""+M,Q);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case T:return M.key===de?x(R,C,M,Q):null;case z:return M.key===de?D(R,C,M,Q):null;case J:return de=M._init,M=de(M._payload),U(R,C,M,Q)}if(ve(M)||ne(M))return de!==null?null:Y(R,C,M,Q,null);if(typeof M.then=="function")return U(R,C,Ss(M),Q);if(M.$$typeof===H)return U(R,C,os(R,M),Q);_s(R,M)}return null}function B(R,C,M,Q,de){if(typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint")return R=R.get(M)||null,y(C,R,""+Q,de);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case T:return R=R.get(Q.key===null?M:Q.key)||null,x(C,R,Q,de);case z:return R=R.get(Q.key===null?M:Q.key)||null,D(C,R,Q,de);case J:var Ve=Q._init;return Q=Ve(Q._payload),B(R,C,M,Q,de)}if(ve(Q)||ne(Q))return R=R.get(M)||null,Y(C,R,Q,de,null);if(typeof Q.then=="function")return B(R,C,M,Ss(Q),de);if(Q.$$typeof===H)return B(R,C,M,os(C,Q),de);_s(C,Q)}return null}function Oe(R,C,M,Q){for(var de=null,Ve=null,_e=C,Ae=C=0,Ut=null;_e!==null&&Ae<M.length;Ae++){_e.index>Ae?(Ut=_e,_e=null):Ut=_e.sibling;var Pe=U(R,_e,M[Ae],Q);if(Pe===null){_e===null&&(_e=Ut);break}e&&_e&&Pe.alternate===null&&t(R,_e),C=c(Pe,C,Ae),Ve===null?de=Pe:Ve.sibling=Pe,Ve=Pe,_e=Ut}if(Ae===M.length)return n(R,_e),$e&&sl(R,Ae),de;if(_e===null){for(;Ae<M.length;Ae++)_e=F(R,M[Ae],Q),_e!==null&&(C=c(_e,C,Ae),Ve===null?de=_e:Ve.sibling=_e,Ve=_e);return $e&&sl(R,Ae),de}for(_e=r(_e);Ae<M.length;Ae++)Ut=B(_e,R,Ae,M[Ae],Q),Ut!==null&&(e&&Ut.alternate!==null&&_e.delete(Ut.key===null?Ae:Ut.key),C=c(Ut,C,Ae),Ve===null?de=Ut:Ve.sibling=Ut,Ve=Ut);return e&&_e.forEach(function(La){return t(R,La)}),$e&&sl(R,Ae),de}function Ee(R,C,M,Q){if(M==null)throw Error(s(151));for(var de=null,Ve=null,_e=C,Ae=C=0,Ut=null,Pe=M.next();_e!==null&&!Pe.done;Ae++,Pe=M.next()){_e.index>Ae?(Ut=_e,_e=null):Ut=_e.sibling;var La=U(R,_e,Pe.value,Q);if(La===null){_e===null&&(_e=Ut);break}e&&_e&&La.alternate===null&&t(R,_e),C=c(La,C,Ae),Ve===null?de=La:Ve.sibling=La,Ve=La,_e=Ut}if(Pe.done)return n(R,_e),$e&&sl(R,Ae),de;if(_e===null){for(;!Pe.done;Ae++,Pe=M.next())Pe=F(R,Pe.value,Q),Pe!==null&&(C=c(Pe,C,Ae),Ve===null?de=Pe:Ve.sibling=Pe,Ve=Pe);return $e&&sl(R,Ae),de}for(_e=r(_e);!Pe.done;Ae++,Pe=M.next())Pe=B(_e,R,Ae,Pe.value,Q),Pe!==null&&(e&&Pe.alternate!==null&&_e.delete(Pe.key===null?Ae:Pe.key),C=c(Pe,C,Ae),Ve===null?de=Pe:Ve.sibling=Pe,Ve=Pe);return e&&_e.forEach(function(Bx){return t(R,Bx)}),$e&&sl(R,Ae),de}function nt(R,C,M,Q){if(typeof M=="object"&&M!==null&&M.type===L&&M.key===null&&(M=M.props.children),typeof M=="object"&&M!==null){switch(M.$$typeof){case T:e:{for(var de=M.key;C!==null;){if(C.key===de){if(de=M.type,de===L){if(C.tag===7){n(R,C.sibling),Q=o(C,M.props.children),Q.return=R,R=Q;break e}}else if(C.elementType===de||typeof de=="object"&&de!==null&&de.$$typeof===J&&dm(de)===C.type){n(R,C.sibling),Q=o(C,M.props),li(Q,M),Q.return=R,R=Q;break e}n(R,C);break}else t(R,C);C=C.sibling}M.type===L?(Q=rl(M.props.children,R.mode,Q,M.key),Q.return=R,R=Q):(Q=ls(M.type,M.key,M.props,null,R.mode,Q),li(Q,M),Q.return=R,R=Q)}return m(R);case z:e:{for(de=M.key;C!==null;){if(C.key===de)if(C.tag===4&&C.stateNode.containerInfo===M.containerInfo&&C.stateNode.implementation===M.implementation){n(R,C.sibling),Q=o(C,M.children||[]),Q.return=R,R=Q;break e}else{n(R,C);break}else t(R,C);C=C.sibling}Q=_u(M,R.mode,Q),Q.return=R,R=Q}return m(R);case J:return de=M._init,M=de(M._payload),nt(R,C,M,Q)}if(ve(M))return Oe(R,C,M,Q);if(ne(M)){if(de=ne(M),typeof de!="function")throw Error(s(150));return M=de.call(M),Ee(R,C,M,Q)}if(typeof M.then=="function")return nt(R,C,Ss(M),Q);if(M.$$typeof===H)return nt(R,C,os(R,M),Q);_s(R,M)}return typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint"?(M=""+M,C!==null&&C.tag===6?(n(R,C.sibling),Q=o(C,M),Q.return=R,R=Q):(n(R,C),Q=Su(M,R.mode,Q),Q.return=R,R=Q),m(R)):n(R,C)}return function(R,C,M,Q){try{ai=0;var de=nt(R,C,M,Q);return $l=null,de}catch(_e){if(_e===Pr||_e===cs)throw _e;var Ve=nn(29,_e,null,R.mode);return Ve.lanes=Q,Ve.return=R,Ve}finally{}}}var Jl=hm(!0),mm=hm(!1),Sn=K(null),kn=null;function Ea(e){var t=e.alternate;W(Ot,Ot.current&1),W(Sn,e),kn===null&&(t===null||Ql.current!==null||t.memoizedState!==null)&&(kn=e)}function vm(e){if(e.tag===22){if(W(Ot,Ot.current),W(Sn,e),kn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(kn=e)}}else Aa()}function Aa(){W(Ot,Ot.current),W(Sn,Sn.current)}function ta(e){ae(Sn),kn===e&&(kn=null),ae(Ot)}var Ot=K(0);function ws(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Gc(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function tc(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:b({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var nc={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=sn(),o=Sa(r);o.payload=t,n!=null&&(o.callback=n),t=_a(e,o,r),t!==null&&(on(t,e,r),$r(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=sn(),o=Sa(r);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=_a(e,o,r),t!==null&&(on(t,e,r),$r(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=sn(),r=Sa(n);r.tag=2,t!=null&&(r.callback=t),t=_a(e,r,n),t!==null&&(on(t,e,n),$r(t,e,n))}};function pm(e,t,n,r,o,c,m){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,c,m):t.prototype&&t.prototype.isPureReactComponent?!Hr(n,r)||!Hr(o,c):!0}function gm(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&nc.enqueueReplaceState(t,t.state,null)}function ml(e,t){var n=t;if("ref"in t){n={};for(var r in t)r!=="ref"&&(n[r]=t[r])}if(e=e.defaultProps){n===t&&(n=b({},n));for(var o in e)n[o]===void 0&&(n[o]=e[o])}return n}var Es=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function ym(e){Es(e)}function bm(e){console.error(e)}function xm(e){Es(e)}function As(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(r){setTimeout(function(){throw r})}}function Sm(e,t,n){try{var r=e.onCaughtError;r(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function ac(e,t,n){return n=Sa(n),n.tag=3,n.payload={element:null},n.callback=function(){As(e,t)},n}function _m(e){return e=Sa(e),e.tag=3,e}function wm(e,t,n,r){var o=n.type.getDerivedStateFromError;if(typeof o=="function"){var c=r.value;e.payload=function(){return o(c)},e.callback=function(){Sm(t,n,r)}}var m=n.stateNode;m!==null&&typeof m.componentDidCatch=="function"&&(e.callback=function(){Sm(t,n,r),typeof o!="function"&&(ja===null?ja=new Set([this]):ja.add(this));var y=r.stack;this.componentDidCatch(r.value,{componentStack:y!==null?y:""})})}function B0(e,t,n,r,o){if(n.flags|=32768,r!==null&&typeof r=="object"&&typeof r.then=="function"){if(t=n.alternate,t!==null&&Xr(t,n,o,!0),n=Sn.current,n!==null){switch(n.tag){case 13:return kn===null?Tc():n.alternate===null&&mt===0&&(mt=3),n.flags&=-257,n.flags|=65536,n.lanes=o,r===Mu?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([r]):t.add(r),Rc(e,r,o)),!1;case 22:return n.flags|=65536,r===Mu?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([r]):n.add(r)),Rc(e,r,o)),!1}throw Error(s(435,n.tag))}return Rc(e,r,o),Tc(),!1}if($e)return t=Sn.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=o,r!==Au&&(e=Error(s(422),{cause:r}),Gr(gn(e,n)))):(r!==Au&&(t=Error(s(423),{cause:r}),Gr(gn(t,n))),e=e.current.alternate,e.flags|=65536,o&=-o,e.lanes|=o,r=gn(r,n),o=ac(e.stateNode,r,o),ku(e,o),mt!==4&&(mt=2)),!1;var c=Error(s(520),{cause:r});if(c=gn(c,n),fi===null?fi=[c]:fi.push(c),mt!==4&&(mt=2),t===null)return!0;r=gn(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=o&-o,n.lanes|=e,e=ac(n.stateNode,r,e),ku(n,e),!1;case 1:if(t=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(ja===null||!ja.has(c))))return n.flags|=65536,o&=-o,n.lanes|=o,o=_m(o),wm(o,e,n,r),ku(n,o),!1}n=n.return}while(n!==null);return!1}var Em=Error(s(461)),zt=!1;function Vt(e,t,n,r){t.child=e===null?mm(t,null,n,r):Jl(t,e.child,n,r)}function Am(e,t,n,r,o){n=n.render;var c=t.ref;if("ref"in r){var m={};for(var y in r)y!=="ref"&&(m[y]=r[y])}else m=r;return fl(t),r=Hu(e,t,n,m,c,o),y=qu(),e!==null&&!zt?(Zu(e,t,o),na(e,t,o)):($e&&y&&wu(t),t.flags|=1,Vt(e,t,r,o),t.child)}function Cm(e,t,n,r,o){if(e===null){var c=n.type;return typeof c=="function"&&!xu(c)&&c.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=c,Tm(e,t,c,r,o)):(e=ls(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!fc(e,o)){var m=c.memoizedProps;if(n=n.compare,n=n!==null?n:Hr,n(m,r)&&e.ref===t.ref)return na(e,t,o)}return t.flags|=1,e=Fn(c,r),e.ref=t.ref,e.return=t,t.child=e}function Tm(e,t,n,r,o){if(e!==null){var c=e.memoizedProps;if(Hr(c,r)&&e.ref===t.ref)if(zt=!1,t.pendingProps=r=c,fc(e,o))(e.flags&131072)!==0&&(zt=!0);else return t.lanes=e.lanes,na(e,t,o)}return lc(e,t,n,r,o)}function Om(e,t,n){var r=t.pendingProps,o=r.children,c=e!==null?e.memoizedState:null;if(r.mode==="hidden"){if((t.flags&128)!==0){if(r=c!==null?c.baseLanes|n:n,e!==null){for(o=t.child=e.child,c=0;o!==null;)c=c|o.lanes|o.childLanes,o=o.sibling;t.childLanes=c&~r}else t.childLanes=0,t.child=null;return Rm(e,t,r,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&us(t,c!==null?c.cachePool:null),c!==null?Th(t,c):Vu(),vm(t);else return t.lanes=t.childLanes=536870912,Rm(e,t,c!==null?c.baseLanes|n:n,n)}else c!==null?(us(t,c.cachePool),Th(t,c),Aa(),t.memoizedState=null):(e!==null&&us(t,null),Vu(),Aa());return Vt(e,t,o,n),t.child}function Rm(e,t,n,r){var o=ju();return o=o===null?null:{parent:Tt._currentValue,pool:o},t.memoizedState={baseLanes:n,cachePool:o},e!==null&&us(t,null),Vu(),vm(t),e!==null&&Xr(e,t,r,!0),null}function Cs(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(s(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function lc(e,t,n,r,o){return fl(t),n=Hu(e,t,n,r,void 0,o),r=qu(),e!==null&&!zt?(Zu(e,t,o),na(e,t,o)):($e&&r&&wu(t),t.flags|=1,Vt(e,t,n,o),t.child)}function Nm(e,t,n,r,o,c){return fl(t),t.updateQueue=null,n=Rh(t,r,n,o),Oh(e),r=qu(),e!==null&&!zt?(Zu(e,t,c),na(e,t,c)):($e&&r&&wu(t),t.flags|=1,Vt(e,t,n,c),t.child)}function jm(e,t,n,r,o){if(fl(t),t.stateNode===null){var c=ql,m=n.contextType;typeof m=="object"&&m!==null&&(c=Yt(m)),c=new n(r,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=nc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=r,c.state=t.memoizedState,c.refs={},Du(t),m=n.contextType,c.context=typeof m=="object"&&m!==null?Yt(m):ql,c.state=t.memoizedState,m=n.getDerivedStateFromProps,typeof m=="function"&&(tc(t,n,m,r),c.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(m=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),m!==c.state&&nc.enqueueReplaceState(c,c.state,null),Wr(t,r,c,o),Jr(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),r=!0}else if(e===null){c=t.stateNode;var y=t.memoizedProps,x=ml(n,y);c.props=x;var D=c.context,Y=n.contextType;m=ql,typeof Y=="object"&&Y!==null&&(m=Yt(Y));var F=n.getDerivedStateFromProps;Y=typeof F=="function"||typeof c.getSnapshotBeforeUpdate=="function",y=t.pendingProps!==y,Y||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(y||D!==m)&&gm(t,c,r,m),xa=!1;var U=t.memoizedState;c.state=U,Wr(t,r,c,o),Jr(),D=t.memoizedState,y||U!==D||xa?(typeof F=="function"&&(tc(t,n,F,r),D=t.memoizedState),(x=xa||pm(t,n,x,r,U,D,m))?(Y||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=D),c.props=r,c.state=D,c.context=m,r=x):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{c=t.stateNode,zu(e,t),m=t.memoizedProps,Y=ml(n,m),c.props=Y,F=t.pendingProps,U=c.context,D=n.contextType,x=ql,typeof D=="object"&&D!==null&&(x=Yt(D)),y=n.getDerivedStateFromProps,(D=typeof y=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(m!==F||U!==x)&&gm(t,c,r,x),xa=!1,U=t.memoizedState,c.state=U,Wr(t,r,c,o),Jr();var B=t.memoizedState;m!==F||U!==B||xa||e!==null&&e.dependencies!==null&&ss(e.dependencies)?(typeof y=="function"&&(tc(t,n,y,r),B=t.memoizedState),(Y=xa||pm(t,n,Y,r,U,B,x)||e!==null&&e.dependencies!==null&&ss(e.dependencies))?(D||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(r,B,x),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(r,B,x)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||m===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=B),c.props=r,c.state=B,c.context=x,r=Y):(typeof c.componentDidUpdate!="function"||m===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),r=!1)}return c=r,Cs(e,t),r=(t.flags&128)!==0,c||r?(c=t.stateNode,n=r&&typeof n.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&r?(t.child=Jl(t,e.child,null,o),t.child=Jl(t,null,n,o)):Vt(e,t,n,o),t.memoizedState=c.state,e=t.child):e=na(e,t,o),e}function Mm(e,t,n,r){return Yr(),t.flags|=256,Vt(e,t,n,r),t.child}var rc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ic(e){return{baseLanes:e,cachePool:bh()}}function sc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=_n),e}function Dm(e,t,n){var r=t.pendingProps,o=!1,c=(t.flags&128)!==0,m;if((m=c)||(m=e!==null&&e.memoizedState===null?!1:(Ot.current&2)!==0),m&&(o=!0,t.flags&=-129),m=(t.flags&32)!==0,t.flags&=-33,e===null){if($e){if(o?Ea(t):Aa(),$e){var y=ht,x;if(x=y){e:{for(x=y,y=zn;x.nodeType!==8;){if(!y){y=null;break e}if(x=Tn(x.nextSibling),x===null){y=null;break e}}y=x}y!==null?(t.memoizedState={dehydrated:y,treeContext:il!==null?{id:$n,overflow:Jn}:null,retryLane:536870912,hydrationErrors:null},x=nn(18,null,null,0),x.stateNode=y,x.return=t,t.child=x,Xt=t,ht=null,x=!0):x=!1}x||ul(t)}if(y=t.memoizedState,y!==null&&(y=y.dehydrated,y!==null))return Gc(y)?t.lanes=32:t.lanes=536870912,null;ta(t)}return y=r.children,r=r.fallback,o?(Aa(),o=t.mode,y=Ts({mode:"hidden",children:y},o),r=rl(r,o,n,null),y.return=t,r.return=t,y.sibling=r,t.child=y,o=t.child,o.memoizedState=ic(n),o.childLanes=sc(e,m,n),t.memoizedState=rc,r):(Ea(t),oc(t,y))}if(x=e.memoizedState,x!==null&&(y=x.dehydrated,y!==null)){if(c)t.flags&256?(Ea(t),t.flags&=-257,t=uc(e,t,n)):t.memoizedState!==null?(Aa(),t.child=e.child,t.flags|=128,t=null):(Aa(),o=r.fallback,y=t.mode,r=Ts({mode:"visible",children:r.children},y),o=rl(o,y,n,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,Jl(t,e.child,null,n),r=t.child,r.memoizedState=ic(n),r.childLanes=sc(e,m,n),t.memoizedState=rc,t=o);else if(Ea(t),Gc(y)){if(m=y.nextSibling&&y.nextSibling.dataset,m)var D=m.dgst;m=D,r=Error(s(419)),r.stack="",r.digest=m,Gr({value:r,source:null,stack:null}),t=uc(e,t,n)}else if(zt||Xr(e,t,n,!1),m=(n&e.childLanes)!==0,zt||m){if(m=rt,m!==null&&(r=n&-n,r=(r&42)!==0?1:Xo(r),r=(r&(m.suspendedLanes|n))!==0?0:r,r!==0&&r!==x.retryLane))throw x.retryLane=r,Hl(e,r),on(m,e,r),Em;y.data==="$?"||Tc(),t=uc(e,t,n)}else y.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=x.treeContext,ht=Tn(y.nextSibling),Xt=t,$e=!0,ol=null,zn=!1,e!==null&&(bn[xn++]=$n,bn[xn++]=Jn,bn[xn++]=il,$n=e.id,Jn=e.overflow,il=t),t=oc(t,r.children),t.flags|=4096);return t}return o?(Aa(),o=r.fallback,y=t.mode,x=e.child,D=x.sibling,r=Fn(x,{mode:"hidden",children:r.children}),r.subtreeFlags=x.subtreeFlags&65011712,D!==null?o=Fn(D,o):(o=rl(o,y,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,y=e.child.memoizedState,y===null?y=ic(n):(x=y.cachePool,x!==null?(D=Tt._currentValue,x=x.parent!==D?{parent:D,pool:D}:x):x=bh(),y={baseLanes:y.baseLanes|n,cachePool:x}),o.memoizedState=y,o.childLanes=sc(e,m,n),t.memoizedState=rc,r):(Ea(t),n=e.child,e=n.sibling,n=Fn(n,{mode:"visible",children:r.children}),n.return=t,n.sibling=null,e!==null&&(m=t.deletions,m===null?(t.deletions=[e],t.flags|=16):m.push(e)),t.child=n,t.memoizedState=null,n)}function oc(e,t){return t=Ts({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Ts(e,t){return e=nn(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function uc(e,t,n){return Jl(t,e.child,null,n),e=oc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function zm(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Tu(e.return,t,n)}function cc(e,t,n,r,o){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=r,c.tail=n,c.tailMode=o)}function km(e,t,n){var r=t.pendingProps,o=r.revealOrder,c=r.tail;if(Vt(e,t,r.children,n),r=Ot.current,(r&2)!==0)r=r&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&zm(e,n,t);else if(e.tag===19)zm(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(W(Ot,r),o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&ws(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),cc(t,!1,o,n,c);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&ws(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}cc(t,!0,n,null,c);break;case"together":cc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function na(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Na|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Xr(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,n=Fn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Fn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function fc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ss(e)))}function L0(e,t,n){switch(t.tag){case 3:we(t,t.stateNode.containerInfo),ba(t,Tt,e.memoizedState.cache),Yr();break;case 27:case 5:Ue(t);break;case 4:we(t,t.stateNode.containerInfo);break;case 10:ba(t,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(r!==null)return r.dehydrated!==null?(Ea(t),t.flags|=128,null):(n&t.child.childLanes)!==0?Dm(e,t,n):(Ea(t),e=na(e,t,n),e!==null?e.sibling:null);Ea(t);break;case 19:var o=(e.flags&128)!==0;if(r=(n&t.childLanes)!==0,r||(Xr(e,t,n,!1),r=(n&t.childLanes)!==0),o){if(r)return km(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),W(Ot,Ot.current),r)break;return null;case 22:case 23:return t.lanes=0,Om(e,t,n);case 24:ba(t,Tt,e.memoizedState.cache)}return na(e,t,n)}function Um(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)zt=!0;else{if(!fc(e,n)&&(t.flags&128)===0)return zt=!1,L0(e,t,n);zt=(e.flags&131072)!==0}else zt=!1,$e&&(t.flags&1048576)!==0&&dh(t,is,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,o=r._init;if(r=o(r._payload),t.type=r,typeof r=="function")xu(r)?(e=ml(r,e),t.tag=1,t=jm(null,t,r,e,n)):(t.tag=0,t=lc(null,t,r,e,n));else{if(r!=null){if(o=r.$$typeof,o===P){t.tag=11,t=Am(null,t,r,e,n);break e}else if(o===$){t.tag=14,t=Cm(null,t,r,e,n);break e}}throw t=fe(r)||r,Error(s(306,t,""))}}return t;case 0:return lc(e,t,t.type,t.pendingProps,n);case 1:return r=t.type,o=ml(r,t.pendingProps),jm(e,t,r,o,n);case 3:e:{if(we(t,t.stateNode.containerInfo),e===null)throw Error(s(387));r=t.pendingProps;var c=t.memoizedState;o=c.element,zu(e,t),Wr(t,r,null,n);var m=t.memoizedState;if(r=m.cache,ba(t,Tt,r),r!==c.cache&&Ou(t,[Tt],n,!0),Jr(),r=m.element,c.isDehydrated)if(c={element:r,isDehydrated:!1,cache:m.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Mm(e,t,r,n);break e}else if(r!==o){o=gn(Error(s(424)),t),Gr(o),t=Mm(e,t,r,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(ht=Tn(e.firstChild),Xt=t,$e=!0,ol=null,zn=!0,n=mm(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Yr(),r===o){t=na(e,t,n);break e}Vt(e,t,r,n)}t=t.child}return t;case 26:return Cs(e,t),e===null?(n=Hv(t.type,null,t.pendingProps,null))?t.memoizedState=n:$e||(n=t.type,e=t.pendingProps,r=qs(Se.current).createElement(n),r[Zt]=t,r[Pt]=e,Lt(r,n,e),Dt(r),t.stateNode=r):t.memoizedState=Hv(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Ue(t),e===null&&$e&&(r=t.stateNode=Vv(t.type,t.pendingProps,Se.current),Xt=t,zn=!0,o=ht,za(t.type)?(Xc=o,ht=Tn(r.firstChild)):ht=o),Vt(e,t,t.pendingProps.children,n),Cs(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&$e&&((o=r=ht)&&(r=hx(r,t.type,t.pendingProps,zn),r!==null?(t.stateNode=r,Xt=t,ht=Tn(r.firstChild),zn=!1,o=!0):o=!1),o||ul(t)),Ue(t),o=t.type,c=t.pendingProps,m=e!==null?e.memoizedProps:null,r=c.children,qc(o,c)?r=null:m!==null&&qc(o,m)&&(t.flags|=32),t.memoizedState!==null&&(o=Hu(e,t,j0,null,null,n),xi._currentValue=o),Cs(e,t),Vt(e,t,r,n),t.child;case 6:return e===null&&$e&&((e=n=ht)&&(n=mx(n,t.pendingProps,zn),n!==null?(t.stateNode=n,Xt=t,ht=null,e=!0):e=!1),e||ul(t)),null;case 13:return Dm(e,t,n);case 4:return we(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Jl(t,null,r,n):Vt(e,t,r,n),t.child;case 11:return Am(e,t,t.type,t.pendingProps,n);case 7:return Vt(e,t,t.pendingProps,n),t.child;case 8:return Vt(e,t,t.pendingProps.children,n),t.child;case 12:return Vt(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ba(t,t.type,r.value),Vt(e,t,r.children,n),t.child;case 9:return o=t.type._context,r=t.pendingProps.children,fl(t),o=Yt(o),r=r(o),t.flags|=1,Vt(e,t,r,n),t.child;case 14:return Cm(e,t,t.type,t.pendingProps,n);case 15:return Tm(e,t,t.type,t.pendingProps,n);case 19:return km(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},e===null?(n=Ts(r,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=Fn(e.child,r),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Om(e,t,n);case 24:return fl(t),r=Yt(Tt),e===null?(o=ju(),o===null&&(o=rt,c=Ru(),o.pooledCache=c,c.refCount++,c!==null&&(o.pooledCacheLanes|=n),o=c),t.memoizedState={parent:r,cache:o},Du(t),ba(t,Tt,o)):((e.lanes&n)!==0&&(zu(e,t),Wr(t,null,null,n),Jr()),o=e.memoizedState,c=t.memoizedState,o.parent!==r?(o={parent:r,cache:r},t.memoizedState=o,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=o),ba(t,Tt,r)):(r=c.cache,ba(t,Tt,r),r!==o.cache&&Ou(t,[Tt],n,!0))),Vt(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function aa(e){e.flags|=4}function Vm(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Xv(t)){if(t=Sn.current,t!==null&&((Ge&4194048)===Ge?kn!==null:(Ge&62914560)!==Ge&&(Ge&536870912)===0||t!==kn))throw Fr=Mu,xh;e.flags|=8192}}function Os(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?vd():536870912,e.lanes|=t,tr|=t)}function ri(e,t){if(!$e)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function st(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&65011712,r|=o.flags&65011712,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function H0(e,t,n){var r=t.pendingProps;switch(Eu(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return st(t),null;case 1:return st(t),null;case 3:return n=t.stateNode,r=null,e!==null&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),In(Tt),Xe(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Zr(t)?aa(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,vh())),st(t),null;case 26:return n=t.memoizedState,e===null?(aa(t),n!==null?(st(t),Vm(t,n)):(st(t),t.flags&=-16777217)):n?n!==e.memoizedState?(aa(t),st(t),Vm(t,n)):(st(t),t.flags&=-16777217):(e.memoizedProps!==r&&aa(t),st(t),t.flags&=-16777217),null;case 27:Ze(t),n=Se.current;var o=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==r&&aa(t);else{if(!r){if(t.stateNode===null)throw Error(s(166));return st(t),null}e=re.current,Zr(t)?hh(t):(e=Vv(o,r,n),t.stateNode=e,aa(t))}return st(t),null;case 5:if(Ze(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==r&&aa(t);else{if(!r){if(t.stateNode===null)throw Error(s(166));return st(t),null}if(e=re.current,Zr(t))hh(t);else{switch(o=qs(Se.current),e){case 1:e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof r.is=="string"?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e=typeof r.is=="string"?o.createElement(n,{is:r.is}):o.createElement(n)}}e[Zt]=t,e[Pt]=r;e:for(o=t.child;o!==null;){if(o.tag===5||o.tag===6)e.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===t)break e;for(;o.sibling===null;){if(o.return===null||o.return===t)break e;o=o.return}o.sibling.return=o.return,o=o.sibling}t.stateNode=e;e:switch(Lt(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&aa(t)}}return st(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==r&&aa(t);else{if(typeof r!="string"&&t.stateNode===null)throw Error(s(166));if(e=Se.current,Zr(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,o=Xt,o!==null)switch(o.tag){case 27:case 5:r=o.memoizedProps}e[Zt]=t,e=!!(e.nodeValue===n||r!==null&&r.suppressHydrationWarning===!0||Nv(e.nodeValue,n)),e||ul(t)}else e=qs(e).createTextNode(r),e[Zt]=t,t.stateNode=e}return st(t),null;case 13:if(r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(o=Zr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(s(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(s(317));o[Zt]=t}else Yr(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;st(t),o=!1}else o=vh(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=o),o=!0;if(!o)return t.flags&256?(ta(t),t):(ta(t),null)}if(ta(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=r!==null,e=e!==null&&e.memoizedState!==null,n){r=t.child,o=null,r.alternate!==null&&r.alternate.memoizedState!==null&&r.alternate.memoizedState.cachePool!==null&&(o=r.alternate.memoizedState.cachePool.pool);var c=null;r.memoizedState!==null&&r.memoizedState.cachePool!==null&&(c=r.memoizedState.cachePool.pool),c!==o&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Os(t,t.updateQueue),st(t),null;case 4:return Xe(),e===null&&Uc(t.stateNode.containerInfo),st(t),null;case 10:return In(t.type),st(t),null;case 19:if(ae(Ot),o=t.memoizedState,o===null)return st(t),null;if(r=(t.flags&128)!==0,c=o.rendering,c===null)if(r)ri(o,!1);else{if(mt!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=ws(e),c!==null){for(t.flags|=128,ri(o,!1),e=c.updateQueue,t.updateQueue=e,Os(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)fh(n,e),n=n.sibling;return W(Ot,Ot.current&1|2),t.child}e=e.sibling}o.tail!==null&&qt()>js&&(t.flags|=128,r=!0,ri(o,!1),t.lanes=4194304)}else{if(!r)if(e=ws(c),e!==null){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,Os(t,e),ri(o,!0),o.tail===null&&o.tailMode==="hidden"&&!c.alternate&&!$e)return st(t),null}else 2*qt()-o.renderingStartTime>js&&n!==536870912&&(t.flags|=128,r=!0,ri(o,!1),t.lanes=4194304);o.isBackwards?(c.sibling=t.child,t.child=c):(e=o.last,e!==null?e.sibling=c:t.child=c,o.last=c)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=qt(),t.sibling=null,e=Ot.current,W(Ot,r?e&1|2:e&1),t):(st(t),null);case 22:case 23:return ta(t),Bu(),r=t.memoizedState!==null,e!==null?e.memoizedState!==null!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?(n&536870912)!==0&&(t.flags&128)===0&&(st(t),t.subtreeFlags&6&&(t.flags|=8192)):st(t),n=t.updateQueue,n!==null&&Os(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),r=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),e!==null&&ae(dl),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),In(Tt),st(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function q0(e,t){switch(Eu(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return In(Tt),Xe(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Ze(t),null;case 13:if(ta(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Yr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ae(Ot),null;case 4:return Xe(),null;case 10:return In(t.type),null;case 22:case 23:return ta(t),Bu(),e!==null&&ae(dl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return In(Tt),null;case 25:return null;default:return null}}function Bm(e,t){switch(Eu(t),t.tag){case 3:In(Tt),Xe();break;case 26:case 27:case 5:Ze(t);break;case 4:Xe();break;case 13:ta(t);break;case 19:ae(Ot);break;case 10:In(t.type);break;case 22:case 23:ta(t),Bu(),e!==null&&ae(dl);break;case 24:In(Tt)}}function ii(e,t){try{var n=t.updateQueue,r=n!==null?n.lastEffect:null;if(r!==null){var o=r.next;n=o;do{if((n.tag&e)===e){r=void 0;var c=n.create,m=n.inst;r=c(),m.destroy=r}n=n.next}while(n!==o)}}catch(y){at(t,t.return,y)}}function Ca(e,t,n){try{var r=t.updateQueue,o=r!==null?r.lastEffect:null;if(o!==null){var c=o.next;r=c;do{if((r.tag&e)===e){var m=r.inst,y=m.destroy;if(y!==void 0){m.destroy=void 0,o=t;var x=n,D=y;try{D()}catch(Y){at(o,x,Y)}}}r=r.next}while(r!==c)}}catch(Y){at(t,t.return,Y)}}function Lm(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{Ch(t,n)}catch(r){at(e,e.return,r)}}}function Hm(e,t,n){n.props=ml(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){at(e,t,r)}}function si(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;case 30:r=e.stateNode;break;default:r=e.stateNode}typeof n=="function"?e.refCleanup=n(r):n.current=r}}catch(o){at(e,t,o)}}function Un(e,t){var n=e.ref,r=e.refCleanup;if(n!==null)if(typeof r=="function")try{r()}catch(o){at(e,t,o)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(o){at(e,t,o)}else n.current=null}function qm(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(o){at(e,e.return,o)}}function dc(e,t,n){try{var r=e.stateNode;ox(r,e.type,n,t),r[Pt]=t}catch(o){at(e,e.return,o)}}function Zm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&za(e.type)||e.tag===4}function hc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Zm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&za(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function mc(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Hs));else if(r!==4&&(r===27&&za(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(mc(e,t,n),e=e.sibling;e!==null;)mc(e,t,n),e=e.sibling}function Rs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(r===27&&za(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(Rs(e,t,n),e=e.sibling;e!==null;)Rs(e,t,n),e=e.sibling}function Ym(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,o=t.attributes;o.length;)t.removeAttributeNode(o[0]);Lt(t,r,n),t[Zt]=e,t[Pt]=n}catch(c){at(e,e.return,c)}}var la=!1,yt=!1,vc=!1,Gm=typeof WeakSet=="function"?WeakSet:Set,kt=null;function Z0(e,t){if(e=e.containerInfo,Lc=Ks,e=th(e),hu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,c=r.focusNode;r=r.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var m=0,y=-1,x=-1,D=0,Y=0,F=e,U=null;t:for(;;){for(var B;F!==n||o!==0&&F.nodeType!==3||(y=m+o),F!==c||r!==0&&F.nodeType!==3||(x=m+r),F.nodeType===3&&(m+=F.nodeValue.length),(B=F.firstChild)!==null;)U=F,F=B;for(;;){if(F===e)break t;if(U===n&&++D===o&&(y=m),U===c&&++Y===r&&(x=m),(B=F.nextSibling)!==null)break;F=U,U=F.parentNode}F=B}n=y===-1||x===-1?null:{start:y,end:x}}else n=null}n=n||{start:0,end:0}}else n=null;for(Hc={focusedElem:e,selectionRange:n},Ks=!1,kt=t;kt!==null;)if(t=kt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,kt=e;else for(;kt!==null;){switch(t=kt,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,n=t,o=c.memoizedProps,c=c.memoizedState,r=n.stateNode;try{var Oe=ml(n.type,o,n.elementType===n.type);e=r.getSnapshotBeforeUpdate(Oe,c),r.__reactInternalSnapshotBeforeUpdate=e}catch(Ee){at(n,n.return,Ee)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Yc(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Yc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,kt=e;break}kt=t.return}}function Xm(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Ta(e,n),r&4&&ii(5,n);break;case 1:if(Ta(e,n),r&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(m){at(n,n.return,m)}else{var o=ml(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(m){at(n,n.return,m)}}r&64&&Lm(n),r&512&&si(n,n.return);break;case 3:if(Ta(e,n),r&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{Ch(e,t)}catch(m){at(n,n.return,m)}}break;case 27:t===null&&r&4&&Ym(n);case 26:case 5:Ta(e,n),t===null&&r&4&&qm(n),r&512&&si(n,n.return);break;case 12:Ta(e,n);break;case 13:Ta(e,n),r&4&&Pm(e,n),r&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=J0.bind(null,n),vx(e,n))));break;case 22:if(r=n.memoizedState!==null||la,!r){t=t!==null&&t.memoizedState!==null||yt,o=la;var c=yt;la=r,(yt=t)&&!c?Oa(e,n,(n.subtreeFlags&8772)!==0):Ta(e,n),la=o,yt=c}break;case 30:break;default:Ta(e,n)}}function Qm(e){var t=e.alternate;t!==null&&(e.alternate=null,Qm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Po(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var it=null,Jt=!1;function ra(e,t,n){for(n=n.child;n!==null;)Km(e,t,n),n=n.sibling}function Km(e,t,n){if(Me&&typeof Me.onCommitFiberUnmount=="function")try{Me.onCommitFiberUnmount(be,n)}catch{}switch(n.tag){case 26:yt||Un(n,t),ra(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:yt||Un(n,t);var r=it,o=Jt;za(n.type)&&(it=n.stateNode,Jt=!1),ra(e,t,n),pi(n.stateNode),it=r,Jt=o;break;case 5:yt||Un(n,t);case 6:if(r=it,o=Jt,it=null,ra(e,t,n),it=r,Jt=o,it!==null)if(Jt)try{(it.nodeType===9?it.body:it.nodeName==="HTML"?it.ownerDocument.body:it).removeChild(n.stateNode)}catch(c){at(n,t,c)}else try{it.removeChild(n.stateNode)}catch(c){at(n,t,c)}break;case 18:it!==null&&(Jt?(e=it,kv(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Ei(e)):kv(it,n.stateNode));break;case 4:r=it,o=Jt,it=n.stateNode.containerInfo,Jt=!0,ra(e,t,n),it=r,Jt=o;break;case 0:case 11:case 14:case 15:yt||Ca(2,n,t),yt||Ca(4,n,t),ra(e,t,n);break;case 1:yt||(Un(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"&&Hm(n,t,r)),ra(e,t,n);break;case 21:ra(e,t,n);break;case 22:yt=(r=yt)||n.memoizedState!==null,ra(e,t,n),yt=r;break;default:ra(e,t,n)}}function Pm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Ei(e)}catch(n){at(t,t.return,n)}}function Y0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Gm),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Gm),t;default:throw Error(s(435,e.tag))}}function pc(e,t){var n=Y0(e);t.forEach(function(r){var o=W0.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}function an(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r],c=e,m=t,y=m;e:for(;y!==null;){switch(y.tag){case 27:if(za(y.type)){it=y.stateNode,Jt=!1;break e}break;case 5:it=y.stateNode,Jt=!1;break e;case 3:case 4:it=y.stateNode.containerInfo,Jt=!0;break e}y=y.return}if(it===null)throw Error(s(160));Km(c,m,o),it=null,Jt=!1,c=o.alternate,c!==null&&(c.return=null),o.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Fm(t,e),t=t.sibling}var Cn=null;function Fm(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:an(t,e),ln(e),r&4&&(Ca(3,e,e.return),ii(3,e),Ca(5,e,e.return));break;case 1:an(t,e),ln(e),r&512&&(yt||n===null||Un(n,n.return)),r&64&&la&&(e=e.updateQueue,e!==null&&(r=e.callbacks,r!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?r:n.concat(r))));break;case 26:var o=Cn;if(an(t,e),ln(e),r&512&&(yt||n===null||Un(n,n.return)),r&4){var c=n!==null?n.memoizedState:null;if(r=e.memoizedState,n===null)if(r===null)if(e.stateNode===null){e:{r=e.type,n=e.memoizedProps,o=o.ownerDocument||o;t:switch(r){case"title":c=o.getElementsByTagName("title")[0],(!c||c[jr]||c[Zt]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=o.createElement(r),o.head.insertBefore(c,o.querySelector("head > title"))),Lt(c,r,n),c[Zt]=e,Dt(c),r=c;break e;case"link":var m=Yv("link","href",o).get(r+(n.href||""));if(m){for(var y=0;y<m.length;y++)if(c=m[y],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){m.splice(y,1);break t}}c=o.createElement(r),Lt(c,r,n),o.head.appendChild(c);break;case"meta":if(m=Yv("meta","content",o).get(r+(n.content||""))){for(y=0;y<m.length;y++)if(c=m[y],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){m.splice(y,1);break t}}c=o.createElement(r),Lt(c,r,n),o.head.appendChild(c);break;default:throw Error(s(468,r))}c[Zt]=e,Dt(c),r=c}e.stateNode=r}else Gv(o,e.type,e.stateNode);else e.stateNode=Zv(o,r,e.memoizedProps);else c!==r?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,r===null?Gv(o,e.type,e.stateNode):Zv(o,r,e.memoizedProps)):r===null&&e.stateNode!==null&&dc(e,e.memoizedProps,n.memoizedProps)}break;case 27:an(t,e),ln(e),r&512&&(yt||n===null||Un(n,n.return)),n!==null&&r&4&&dc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(an(t,e),ln(e),r&512&&(yt||n===null||Un(n,n.return)),e.flags&32){o=e.stateNode;try{Dl(o,"")}catch(B){at(e,e.return,B)}}r&4&&e.stateNode!=null&&(o=e.memoizedProps,dc(e,o,n!==null?n.memoizedProps:o)),r&1024&&(vc=!0);break;case 6:if(an(t,e),ln(e),r&4){if(e.stateNode===null)throw Error(s(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(B){at(e,e.return,B)}}break;case 3:if(Gs=null,o=Cn,Cn=Zs(t.containerInfo),an(t,e),Cn=o,ln(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ei(t.containerInfo)}catch(B){at(e,e.return,B)}vc&&(vc=!1,$m(e));break;case 4:r=Cn,Cn=Zs(e.stateNode.containerInfo),an(t,e),ln(e),Cn=r;break;case 12:an(t,e),ln(e);break;case 13:an(t,e),ln(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(_c=qt()),r&4&&(r=e.updateQueue,r!==null&&(e.updateQueue=null,pc(e,r)));break;case 22:o=e.memoizedState!==null;var x=n!==null&&n.memoizedState!==null,D=la,Y=yt;if(la=D||o,yt=Y||x,an(t,e),yt=Y,la=D,ln(e),r&8192)e:for(t=e.stateNode,t._visibility=o?t._visibility&-2:t._visibility|1,o&&(n===null||x||la||yt||vl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){x=n=t;try{if(c=x.stateNode,o)m=c.style,typeof m.setProperty=="function"?m.setProperty("display","none","important"):m.display="none";else{y=x.stateNode;var F=x.memoizedProps.style,U=F!=null&&F.hasOwnProperty("display")?F.display:null;y.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(B){at(x,x.return,B)}}}else if(t.tag===6){if(n===null){x=t;try{x.stateNode.nodeValue=o?"":x.memoizedProps}catch(B){at(x,x.return,B)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}r&4&&(r=e.updateQueue,r!==null&&(n=r.retryQueue,n!==null&&(r.retryQueue=null,pc(e,n))));break;case 19:an(t,e),ln(e),r&4&&(r=e.updateQueue,r!==null&&(e.updateQueue=null,pc(e,r)));break;case 30:break;case 21:break;default:an(t,e),ln(e)}}function ln(e){var t=e.flags;if(t&2){try{for(var n,r=e.return;r!==null;){if(Zm(r)){n=r;break}r=r.return}if(n==null)throw Error(s(160));switch(n.tag){case 27:var o=n.stateNode,c=hc(e);Rs(e,c,o);break;case 5:var m=n.stateNode;n.flags&32&&(Dl(m,""),n.flags&=-33);var y=hc(e);Rs(e,y,m);break;case 3:case 4:var x=n.stateNode.containerInfo,D=hc(e);mc(e,D,x);break;default:throw Error(s(161))}}catch(Y){at(e,e.return,Y)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function $m(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;$m(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Ta(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Xm(e,t.alternate,t),t=t.sibling}function vl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ca(4,t,t.return),vl(t);break;case 1:Un(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Hm(t,t.return,n),vl(t);break;case 27:pi(t.stateNode);case 26:case 5:Un(t,t.return),vl(t);break;case 22:t.memoizedState===null&&vl(t);break;case 30:vl(t);break;default:vl(t)}e=e.sibling}}function Oa(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var r=t.alternate,o=e,c=t,m=c.flags;switch(c.tag){case 0:case 11:case 15:Oa(o,c,n),ii(4,c);break;case 1:if(Oa(o,c,n),r=c,o=r.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(D){at(r,r.return,D)}if(r=c,o=r.updateQueue,o!==null){var y=r.stateNode;try{var x=o.shared.hiddenCallbacks;if(x!==null)for(o.shared.hiddenCallbacks=null,o=0;o<x.length;o++)Ah(x[o],y)}catch(D){at(r,r.return,D)}}n&&m&64&&Lm(c),si(c,c.return);break;case 27:Ym(c);case 26:case 5:Oa(o,c,n),n&&r===null&&m&4&&qm(c),si(c,c.return);break;case 12:Oa(o,c,n);break;case 13:Oa(o,c,n),n&&m&4&&Pm(o,c);break;case 22:c.memoizedState===null&&Oa(o,c,n),si(c,c.return);break;case 30:break;default:Oa(o,c,n)}t=t.sibling}}function gc(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Qr(n))}function yc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Qr(e))}function Vn(e,t,n,r){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Jm(e,t,n,r),t=t.sibling}function Jm(e,t,n,r){var o=t.flags;switch(t.tag){case 0:case 11:case 15:Vn(e,t,n,r),o&2048&&ii(9,t);break;case 1:Vn(e,t,n,r);break;case 3:Vn(e,t,n,r),o&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Qr(e)));break;case 12:if(o&2048){Vn(e,t,n,r),e=t.stateNode;try{var c=t.memoizedProps,m=c.id,y=c.onPostCommit;typeof y=="function"&&y(m,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(x){at(t,t.return,x)}}else Vn(e,t,n,r);break;case 13:Vn(e,t,n,r);break;case 23:break;case 22:c=t.stateNode,m=t.alternate,t.memoizedState!==null?c._visibility&2?Vn(e,t,n,r):oi(e,t):c._visibility&2?Vn(e,t,n,r):(c._visibility|=2,Wl(e,t,n,r,(t.subtreeFlags&10256)!==0)),o&2048&&gc(m,t);break;case 24:Vn(e,t,n,r),o&2048&&yc(t.alternate,t);break;default:Vn(e,t,n,r)}}function Wl(e,t,n,r,o){for(o=o&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,m=t,y=n,x=r,D=m.flags;switch(m.tag){case 0:case 11:case 15:Wl(c,m,y,x,o),ii(8,m);break;case 23:break;case 22:var Y=m.stateNode;m.memoizedState!==null?Y._visibility&2?Wl(c,m,y,x,o):oi(c,m):(Y._visibility|=2,Wl(c,m,y,x,o)),o&&D&2048&&gc(m.alternate,m);break;case 24:Wl(c,m,y,x,o),o&&D&2048&&yc(m.alternate,m);break;default:Wl(c,m,y,x,o)}t=t.sibling}}function oi(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,r=t,o=r.flags;switch(r.tag){case 22:oi(n,r),o&2048&&gc(r.alternate,r);break;case 24:oi(n,r),o&2048&&yc(r.alternate,r);break;default:oi(n,r)}t=t.sibling}}var ui=8192;function Il(e){if(e.subtreeFlags&ui)for(e=e.child;e!==null;)Wm(e),e=e.sibling}function Wm(e){switch(e.tag){case 26:Il(e),e.flags&ui&&e.memoizedState!==null&&Ox(Cn,e.memoizedState,e.memoizedProps);break;case 5:Il(e);break;case 3:case 4:var t=Cn;Cn=Zs(e.stateNode.containerInfo),Il(e),Cn=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=ui,ui=16777216,Il(e),ui=t):Il(e));break;default:Il(e)}}function Im(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function ci(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var r=t[n];kt=r,tv(r,e)}Im(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ev(e),e=e.sibling}function ev(e){switch(e.tag){case 0:case 11:case 15:ci(e),e.flags&2048&&Ca(9,e,e.return);break;case 3:ci(e);break;case 12:ci(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Ns(e)):ci(e);break;default:ci(e)}}function Ns(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var r=t[n];kt=r,tv(r,e)}Im(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Ca(8,t,t.return),Ns(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Ns(t));break;default:Ns(t)}e=e.sibling}}function tv(e,t){for(;kt!==null;){var n=kt;switch(n.tag){case 0:case 11:case 15:Ca(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var r=n.memoizedState.cachePool.pool;r!=null&&r.refCount++}break;case 24:Qr(n.memoizedState.cache)}if(r=n.child,r!==null)r.return=n,kt=r;else e:for(n=e;kt!==null;){r=kt;var o=r.sibling,c=r.return;if(Qm(r),r===n){kt=null;break e}if(o!==null){o.return=c,kt=o;break e}kt=c}}}var G0={getCacheForType:function(e){var t=Yt(Tt),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},X0=typeof WeakMap=="function"?WeakMap:Map,Je=0,rt=null,He=null,Ge=0,We=0,rn=null,Ra=!1,er=!1,bc=!1,ia=0,mt=0,Na=0,pl=0,xc=0,_n=0,tr=0,fi=null,Wt=null,Sc=!1,_c=0,js=1/0,Ms=null,ja=null,Bt=0,Ma=null,nr=null,ar=0,wc=0,Ec=null,nv=null,di=0,Ac=null;function sn(){if((Je&2)!==0&&Ge!==0)return Ge&-Ge;if(O.T!==null){var e=Gl;return e!==0?e:Mc()}return yd()}function av(){_n===0&&(_n=(Ge&536870912)===0||$e?md():536870912);var e=Sn.current;return e!==null&&(e.flags|=32),_n}function on(e,t,n){(e===rt&&(We===2||We===9)||e.cancelPendingCommit!==null)&&(lr(e,0),Da(e,Ge,_n,!1)),Nr(e,n),((Je&2)===0||e!==rt)&&(e===rt&&((Je&2)===0&&(pl|=n),mt===4&&Da(e,Ge,_n,!1)),Bn(e))}function lv(e,t,n){if((Je&6)!==0)throw Error(s(327));var r=!n&&(t&124)===0&&(t&e.expiredLanes)===0||el(e,t),o=r?P0(e,t):Oc(e,t,!0),c=r;do{if(o===0){er&&!r&&Da(e,t,0,!1);break}else{if(n=e.current.alternate,c&&!Q0(n)){o=Oc(e,t,!1),c=!1;continue}if(o===2){if(c=t,e.errorRecoveryDisabledLanes&c)var m=0;else m=e.pendingLanes&-536870913,m=m!==0?m:m&536870912?536870912:0;if(m!==0){t=m;e:{var y=e;o=fi;var x=y.current.memoizedState.isDehydrated;if(x&&(lr(y,m).flags|=256),m=Oc(y,m,!1),m!==2){if(bc&&!x){y.errorRecoveryDisabledLanes|=c,pl|=c,o=4;break e}c=Wt,Wt=o,c!==null&&(Wt===null?Wt=c:Wt.push.apply(Wt,c))}o=m}if(c=!1,o!==2)continue}}if(o===1){lr(e,0),Da(e,t,0,!0);break}e:{switch(r=e,c=o,c){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:Da(r,t,_n,!Ra);break e;case 2:Wt=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(o=_c+300-qt(),10<o)){if(Da(r,t,_n,!Ra),Ia(r,0,!0)!==0)break e;r.timeoutHandle=Dv(rv.bind(null,r,n,Wt,Ms,Sc,t,_n,pl,tr,Ra,c,2,-0,0),o);break e}rv(r,n,Wt,Ms,Sc,t,_n,pl,tr,Ra,c,0,-0,0)}}break}while(!0);Bn(e)}function rv(e,t,n,r,o,c,m,y,x,D,Y,F,U,B){if(e.timeoutHandle=-1,F=t.subtreeFlags,(F&8192||(F&16785408)===16785408)&&(bi={stylesheets:null,count:0,unsuspend:Tx},Wm(t),F=Rx(),F!==null)){e.cancelPendingCommit=F(dv.bind(null,e,t,c,n,r,o,m,y,x,Y,1,U,B)),Da(e,c,m,!D);return}dv(e,t,c,n,r,o,m,y,x)}function Q0(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var r=0;r<n.length;r++){var o=n[r],c=o.getSnapshot;o=o.value;try{if(!tn(c(),o))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Da(e,t,n,r){t&=~xc,t&=~pl,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var o=t;0<o;){var c=31-lt(o),m=1<<c;r[c]=-1,o&=~m}n!==0&&pd(e,n,t)}function Ds(){return(Je&6)===0?(hi(0),!1):!0}function Cc(){if(He!==null){if(We===0)var e=He.return;else e=He,Wn=cl=null,Yu(e),$l=null,ai=0,e=He;for(;e!==null;)Bm(e.alternate,e),e=e.return;He=null}}function lr(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,cx(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),Cc(),rt=e,He=n=Fn(e.current,null),Ge=t,We=0,rn=null,Ra=!1,er=el(e,t),bc=!1,tr=_n=xc=pl=Na=mt=0,Wt=fi=null,Sc=!1,(t&8)!==0&&(t|=t&32);var r=e.entangledLanes;if(r!==0)for(e=e.entanglements,r&=t;0<r;){var o=31-lt(r),c=1<<o;t|=e[o],r&=~c}return ia=t,ts(),n}function iv(e,t){ze=null,O.H=xs,t===Pr||t===cs?(t=wh(),We=3):t===xh?(t=wh(),We=4):We=t===Em?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,rn=t,He===null&&(mt=1,As(e,gn(t,e.current)))}function sv(){var e=O.H;return O.H=xs,e===null?xs:e}function ov(){var e=O.A;return O.A=G0,e}function Tc(){mt=4,Ra||(Ge&4194048)!==Ge&&Sn.current!==null||(er=!0),(Na&134217727)===0&&(pl&134217727)===0||rt===null||Da(rt,Ge,_n,!1)}function Oc(e,t,n){var r=Je;Je|=2;var o=sv(),c=ov();(rt!==e||Ge!==t)&&(Ms=null,lr(e,t)),t=!1;var m=mt;e:do try{if(We!==0&&He!==null){var y=He,x=rn;switch(We){case 8:Cc(),m=6;break e;case 3:case 2:case 9:case 6:Sn.current===null&&(t=!0);var D=We;if(We=0,rn=null,rr(e,y,x,D),n&&er){m=0;break e}break;default:D=We,We=0,rn=null,rr(e,y,x,D)}}K0(),m=mt;break}catch(Y){iv(e,Y)}while(!0);return t&&e.shellSuspendCounter++,Wn=cl=null,Je=r,O.H=o,O.A=c,He===null&&(rt=null,Ge=0,ts()),m}function K0(){for(;He!==null;)uv(He)}function P0(e,t){var n=Je;Je|=2;var r=sv(),o=ov();rt!==e||Ge!==t?(Ms=null,js=qt()+500,lr(e,t)):er=el(e,t);e:do try{if(We!==0&&He!==null){t=He;var c=rn;t:switch(We){case 1:We=0,rn=null,rr(e,t,c,1);break;case 2:case 9:if(Sh(c)){We=0,rn=null,cv(t);break}t=function(){We!==2&&We!==9||rt!==e||(We=7),Bn(e)},c.then(t,t);break e;case 3:We=7;break e;case 4:We=5;break e;case 7:Sh(c)?(We=0,rn=null,cv(t)):(We=0,rn=null,rr(e,t,c,7));break;case 5:var m=null;switch(He.tag){case 26:m=He.memoizedState;case 5:case 27:var y=He;if(!m||Xv(m)){We=0,rn=null;var x=y.sibling;if(x!==null)He=x;else{var D=y.return;D!==null?(He=D,zs(D)):He=null}break t}}We=0,rn=null,rr(e,t,c,5);break;case 6:We=0,rn=null,rr(e,t,c,6);break;case 8:Cc(),mt=6;break e;default:throw Error(s(462))}}F0();break}catch(Y){iv(e,Y)}while(!0);return Wn=cl=null,O.H=r,O.A=o,Je=n,He!==null?0:(rt=null,Ge=0,ts(),mt)}function F0(){for(;He!==null&&!ma();)uv(He)}function uv(e){var t=Um(e.alternate,e,ia);e.memoizedProps=e.pendingProps,t===null?zs(e):He=t}function cv(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Nm(n,t,t.pendingProps,t.type,void 0,Ge);break;case 11:t=Nm(n,t,t.pendingProps,t.type.render,t.ref,Ge);break;case 5:Yu(t);default:Bm(n,t),t=He=fh(t,ia),t=Um(n,t,ia)}e.memoizedProps=e.pendingProps,t===null?zs(e):He=t}function rr(e,t,n,r){Wn=cl=null,Yu(t),$l=null,ai=0;var o=t.return;try{if(B0(e,o,t,n,Ge)){mt=1,As(e,gn(n,e.current)),He=null;return}}catch(c){if(o!==null)throw He=o,c;mt=1,As(e,gn(n,e.current)),He=null;return}t.flags&32768?($e||r===1?e=!0:er||(Ge&536870912)!==0?e=!1:(Ra=e=!0,(r===2||r===9||r===3||r===6)&&(r=Sn.current,r!==null&&r.tag===13&&(r.flags|=16384))),fv(t,e)):zs(t)}function zs(e){var t=e;do{if((t.flags&32768)!==0){fv(t,Ra);return}e=t.return;var n=H0(t.alternate,t,ia);if(n!==null){He=n;return}if(t=t.sibling,t!==null){He=t;return}He=t=e}while(t!==null);mt===0&&(mt=5)}function fv(e,t){do{var n=q0(e.alternate,e);if(n!==null){n.flags&=32767,He=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){He=e;return}He=e=n}while(e!==null);mt=6,He=null}function dv(e,t,n,r,o,c,m,y,x){e.cancelPendingCommit=null;do ks();while(Bt!==0);if((Je&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(c=t.lanes|t.childLanes,c|=yu,Tb(e,n,c,m,y,x),e===rt&&(He=rt=null,Ge=0),nr=t,Ma=e,ar=n,wc=c,Ec=o,nv=r,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,I0(k,function(){return gv(),null})):(e.callbackNode=null,e.callbackPriority=0),r=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||r){r=O.T,O.T=null,o=G.p,G.p=2,m=Je,Je|=4;try{Z0(e,t,n)}finally{Je=m,G.p=o,O.T=r}}Bt=1,hv(),mv(),vv()}}function hv(){if(Bt===1){Bt=0;var e=Ma,t=nr,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=O.T,O.T=null;var r=G.p;G.p=2;var o=Je;Je|=4;try{Fm(t,e);var c=Hc,m=th(e.containerInfo),y=c.focusedElem,x=c.selectionRange;if(m!==y&&y&&y.ownerDocument&&eh(y.ownerDocument.documentElement,y)){if(x!==null&&hu(y)){var D=x.start,Y=x.end;if(Y===void 0&&(Y=D),"selectionStart"in y)y.selectionStart=D,y.selectionEnd=Math.min(Y,y.value.length);else{var F=y.ownerDocument||document,U=F&&F.defaultView||window;if(U.getSelection){var B=U.getSelection(),Oe=y.textContent.length,Ee=Math.min(x.start,Oe),nt=x.end===void 0?Ee:Math.min(x.end,Oe);!B.extend&&Ee>nt&&(m=nt,nt=Ee,Ee=m);var R=Id(y,Ee),C=Id(y,nt);if(R&&C&&(B.rangeCount!==1||B.anchorNode!==R.node||B.anchorOffset!==R.offset||B.focusNode!==C.node||B.focusOffset!==C.offset)){var M=F.createRange();M.setStart(R.node,R.offset),B.removeAllRanges(),Ee>nt?(B.addRange(M),B.extend(C.node,C.offset)):(M.setEnd(C.node,C.offset),B.addRange(M))}}}}for(F=[],B=y;B=B.parentNode;)B.nodeType===1&&F.push({element:B,left:B.scrollLeft,top:B.scrollTop});for(typeof y.focus=="function"&&y.focus(),y=0;y<F.length;y++){var Q=F[y];Q.element.scrollLeft=Q.left,Q.element.scrollTop=Q.top}}Ks=!!Lc,Hc=Lc=null}finally{Je=o,G.p=r,O.T=n}}e.current=t,Bt=2}}function mv(){if(Bt===2){Bt=0;var e=Ma,t=nr,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=O.T,O.T=null;var r=G.p;G.p=2;var o=Je;Je|=4;try{Xm(e,t.alternate,t)}finally{Je=o,G.p=r,O.T=n}}Bt=3}}function vv(){if(Bt===4||Bt===3){Bt=0,Wa();var e=Ma,t=nr,n=ar,r=nv;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Bt=5:(Bt=0,nr=Ma=null,pv(e,e.pendingLanes));var o=e.pendingLanes;if(o===0&&(ja=null),Qo(n),t=t.stateNode,Me&&typeof Me.onCommitFiberRoot=="function")try{Me.onCommitFiberRoot(be,t,void 0,(t.current.flags&128)===128)}catch{}if(r!==null){t=O.T,o=G.p,G.p=2,O.T=null;try{for(var c=e.onRecoverableError,m=0;m<r.length;m++){var y=r[m];c(y.value,{componentStack:y.stack})}}finally{O.T=t,G.p=o}}(ar&3)!==0&&ks(),Bn(e),o=e.pendingLanes,(n&4194090)!==0&&(o&42)!==0?e===Ac?di++:(di=0,Ac=e):di=0,hi(0)}}function pv(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Qr(t)))}function ks(e){return hv(),mv(),vv(),gv()}function gv(){if(Bt!==5)return!1;var e=Ma,t=wc;wc=0;var n=Qo(ar),r=O.T,o=G.p;try{G.p=32>n?32:n,O.T=null,n=Ec,Ec=null;var c=Ma,m=ar;if(Bt=0,nr=Ma=null,ar=0,(Je&6)!==0)throw Error(s(331));var y=Je;if(Je|=4,ev(c.current),Jm(c,c.current,m,n),Je=y,hi(0,!1),Me&&typeof Me.onPostCommitFiberRoot=="function")try{Me.onPostCommitFiberRoot(be,c)}catch{}return!0}finally{G.p=o,O.T=r,pv(e,t)}}function yv(e,t,n){t=gn(n,t),t=ac(e.stateNode,t,2),e=_a(e,t,2),e!==null&&(Nr(e,2),Bn(e))}function at(e,t,n){if(e.tag===3)yv(e,e,n);else for(;t!==null;){if(t.tag===3){yv(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(ja===null||!ja.has(r))){e=gn(n,e),n=_m(2),r=_a(t,n,2),r!==null&&(wm(n,r,t,e),Nr(r,2),Bn(r));break}}t=t.return}}function Rc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new X0;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(bc=!0,o.add(n),e=$0.bind(null,e,t,n),t.then(e,e))}function $0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,rt===e&&(Ge&n)===n&&(mt===4||mt===3&&(Ge&62914560)===Ge&&300>qt()-_c?(Je&2)===0&&lr(e,0):xc|=n,tr===Ge&&(tr=0)),Bn(e)}function bv(e,t){t===0&&(t=vd()),e=Hl(e,t),e!==null&&(Nr(e,t),Bn(e))}function J0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),bv(e,n)}function W0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(s(314))}r!==null&&r.delete(t),bv(e,n)}function I0(e,t){return Mt(e,t)}var Us=null,ir=null,Nc=!1,Vs=!1,jc=!1,gl=0;function Bn(e){e!==ir&&e.next===null&&(ir===null?Us=ir=e:ir=ir.next=e),Vs=!0,Nc||(Nc=!0,tx())}function hi(e,t){if(!jc&&Vs){jc=!0;do for(var n=!1,r=Us;r!==null;){if(e!==0){var o=r.pendingLanes;if(o===0)var c=0;else{var m=r.suspendedLanes,y=r.pingedLanes;c=(1<<31-lt(42|e)+1)-1,c&=o&~(m&~y),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,wv(r,c))}else c=Ge,c=Ia(r,r===rt?c:0,r.cancelPendingCommit!==null||r.timeoutHandle!==-1),(c&3)===0||el(r,c)||(n=!0,wv(r,c));r=r.next}while(n);jc=!1}}function ex(){xv()}function xv(){Vs=Nc=!1;var e=0;gl!==0&&(ux()&&(e=gl),gl=0);for(var t=qt(),n=null,r=Us;r!==null;){var o=r.next,c=Sv(r,t);c===0?(r.next=null,n===null?Us=o:n.next=o,o===null&&(ir=n)):(n=r,(e!==0||(c&3)!==0)&&(Vs=!0)),r=o}hi(e)}function Sv(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var m=31-lt(c),y=1<<m,x=o[m];x===-1?((y&n)===0||(y&r)!==0)&&(o[m]=Yi(y,t)):x<=t&&(e.expiredLanes|=y),c&=~y}if(t=rt,n=Ge,n=Ia(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),r=e.callbackNode,n===0||e===t&&(We===2||We===9)||e.cancelPendingCommit!==null)return r!==null&&r!==null&&Kt(r),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||el(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(r!==null&&Kt(r),Qo(n)){case 2:case 8:n=A;break;case 32:n=k;break;case 268435456:n=oe;break;default:n=k}return r=_v.bind(null,e),n=Mt(n,r),e.callbackPriority=t,e.callbackNode=n,t}return r!==null&&r!==null&&Kt(r),e.callbackPriority=2,e.callbackNode=null,2}function _v(e,t){if(Bt!==0&&Bt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ks()&&e.callbackNode!==n)return null;var r=Ge;return r=Ia(e,e===rt?r:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),r===0?null:(lv(e,r,t),Sv(e,qt()),e.callbackNode!=null&&e.callbackNode===n?_v.bind(null,e):null)}function wv(e,t){if(ks())return null;lv(e,t,!0)}function tx(){fx(function(){(Je&6)!==0?Mt(Rr,ex):xv()})}function Mc(){return gl===0&&(gl=md()),gl}function Ev(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Pi(""+e)}function Av(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function nx(e,t,n,r,o){if(t==="submit"&&n&&n.stateNode===o){var c=Ev((o[Pt]||null).action),m=r.submitter;m&&(t=(t=m[Pt]||null)?Ev(t.formAction):m.getAttribute("formAction"),t!==null&&(c=t,m=null));var y=new Wi("action","action",null,r,o);e.push({event:y,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(gl!==0){var x=m?Av(o,m):new FormData(o);Wu(n,{pending:!0,data:x,method:o.method,action:c},null,x)}}else typeof c=="function"&&(y.preventDefault(),x=m?Av(o,m):new FormData(o),Wu(n,{pending:!0,data:x,method:o.method,action:c},c,x))},currentTarget:o}]})}}for(var Dc=0;Dc<gu.length;Dc++){var zc=gu[Dc],ax=zc.toLowerCase(),lx=zc[0].toUpperCase()+zc.slice(1);An(ax,"on"+lx)}An(lh,"onAnimationEnd"),An(rh,"onAnimationIteration"),An(ih,"onAnimationStart"),An("dblclick","onDoubleClick"),An("focusin","onFocus"),An("focusout","onBlur"),An(S0,"onTransitionRun"),An(_0,"onTransitionStart"),An(w0,"onTransitionCancel"),An(sh,"onTransitionEnd"),Nl("onMouseEnter",["mouseout","mouseover"]),Nl("onMouseLeave",["mouseout","mouseover"]),Nl("onPointerEnter",["pointerout","pointerover"]),Nl("onPointerLeave",["pointerout","pointerover"]),tl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),tl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),tl("onBeforeInput",["compositionend","keypress","textInput","paste"]),tl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),tl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),tl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var mi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),rx=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(mi));function Cv(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var c=void 0;if(t)for(var m=r.length-1;0<=m;m--){var y=r[m],x=y.instance,D=y.currentTarget;if(y=y.listener,x!==c&&o.isPropagationStopped())break e;c=y,o.currentTarget=D;try{c(o)}catch(Y){Es(Y)}o.currentTarget=null,c=x}else for(m=0;m<r.length;m++){if(y=r[m],x=y.instance,D=y.currentTarget,y=y.listener,x!==c&&o.isPropagationStopped())break e;c=y,o.currentTarget=D;try{c(o)}catch(Y){Es(Y)}o.currentTarget=null,c=x}}}}function qe(e,t){var n=t[Ko];n===void 0&&(n=t[Ko]=new Set);var r=e+"__bubble";n.has(r)||(Tv(t,e,2,!1),n.add(r))}function kc(e,t,n){var r=0;t&&(r|=4),Tv(n,e,r,t)}var Bs="_reactListening"+Math.random().toString(36).slice(2);function Uc(e){if(!e[Bs]){e[Bs]=!0,xd.forEach(function(n){n!=="selectionchange"&&(rx.has(n)||kc(n,!1,e),kc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Bs]||(t[Bs]=!0,kc("selectionchange",!1,t))}}function Tv(e,t,n,r){switch(Jv(t)){case 2:var o=Mx;break;case 8:o=Dx;break;default:o=$c}n=o.bind(null,t,n,e),o=void 0,!lu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Vc(e,t,n,r,o){var c=r;if((t&1)===0&&(t&2)===0&&r!==null)e:for(;;){if(r===null)return;var m=r.tag;if(m===3||m===4){var y=r.stateNode.containerInfo;if(y===o)break;if(m===4)for(m=r.return;m!==null;){var x=m.tag;if((x===3||x===4)&&m.stateNode.containerInfo===o)return;m=m.return}for(;y!==null;){if(m=Tl(y),m===null)return;if(x=m.tag,x===5||x===6||x===26||x===27){r=c=m;continue e}y=y.parentNode}}r=r.return}zd(function(){var D=c,Y=nu(n),F=[];e:{var U=oh.get(e);if(U!==void 0){var B=Wi,Oe=e;switch(e){case"keypress":if($i(n)===0)break e;case"keydown":case"keyup":B=Ib;break;case"focusin":Oe="focus",B=ou;break;case"focusout":Oe="blur",B=ou;break;case"beforeblur":case"afterblur":B=ou;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":B=Vd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":B=qb;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":B=n0;break;case lh:case rh:case ih:B=Gb;break;case sh:B=l0;break;case"scroll":case"scrollend":B=Lb;break;case"wheel":B=i0;break;case"copy":case"cut":case"paste":B=Qb;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":B=Ld;break;case"toggle":case"beforetoggle":B=o0}var Ee=(t&4)!==0,nt=!Ee&&(e==="scroll"||e==="scrollend"),R=Ee?U!==null?U+"Capture":null:U;Ee=[];for(var C=D,M;C!==null;){var Q=C;if(M=Q.stateNode,Q=Q.tag,Q!==5&&Q!==26&&Q!==27||M===null||R===null||(Q=Dr(C,R),Q!=null&&Ee.push(vi(C,Q,M))),nt)break;C=C.return}0<Ee.length&&(U=new B(U,Oe,null,n,Y),F.push({event:U,listeners:Ee}))}}if((t&7)===0){e:{if(U=e==="mouseover"||e==="pointerover",B=e==="mouseout"||e==="pointerout",U&&n!==tu&&(Oe=n.relatedTarget||n.fromElement)&&(Tl(Oe)||Oe[Cl]))break e;if((B||U)&&(U=Y.window===Y?Y:(U=Y.ownerDocument)?U.defaultView||U.parentWindow:window,B?(Oe=n.relatedTarget||n.toElement,B=D,Oe=Oe?Tl(Oe):null,Oe!==null&&(nt=f(Oe),Ee=Oe.tag,Oe!==nt||Ee!==5&&Ee!==27&&Ee!==6)&&(Oe=null)):(B=null,Oe=D),B!==Oe)){if(Ee=Vd,Q="onMouseLeave",R="onMouseEnter",C="mouse",(e==="pointerout"||e==="pointerover")&&(Ee=Ld,Q="onPointerLeave",R="onPointerEnter",C="pointer"),nt=B==null?U:Mr(B),M=Oe==null?U:Mr(Oe),U=new Ee(Q,C+"leave",B,n,Y),U.target=nt,U.relatedTarget=M,Q=null,Tl(Y)===D&&(Ee=new Ee(R,C+"enter",Oe,n,Y),Ee.target=M,Ee.relatedTarget=nt,Q=Ee),nt=Q,B&&Oe)t:{for(Ee=B,R=Oe,C=0,M=Ee;M;M=sr(M))C++;for(M=0,Q=R;Q;Q=sr(Q))M++;for(;0<C-M;)Ee=sr(Ee),C--;for(;0<M-C;)R=sr(R),M--;for(;C--;){if(Ee===R||R!==null&&Ee===R.alternate)break t;Ee=sr(Ee),R=sr(R)}Ee=null}else Ee=null;B!==null&&Ov(F,U,B,Ee,!1),Oe!==null&&nt!==null&&Ov(F,nt,Oe,Ee,!0)}}e:{if(U=D?Mr(D):window,B=U.nodeName&&U.nodeName.toLowerCase(),B==="select"||B==="input"&&U.type==="file")var de=Kd;else if(Xd(U))if(Pd)de=y0;else{de=p0;var Ve=v0}else B=U.nodeName,!B||B.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?D&&eu(D.elementType)&&(de=Kd):de=g0;if(de&&(de=de(e,D))){Qd(F,de,n,Y);break e}Ve&&Ve(e,U,D),e==="focusout"&&D&&U.type==="number"&&D.memoizedProps.value!=null&&Io(U,"number",U.value)}switch(Ve=D?Mr(D):window,e){case"focusin":(Xd(Ve)||Ve.contentEditable==="true")&&(Vl=Ve,mu=D,qr=null);break;case"focusout":qr=mu=Vl=null;break;case"mousedown":vu=!0;break;case"contextmenu":case"mouseup":case"dragend":vu=!1,nh(F,n,Y);break;case"selectionchange":if(x0)break;case"keydown":case"keyup":nh(F,n,Y)}var _e;if(cu)e:{switch(e){case"compositionstart":var Ae="onCompositionStart";break e;case"compositionend":Ae="onCompositionEnd";break e;case"compositionupdate":Ae="onCompositionUpdate";break e}Ae=void 0}else Ul?Yd(e,n)&&(Ae="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(Ae="onCompositionStart");Ae&&(Hd&&n.locale!=="ko"&&(Ul||Ae!=="onCompositionStart"?Ae==="onCompositionEnd"&&Ul&&(_e=kd()):(ya=Y,ru="value"in ya?ya.value:ya.textContent,Ul=!0)),Ve=Ls(D,Ae),0<Ve.length&&(Ae=new Bd(Ae,e,null,n,Y),F.push({event:Ae,listeners:Ve}),_e?Ae.data=_e:(_e=Gd(n),_e!==null&&(Ae.data=_e)))),(_e=c0?f0(e,n):d0(e,n))&&(Ae=Ls(D,"onBeforeInput"),0<Ae.length&&(Ve=new Bd("onBeforeInput","beforeinput",null,n,Y),F.push({event:Ve,listeners:Ae}),Ve.data=_e)),nx(F,e,D,n,Y)}Cv(F,t)})}function vi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ls(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,c=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||c===null||(o=Dr(e,n),o!=null&&r.unshift(vi(e,o,c)),o=Dr(e,t),o!=null&&r.push(vi(e,o,c))),e.tag===3)return r;e=e.return}return[]}function sr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Ov(e,t,n,r,o){for(var c=t._reactName,m=[];n!==null&&n!==r;){var y=n,x=y.alternate,D=y.stateNode;if(y=y.tag,x!==null&&x===r)break;y!==5&&y!==26&&y!==27||D===null||(x=D,o?(D=Dr(n,c),D!=null&&m.unshift(vi(n,D,x))):o||(D=Dr(n,c),D!=null&&m.push(vi(n,D,x)))),n=n.return}m.length!==0&&e.push({event:t,listeners:m})}var ix=/\r\n?/g,sx=/\u0000|\uFFFD/g;function Rv(e){return(typeof e=="string"?e:""+e).replace(ix,`
`).replace(sx,"")}function Nv(e,t){return t=Rv(t),Rv(e)===t}function Hs(){}function tt(e,t,n,r,o,c){switch(n){case"children":typeof r=="string"?t==="body"||t==="textarea"&&r===""||Dl(e,r):(typeof r=="number"||typeof r=="bigint")&&t!=="body"&&Dl(e,""+r);break;case"className":Xi(e,"class",r);break;case"tabIndex":Xi(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":Xi(e,n,r);break;case"style":Md(e,r,c);break;case"data":if(t!=="object"){Xi(e,"data",r);break}case"src":case"href":if(r===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(r==null||typeof r=="function"||typeof r=="symbol"||typeof r=="boolean"){e.removeAttribute(n);break}r=Pi(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if(typeof r=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(t!=="input"&&tt(e,t,"name",o.name,o,null),tt(e,t,"formEncType",o.formEncType,o,null),tt(e,t,"formMethod",o.formMethod,o,null),tt(e,t,"formTarget",o.formTarget,o,null)):(tt(e,t,"encType",o.encType,o,null),tt(e,t,"method",o.method,o,null),tt(e,t,"target",o.target,o,null)));if(r==null||typeof r=="symbol"||typeof r=="boolean"){e.removeAttribute(n);break}r=Pi(""+r),e.setAttribute(n,r);break;case"onClick":r!=null&&(e.onclick=Hs);break;case"onScroll":r!=null&&qe("scroll",e);break;case"onScrollEnd":r!=null&&qe("scrollend",e);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(s(61));if(n=r.__html,n!=null){if(o.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&typeof r!="function"&&typeof r!="symbol";break;case"muted":e.muted=r&&typeof r!="function"&&typeof r!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(r==null||typeof r=="function"||typeof r=="boolean"||typeof r=="symbol"){e.removeAttribute("xlink:href");break}n=Pi(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":r!=null&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":r===!0?e.setAttribute(n,""):r!==!1&&r!=null&&typeof r!="function"&&typeof r!="symbol"?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":r!=null&&typeof r!="function"&&typeof r!="symbol"&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":r==null||typeof r=="function"||typeof r=="symbol"||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":qe("beforetoggle",e),qe("toggle",e),Gi(e,"popover",r);break;case"xlinkActuate":Kn(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":Kn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":Kn(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":Kn(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":Kn(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":Kn(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":Kn(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":Kn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":Kn(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":Gi(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Vb.get(n)||n,Gi(e,n,r))}}function Bc(e,t,n,r,o,c){switch(n){case"style":Md(e,r,c);break;case"dangerouslySetInnerHTML":if(r!=null){if(typeof r!="object"||!("__html"in r))throw Error(s(61));if(n=r.__html,n!=null){if(o.children!=null)throw Error(s(60));e.innerHTML=n}}break;case"children":typeof r=="string"?Dl(e,r):(typeof r=="number"||typeof r=="bigint")&&Dl(e,""+r);break;case"onScroll":r!=null&&qe("scroll",e);break;case"onScrollEnd":r!=null&&qe("scrollend",e);break;case"onClick":r!=null&&(e.onclick=Hs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Sd.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(o=n.endsWith("Capture"),t=n.slice(2,o?n.length-7:void 0),c=e[Pt]||null,c=c!=null?c[n]:null,typeof c=="function"&&e.removeEventListener(t,c,o),typeof r=="function")){typeof c!="function"&&c!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,o);break e}n in e?e[n]=r:r===!0?e.setAttribute(n,""):Gi(e,n,r)}}}function Lt(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":qe("error",e),qe("load",e);var r=!1,o=!1,c;for(c in n)if(n.hasOwnProperty(c)){var m=n[c];if(m!=null)switch(c){case"src":r=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:tt(e,t,c,m,n,null)}}o&&tt(e,t,"srcSet",n.srcSet,n,null),r&&tt(e,t,"src",n.src,n,null);return;case"input":qe("invalid",e);var y=c=m=o=null,x=null,D=null;for(r in n)if(n.hasOwnProperty(r)){var Y=n[r];if(Y!=null)switch(r){case"name":o=Y;break;case"type":m=Y;break;case"checked":x=Y;break;case"defaultChecked":D=Y;break;case"value":c=Y;break;case"defaultValue":y=Y;break;case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(s(137,t));break;default:tt(e,t,r,Y,n,null)}}Od(e,c,y,x,D,m,o,!1),Qi(e);return;case"select":qe("invalid",e),r=m=c=null;for(o in n)if(n.hasOwnProperty(o)&&(y=n[o],y!=null))switch(o){case"value":c=y;break;case"defaultValue":m=y;break;case"multiple":r=y;default:tt(e,t,o,y,n,null)}t=c,n=m,e.multiple=!!r,t!=null?Ml(e,!!r,t,!1):n!=null&&Ml(e,!!r,n,!0);return;case"textarea":qe("invalid",e),c=o=r=null;for(m in n)if(n.hasOwnProperty(m)&&(y=n[m],y!=null))switch(m){case"value":r=y;break;case"defaultValue":o=y;break;case"children":c=y;break;case"dangerouslySetInnerHTML":if(y!=null)throw Error(s(91));break;default:tt(e,t,m,y,n,null)}Nd(e,r,o,c),Qi(e);return;case"option":for(x in n)if(n.hasOwnProperty(x)&&(r=n[x],r!=null))switch(x){case"selected":e.selected=r&&typeof r!="function"&&typeof r!="symbol";break;default:tt(e,t,x,r,n,null)}return;case"dialog":qe("beforetoggle",e),qe("toggle",e),qe("cancel",e),qe("close",e);break;case"iframe":case"object":qe("load",e);break;case"video":case"audio":for(r=0;r<mi.length;r++)qe(mi[r],e);break;case"image":qe("error",e),qe("load",e);break;case"details":qe("toggle",e);break;case"embed":case"source":case"link":qe("error",e),qe("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in n)if(n.hasOwnProperty(D)&&(r=n[D],r!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:tt(e,t,D,r,n,null)}return;default:if(eu(t)){for(Y in n)n.hasOwnProperty(Y)&&(r=n[Y],r!==void 0&&Bc(e,t,Y,r,n,void 0));return}}for(y in n)n.hasOwnProperty(y)&&(r=n[y],r!=null&&tt(e,t,y,r,n,null))}function ox(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,c=null,m=null,y=null,x=null,D=null,Y=null;for(B in n){var F=n[B];if(n.hasOwnProperty(B)&&F!=null)switch(B){case"checked":break;case"value":break;case"defaultValue":x=F;default:r.hasOwnProperty(B)||tt(e,t,B,null,r,F)}}for(var U in r){var B=r[U];if(F=n[U],r.hasOwnProperty(U)&&(B!=null||F!=null))switch(U){case"type":c=B;break;case"name":o=B;break;case"checked":D=B;break;case"defaultChecked":Y=B;break;case"value":m=B;break;case"defaultValue":y=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(s(137,t));break;default:B!==F&&tt(e,t,U,B,r,F)}}Wo(e,m,y,x,D,Y,c,o);return;case"select":B=m=y=U=null;for(c in n)if(x=n[c],n.hasOwnProperty(c)&&x!=null)switch(c){case"value":break;case"multiple":B=x;default:r.hasOwnProperty(c)||tt(e,t,c,null,r,x)}for(o in r)if(c=r[o],x=n[o],r.hasOwnProperty(o)&&(c!=null||x!=null))switch(o){case"value":U=c;break;case"defaultValue":y=c;break;case"multiple":m=c;default:c!==x&&tt(e,t,o,c,r,x)}t=y,n=m,r=B,U!=null?Ml(e,!!n,U,!1):!!r!=!!n&&(t!=null?Ml(e,!!n,t,!0):Ml(e,!!n,n?[]:"",!1));return;case"textarea":B=U=null;for(y in n)if(o=n[y],n.hasOwnProperty(y)&&o!=null&&!r.hasOwnProperty(y))switch(y){case"value":break;case"children":break;default:tt(e,t,y,null,r,o)}for(m in r)if(o=r[m],c=n[m],r.hasOwnProperty(m)&&(o!=null||c!=null))switch(m){case"value":U=o;break;case"defaultValue":B=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(s(91));break;default:o!==c&&tt(e,t,m,o,r,c)}Rd(e,U,B);return;case"option":for(var Oe in n)if(U=n[Oe],n.hasOwnProperty(Oe)&&U!=null&&!r.hasOwnProperty(Oe))switch(Oe){case"selected":e.selected=!1;break;default:tt(e,t,Oe,null,r,U)}for(x in r)if(U=r[x],B=n[x],r.hasOwnProperty(x)&&U!==B&&(U!=null||B!=null))switch(x){case"selected":e.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:tt(e,t,x,U,r,B)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var Ee in n)U=n[Ee],n.hasOwnProperty(Ee)&&U!=null&&!r.hasOwnProperty(Ee)&&tt(e,t,Ee,null,r,U);for(D in r)if(U=r[D],B=n[D],r.hasOwnProperty(D)&&U!==B&&(U!=null||B!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(s(137,t));break;default:tt(e,t,D,U,r,B)}return;default:if(eu(t)){for(var nt in n)U=n[nt],n.hasOwnProperty(nt)&&U!==void 0&&!r.hasOwnProperty(nt)&&Bc(e,t,nt,void 0,r,U);for(Y in r)U=r[Y],B=n[Y],!r.hasOwnProperty(Y)||U===B||U===void 0&&B===void 0||Bc(e,t,Y,U,r,B);return}}for(var R in n)U=n[R],n.hasOwnProperty(R)&&U!=null&&!r.hasOwnProperty(R)&&tt(e,t,R,null,r,U);for(F in r)U=r[F],B=n[F],!r.hasOwnProperty(F)||U===B||U==null&&B==null||tt(e,t,F,U,r,B)}var Lc=null,Hc=null;function qs(e){return e.nodeType===9?e:e.ownerDocument}function jv(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Mv(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function qc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Zc=null;function ux(){var e=window.event;return e&&e.type==="popstate"?e===Zc?!1:(Zc=e,!0):(Zc=null,!1)}var Dv=typeof setTimeout=="function"?setTimeout:void 0,cx=typeof clearTimeout=="function"?clearTimeout:void 0,zv=typeof Promise=="function"?Promise:void 0,fx=typeof queueMicrotask=="function"?queueMicrotask:typeof zv<"u"?function(e){return zv.resolve(null).then(e).catch(dx)}:Dv;function dx(e){setTimeout(function(){throw e})}function za(e){return e==="head"}function kv(e,t){var n=t,r=0,o=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<r&&8>r){n=r;var m=e.ownerDocument;if(n&1&&pi(m.documentElement),n&2&&pi(m.body),n&4)for(n=m.head,pi(n),m=n.firstChild;m;){var y=m.nextSibling,x=m.nodeName;m[jr]||x==="SCRIPT"||x==="STYLE"||x==="LINK"&&m.rel.toLowerCase()==="stylesheet"||n.removeChild(m),m=y}}if(o===0){e.removeChild(c),Ei(t);return}o--}else n==="$"||n==="$?"||n==="$!"?o++:r=n.charCodeAt(0)-48;else r=0;n=c}while(n);Ei(t)}function Yc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Yc(n),Po(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function hx(e,t,n,r){for(;e.nodeType===1;){var o=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(r){if(!e[jr])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==o.rel||e.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||e.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||e.getAttribute("title")!==(o.title==null?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(o.src==null?null:o.src)||e.getAttribute("type")!==(o.type==null?null:o.type)||e.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=o.name==null?null:""+o.name;if(o.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Tn(e.nextSibling),e===null)break}return null}function mx(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Tn(e.nextSibling),e===null))return null;return e}function Gc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function vx(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}function Tn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Xc=null;function Uv(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Vv(e,t,n){switch(t=qs(n),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function pi(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Po(e)}var wn=new Map,Bv=new Set;function Zs(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var sa=G.d;G.d={f:px,r:gx,D:yx,C:bx,L:xx,m:Sx,X:wx,S:_x,M:Ex};function px(){var e=sa.f(),t=Ds();return e||t}function gx(e){var t=Ol(e);t!==null&&t.tag===5&&t.type==="form"?lm(t):sa.r(e)}var or=typeof document>"u"?null:document;function Lv(e,t,n){var r=or;if(r&&typeof t=="string"&&t){var o=pn(t);o='link[rel="'+e+'"][href="'+o+'"]',typeof n=="string"&&(o+='[crossorigin="'+n+'"]'),Bv.has(o)||(Bv.add(o),e={rel:e,crossOrigin:n,href:t},r.querySelector(o)===null&&(t=r.createElement("link"),Lt(t,"link",e),Dt(t),r.head.appendChild(t)))}}function yx(e){sa.D(e),Lv("dns-prefetch",e,null)}function bx(e,t){sa.C(e,t),Lv("preconnect",e,t)}function xx(e,t,n){sa.L(e,t,n);var r=or;if(r&&e&&t){var o='link[rel="preload"][as="'+pn(t)+'"]';t==="image"&&n&&n.imageSrcSet?(o+='[imagesrcset="'+pn(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(o+='[imagesizes="'+pn(n.imageSizes)+'"]')):o+='[href="'+pn(e)+'"]';var c=o;switch(t){case"style":c=ur(e);break;case"script":c=cr(e)}wn.has(c)||(e=b({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),wn.set(c,e),r.querySelector(o)!==null||t==="style"&&r.querySelector(gi(c))||t==="script"&&r.querySelector(yi(c))||(t=r.createElement("link"),Lt(t,"link",e),Dt(t),r.head.appendChild(t)))}}function Sx(e,t){sa.m(e,t);var n=or;if(n&&e){var r=t&&typeof t.as=="string"?t.as:"script",o='link[rel="modulepreload"][as="'+pn(r)+'"][href="'+pn(e)+'"]',c=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=cr(e)}if(!wn.has(c)&&(e=b({rel:"modulepreload",href:e},t),wn.set(c,e),n.querySelector(o)===null)){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(yi(c)))return}r=n.createElement("link"),Lt(r,"link",e),Dt(r),n.head.appendChild(r)}}}function _x(e,t,n){sa.S(e,t,n);var r=or;if(r&&e){var o=Rl(r).hoistableStyles,c=ur(e);t=t||"default";var m=o.get(c);if(!m){var y={loading:0,preload:null};if(m=r.querySelector(gi(c)))y.loading=5;else{e=b({rel:"stylesheet",href:e,"data-precedence":t},n),(n=wn.get(c))&&Qc(e,n);var x=m=r.createElement("link");Dt(x),Lt(x,"link",e),x._p=new Promise(function(D,Y){x.onload=D,x.onerror=Y}),x.addEventListener("load",function(){y.loading|=1}),x.addEventListener("error",function(){y.loading|=2}),y.loading|=4,Ys(m,t,r)}m={type:"stylesheet",instance:m,count:1,state:y},o.set(c,m)}}}function wx(e,t){sa.X(e,t);var n=or;if(n&&e){var r=Rl(n).hoistableScripts,o=cr(e),c=r.get(o);c||(c=n.querySelector(yi(o)),c||(e=b({src:e,async:!0},t),(t=wn.get(o))&&Kc(e,t),c=n.createElement("script"),Dt(c),Lt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},r.set(o,c))}}function Ex(e,t){sa.M(e,t);var n=or;if(n&&e){var r=Rl(n).hoistableScripts,o=cr(e),c=r.get(o);c||(c=n.querySelector(yi(o)),c||(e=b({src:e,async:!0,type:"module"},t),(t=wn.get(o))&&Kc(e,t),c=n.createElement("script"),Dt(c),Lt(c,"link",e),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},r.set(o,c))}}function Hv(e,t,n,r){var o=(o=Se.current)?Zs(o):null;if(!o)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=ur(n.href),n=Rl(o).hoistableStyles,r=n.get(t),r||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=ur(n.href);var c=Rl(o).hoistableStyles,m=c.get(e);if(m||(o=o.ownerDocument||o,m={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,m),(c=o.querySelector(gi(e)))&&!c._p&&(m.instance=c,m.state.loading=5),wn.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},wn.set(e,n),c||Ax(o,e,n,m.state))),t&&r===null)throw Error(s(528,""));return m}if(t&&r!==null)throw Error(s(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=cr(n),n=Rl(o).hoistableScripts,r=n.get(t),r||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function ur(e){return'href="'+pn(e)+'"'}function gi(e){return'link[rel="stylesheet"]['+e+"]"}function qv(e){return b({},e,{"data-precedence":e.precedence,precedence:null})}function Ax(e,t,n,r){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?r.loading=1:(t=e.createElement("link"),r.preload=t,t.addEventListener("load",function(){return r.loading|=1}),t.addEventListener("error",function(){return r.loading|=2}),Lt(t,"link",n),Dt(t),e.head.appendChild(t))}function cr(e){return'[src="'+pn(e)+'"]'}function yi(e){return"script[async]"+e}function Zv(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+pn(n.href)+'"]');if(r)return t.instance=r,Dt(r),r;var o=b({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return r=(e.ownerDocument||e).createElement("style"),Dt(r),Lt(r,"style",o),Ys(r,n.precedence,e),t.instance=r;case"stylesheet":o=ur(n.href);var c=e.querySelector(gi(o));if(c)return t.state.loading|=4,t.instance=c,Dt(c),c;r=qv(n),(o=wn.get(o))&&Qc(r,o),c=(e.ownerDocument||e).createElement("link"),Dt(c);var m=c;return m._p=new Promise(function(y,x){m.onload=y,m.onerror=x}),Lt(c,"link",r),t.state.loading|=4,Ys(c,n.precedence,e),t.instance=c;case"script":return c=cr(n.src),(o=e.querySelector(yi(c)))?(t.instance=o,Dt(o),o):(r=n,(o=wn.get(c))&&(r=b({},n),Kc(r,o)),e=e.ownerDocument||e,o=e.createElement("script"),Dt(o),Lt(o,"link",r),e.head.appendChild(o),t.instance=o);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(r=t.instance,t.state.loading|=4,Ys(r,n.precedence,e));return t.instance}function Ys(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,c=o,m=0;m<r.length;m++){var y=r[m];if(y.dataset.precedence===t)c=y;else if(c!==o)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function Qc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Kc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Gs=null;function Yv(e,t,n){if(Gs===null){var r=new Map,o=Gs=new Map;o.set(n,r)}else o=Gs,r=o.get(n),r||(r=new Map,o.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),o=0;o<n.length;o++){var c=n[o];if(!(c[jr]||c[Zt]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var m=c.getAttribute(t)||"";m=e+m;var y=r.get(m);y?y.push(c):r.set(m,[c])}}return r}function Gv(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function Cx(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Xv(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var bi=null;function Tx(){}function Ox(e,t,n){if(bi===null)throw Error(s(475));var r=bi;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var o=ur(n.href),c=e.querySelector(gi(o));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(r.count++,r=Xs.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=c,Dt(c);return}c=e.ownerDocument||e,n=qv(n),(o=wn.get(o))&&Qc(n,o),c=c.createElement("link"),Dt(c);var m=c;m._p=new Promise(function(y,x){m.onload=y,m.onerror=x}),Lt(c,"link",n),t.instance=c}r.stylesheets===null&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(r.count++,t=Xs.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}function Rx(){if(bi===null)throw Error(s(475));var e=bi;return e.stylesheets&&e.count===0&&Pc(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Pc(e,e.stylesheets),e.unsuspend){var r=e.unsuspend;e.unsuspend=null,r()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Xs(){if(this.count--,this.count===0){if(this.stylesheets)Pc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Qs=null;function Pc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Qs=new Map,t.forEach(Nx,e),Qs=null,Xs.call(e))}function Nx(e,t){if(!(t.state.loading&4)){var n=Qs.get(e);if(n)var r=n.get(null);else{n=new Map,Qs.set(e,n);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<o.length;c++){var m=o[c];(m.nodeName==="LINK"||m.getAttribute("media")!=="not all")&&(n.set(m.dataset.precedence,m),r=m)}r&&n.set(null,r)}o=t.instance,m=o.getAttribute("data-precedence"),c=n.get(m)||r,c===r&&n.set(null,o),n.set(m,o),this.count++,r=Xs.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),c?c.parentNode.insertBefore(o,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(o,e.firstChild)),t.state.loading|=4}}var xi={$$typeof:H,Provider:null,Consumer:null,_currentValue:V,_currentValue2:V,_threadCount:0};function jx(e,t,n,r,o,c,m,y){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Go(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Go(0),this.hiddenUpdates=Go(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=c,this.onRecoverableError=m,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=y,this.incompleteTransitions=new Map}function Qv(e,t,n,r,o,c,m,y,x,D,Y,F){return e=new jx(e,t,n,m,y,x,D,F),t=1,c===!0&&(t|=24),c=nn(3,null,null,t),e.current=c,c.stateNode=e,t=Ru(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:r,isDehydrated:n,cache:t},Du(c),e}function Kv(e){return e?(e=ql,e):ql}function Pv(e,t,n,r,o,c){o=Kv(o),r.context===null?r.context=o:r.pendingContext=o,r=Sa(t),r.payload={element:n},c=c===void 0?null:c,c!==null&&(r.callback=c),n=_a(e,r,t),n!==null&&(on(n,e,t),$r(n,e,t))}function Fv(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Fc(e,t){Fv(e,t),(e=e.alternate)&&Fv(e,t)}function $v(e){if(e.tag===13){var t=Hl(e,67108864);t!==null&&on(t,e,67108864),Fc(e,67108864)}}var Ks=!0;function Mx(e,t,n,r){var o=O.T;O.T=null;var c=G.p;try{G.p=2,$c(e,t,n,r)}finally{G.p=c,O.T=o}}function Dx(e,t,n,r){var o=O.T;O.T=null;var c=G.p;try{G.p=8,$c(e,t,n,r)}finally{G.p=c,O.T=o}}function $c(e,t,n,r){if(Ks){var o=Jc(r);if(o===null)Vc(e,t,r,Ps,n),Wv(e,r);else if(kx(o,e,t,n,r))r.stopPropagation();else if(Wv(e,r),t&4&&-1<zx.indexOf(e)){for(;o!==null;){var c=Ol(o);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var m=Qn(c.pendingLanes);if(m!==0){var y=c;for(y.pendingLanes|=2,y.entangledLanes|=2;m;){var x=1<<31-lt(m);y.entanglements[1]|=x,m&=~x}Bn(c),(Je&6)===0&&(js=qt()+500,hi(0))}}break;case 13:y=Hl(c,2),y!==null&&on(y,c,2),Ds(),Fc(c,2)}if(c=Jc(r),c===null&&Vc(e,t,r,Ps,n),c===o)break;o=c}o!==null&&r.stopPropagation()}else Vc(e,t,r,null,n)}}function Jc(e){return e=nu(e),Wc(e)}var Ps=null;function Wc(e){if(Ps=null,e=Tl(e),e!==null){var t=f(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Ps=e,null}function Jv(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(hd()){case Rr:return 2;case A:return 8;case k:case X:return 32;case oe:return 268435456;default:return 32}default:return 32}}var Ic=!1,ka=null,Ua=null,Va=null,Si=new Map,_i=new Map,Ba=[],zx="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Wv(e,t){switch(e){case"focusin":case"focusout":ka=null;break;case"dragenter":case"dragleave":Ua=null;break;case"mouseover":case"mouseout":Va=null;break;case"pointerover":case"pointerout":Si.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":_i.delete(t.pointerId)}}function wi(e,t,n,r,o,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:c,targetContainers:[o]},t!==null&&(t=Ol(t),t!==null&&$v(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function kx(e,t,n,r,o){switch(t){case"focusin":return ka=wi(ka,e,t,n,r,o),!0;case"dragenter":return Ua=wi(Ua,e,t,n,r,o),!0;case"mouseover":return Va=wi(Va,e,t,n,r,o),!0;case"pointerover":var c=o.pointerId;return Si.set(c,wi(Si.get(c)||null,e,t,n,r,o)),!0;case"gotpointercapture":return c=o.pointerId,_i.set(c,wi(_i.get(c)||null,e,t,n,r,o)),!0}return!1}function Iv(e){var t=Tl(e.target);if(t!==null){var n=f(t);if(n!==null){if(t=n.tag,t===13){if(t=d(n),t!==null){e.blockedOn=t,Ob(e.priority,function(){if(n.tag===13){var r=sn();r=Xo(r);var o=Hl(n,r);o!==null&&on(o,n,r),Fc(n,r)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Fs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jc(e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);tu=r,n.target.dispatchEvent(r),tu=null}else return t=Ol(n),t!==null&&$v(t),e.blockedOn=n,!1;t.shift()}return!0}function ep(e,t,n){Fs(e)&&n.delete(t)}function Ux(){Ic=!1,ka!==null&&Fs(ka)&&(ka=null),Ua!==null&&Fs(Ua)&&(Ua=null),Va!==null&&Fs(Va)&&(Va=null),Si.forEach(ep),_i.forEach(ep)}function $s(e,t){e.blockedOn===t&&(e.blockedOn=null,Ic||(Ic=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ux)))}var Js=null;function tp(e){Js!==e&&(Js=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Js===e&&(Js=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],o=e[t+2];if(typeof r!="function"){if(Wc(r||n)===null)continue;break}var c=Ol(n);c!==null&&(e.splice(t,3),t-=3,Wu(c,{pending:!0,data:o,method:n.method,action:r},r,o))}}))}function Ei(e){function t(x){return $s(x,e)}ka!==null&&$s(ka,e),Ua!==null&&$s(Ua,e),Va!==null&&$s(Va,e),Si.forEach(t),_i.forEach(t);for(var n=0;n<Ba.length;n++){var r=Ba[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<Ba.length&&(n=Ba[0],n.blockedOn===null);)Iv(n),n.blockedOn===null&&Ba.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(r=0;r<n.length;r+=3){var o=n[r],c=n[r+1],m=o[Pt]||null;if(typeof c=="function")m||tp(n);else if(m){var y=null;if(c&&c.hasAttribute("formAction")){if(o=c,m=c[Pt]||null)y=m.formAction;else if(Wc(o)!==null)continue}else y=m.action;typeof y=="function"?n[r+1]=y:(n.splice(r,3),r-=3),tp(n)}}}function ef(e){this._internalRoot=e}Ws.prototype.render=ef.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var n=t.current,r=sn();Pv(n,r,e,t,null,null)},Ws.prototype.unmount=ef.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Pv(e.current,2,null,e,null,null),Ds(),t[Cl]=null}};function Ws(e){this._internalRoot=e}Ws.prototype.unstable_scheduleHydration=function(e){if(e){var t=yd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ba.length&&t!==0&&t<Ba[n].priority;n++);Ba.splice(n,0,e),n===0&&Iv(e)}};var np=l.version;if(np!=="19.1.0")throw Error(s(527,np,"19.1.0"));G.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=p(t),e=e!==null?v(e):null,e=e===null?null:e.stateNode,e};var Vx={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:O,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Is=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Is.isDisabled&&Is.supportsFiber)try{be=Is.inject(Vx),Me=Is}catch{}}return Ci.createRoot=function(e,t){if(!u(e))throw Error(s(299));var n=!1,r="",o=ym,c=bm,m=xm,y=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onUncaughtError!==void 0&&(o=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(m=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(y=t.unstable_transitionCallbacks)),t=Qv(e,1,!1,null,null,n,r,o,c,m,y,null),e[Cl]=t.current,Uc(e),new ef(t)},Ci.hydrateRoot=function(e,t,n){if(!u(e))throw Error(s(299));var r=!1,o="",c=ym,m=bm,y=xm,x=null,D=null;return n!=null&&(n.unstable_strictMode===!0&&(r=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(m=n.onCaughtError),n.onRecoverableError!==void 0&&(y=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(x=n.unstable_transitionCallbacks),n.formState!==void 0&&(D=n.formState)),t=Qv(e,1,!0,t,n??null,r,o,c,m,y,x,D),t.context=Kv(null),n=t.current,r=sn(),r=Xo(r),o=Sa(r),o.callback=null,_a(n,o,r),n=r,t.current.lanes=n,Nr(t,n),Bn(t),e[Cl]=t.current,Uc(e),new Ws(t)},Ci.version="19.1.0",Ci}var dp;function Kx(){if(dp)return af.exports;dp=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),af.exports=Qx(),af.exports}var Px=Kx();function hp(a,l){if(typeof a=="function")return a(l);a!=null&&(a.current=l)}function Ag(...a){return l=>{let i=!1;const s=a.map(u=>{const f=hp(u,l);return!i&&typeof f=="function"&&(i=!0),f});if(i)return()=>{for(let u=0;u<s.length;u++){const f=s[u];typeof f=="function"?f():hp(a[u],null)}}}}function Ht(...a){return E.useCallback(Ag(...a),a)}function zi(a){const l=Fx(a),i=E.forwardRef((s,u)=>{const{children:f,...d}=s,h=E.Children.toArray(f),p=h.find(Jx);if(p){const v=p.props.children,b=h.map(S=>S===p?E.Children.count(v)>1?E.Children.only(null):E.isValidElement(v)?v.props.children:null:S);return g.jsx(l,{...d,ref:u,children:E.isValidElement(v)?E.cloneElement(v,void 0,b):null})}return g.jsx(l,{...d,ref:u,children:f})});return i.displayName=`${a}.Slot`,i}var Cg=zi("Slot");function Fx(a){const l=E.forwardRef((i,s)=>{const{children:u,...f}=i;if(E.isValidElement(u)){const d=Ix(u),h=Wx(f,u.props);return u.type!==E.Fragment&&(h.ref=s?Ag(s,d):d),E.cloneElement(u,h)}return E.Children.count(u)>1?E.Children.only(null):null});return l.displayName=`${a}.SlotClone`,l}var $x=Symbol("radix.slottable");function Jx(a){return E.isValidElement(a)&&typeof a.type=="function"&&"__radixId"in a.type&&a.type.__radixId===$x}function Wx(a,l){const i={...l};for(const s in l){const u=a[s],f=l[s];/^on[A-Z]/.test(s)?u&&f?i[s]=(...h)=>{const p=f(...h);return u(...h),p}:u&&(i[s]=u):s==="style"?i[s]={...u,...f}:s==="className"&&(i[s]=[u,f].filter(Boolean).join(" "))}return{...a,...i}}function Ix(a){let l=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,i=l&&"isReactWarning"in l&&l.isReactWarning;return i?a.ref:(l=Object.getOwnPropertyDescriptor(a,"ref")?.get,i=l&&"isReactWarning"in l&&l.isReactWarning,i?a.props.ref:a.props.ref||a.ref)}function Tg(a){var l,i,s="";if(typeof a=="string"||typeof a=="number")s+=a;else if(typeof a=="object")if(Array.isArray(a)){var u=a.length;for(l=0;l<u;l++)a[l]&&(i=Tg(a[l]))&&(s&&(s+=" "),s+=i)}else for(i in a)a[i]&&(s&&(s+=" "),s+=i);return s}function Og(){for(var a,l,i=0,s="",u=arguments.length;i<u;i++)(a=arguments[i])&&(l=Tg(a))&&(s&&(s+=" "),s+=l);return s}const mp=a=>typeof a=="boolean"?`${a}`:a===0?"0":a,vp=Og,eS=(a,l)=>i=>{var s;if(l?.variants==null)return vp(a,i?.class,i?.className);const{variants:u,defaultVariants:f}=l,d=Object.keys(u).map(v=>{const b=i?.[v],S=f?.[v];if(b===null)return null;const T=mp(b)||mp(S);return u[v][T]}),h=i&&Object.entries(i).reduce((v,b)=>{let[S,T]=b;return T===void 0||(v[S]=T),v},{}),p=l==null||(s=l.compoundVariants)===null||s===void 0?void 0:s.reduce((v,b)=>{let{class:S,className:T,...z}=b;return Object.entries(z).every(L=>{let[_,N]=L;return Array.isArray(N)?N.includes({...f,...h}[_]):{...f,...h}[_]===N})?[...v,S,T]:v},[]);return vp(a,d,p,i?.class,i?.className)},Gf="-",tS=a=>{const l=aS(a),{conflictingClassGroups:i,conflictingClassGroupModifiers:s}=a;return{getClassGroupId:d=>{const h=d.split(Gf);return h[0]===""&&h.length!==1&&h.shift(),Rg(h,l)||nS(d)},getConflictingClassGroupIds:(d,h)=>{const p=i[d]||[];return h&&s[d]?[...p,...s[d]]:p}}},Rg=(a,l)=>{if(a.length===0)return l.classGroupId;const i=a[0],s=l.nextPart.get(i),u=s?Rg(a.slice(1),s):void 0;if(u)return u;if(l.validators.length===0)return;const f=a.join(Gf);return l.validators.find(({validator:d})=>d(f))?.classGroupId},pp=/^\[(.+)\]$/,nS=a=>{if(pp.test(a)){const l=pp.exec(a)[1],i=l?.substring(0,l.indexOf(":"));if(i)return"arbitrary.."+i}},aS=a=>{const{theme:l,classGroups:i}=a,s={nextPart:new Map,validators:[]};for(const u in i)Sf(i[u],s,u,l);return s},Sf=(a,l,i,s)=>{a.forEach(u=>{if(typeof u=="string"){const f=u===""?l:gp(l,u);f.classGroupId=i;return}if(typeof u=="function"){if(lS(u)){Sf(u(s),l,i,s);return}l.validators.push({validator:u,classGroupId:i});return}Object.entries(u).forEach(([f,d])=>{Sf(d,gp(l,f),i,s)})})},gp=(a,l)=>{let i=a;return l.split(Gf).forEach(s=>{i.nextPart.has(s)||i.nextPart.set(s,{nextPart:new Map,validators:[]}),i=i.nextPart.get(s)}),i},lS=a=>a.isThemeGetter,rS=a=>{if(a<1)return{get:()=>{},set:()=>{}};let l=0,i=new Map,s=new Map;const u=(f,d)=>{i.set(f,d),l++,l>a&&(l=0,s=i,i=new Map)};return{get(f){let d=i.get(f);if(d!==void 0)return d;if((d=s.get(f))!==void 0)return u(f,d),d},set(f,d){i.has(f)?i.set(f,d):u(f,d)}}},_f="!",wf=":",iS=wf.length,sS=a=>{const{prefix:l,experimentalParseClassName:i}=a;let s=u=>{const f=[];let d=0,h=0,p=0,v;for(let L=0;L<u.length;L++){let _=u[L];if(d===0&&h===0){if(_===wf){f.push(u.slice(p,L)),p=L+iS;continue}if(_==="/"){v=L;continue}}_==="["?d++:_==="]"?d--:_==="("?h++:_===")"&&h--}const b=f.length===0?u:u.substring(p),S=oS(b),T=S!==b,z=v&&v>p?v-p:void 0;return{modifiers:f,hasImportantModifier:T,baseClassName:S,maybePostfixModifierPosition:z}};if(l){const u=l+wf,f=s;s=d=>d.startsWith(u)?f(d.substring(u.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:d,maybePostfixModifierPosition:void 0}}if(i){const u=s;s=f=>i({className:f,parseClassName:u})}return s},oS=a=>a.endsWith(_f)?a.substring(0,a.length-1):a.startsWith(_f)?a.substring(1):a,uS=a=>{const l=Object.fromEntries(a.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const u=[];let f=[];return s.forEach(d=>{d[0]==="["||l[d]?(u.push(...f.sort(),d),f=[]):f.push(d)}),u.push(...f.sort()),u}},cS=a=>({cache:rS(a.cacheSize),parseClassName:sS(a),sortModifiers:uS(a),...tS(a)}),fS=/\s+/,dS=(a,l)=>{const{parseClassName:i,getClassGroupId:s,getConflictingClassGroupIds:u,sortModifiers:f}=l,d=[],h=a.trim().split(fS);let p="";for(let v=h.length-1;v>=0;v-=1){const b=h[v],{isExternal:S,modifiers:T,hasImportantModifier:z,baseClassName:L,maybePostfixModifierPosition:_}=i(b);if(S){p=b+(p.length>0?" "+p:p);continue}let N=!!_,q=s(N?L.substring(0,_):L);if(!q){if(!N){p=b+(p.length>0?" "+p:p);continue}if(q=s(L),!q){p=b+(p.length>0?" "+p:p);continue}N=!1}const j=f(T).join(":"),H=z?j+_f:j,P=H+q;if(d.includes(P))continue;d.push(P);const Z=u(q,N);for(let se=0;se<Z.length;++se){const $=Z[se];d.push(H+$)}p=b+(p.length>0?" "+p:p)}return p};function hS(){let a=0,l,i,s="";for(;a<arguments.length;)(l=arguments[a++])&&(i=Ng(l))&&(s&&(s+=" "),s+=i);return s}const Ng=a=>{if(typeof a=="string")return a;let l,i="";for(let s=0;s<a.length;s++)a[s]&&(l=Ng(a[s]))&&(i&&(i+=" "),i+=l);return i};function mS(a,...l){let i,s,u,f=d;function d(p){const v=l.reduce((b,S)=>S(b),a());return i=cS(v),s=i.cache.get,u=i.cache.set,f=h,h(p)}function h(p){const v=s(p);if(v)return v;const b=dS(p,i);return u(p,b),b}return function(){return f(hS.apply(null,arguments))}}const Rt=a=>{const l=i=>i[a]||[];return l.isThemeGetter=!0,l},jg=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Mg=/^\((?:(\w[\w-]*):)?(.+)\)$/i,vS=/^\d+\/\d+$/,pS=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,gS=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,yS=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,bS=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,xS=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,fr=a=>vS.test(a),ke=a=>!!a&&!Number.isNaN(Number(a)),Ha=a=>!!a&&Number.isInteger(Number(a)),of=a=>a.endsWith("%")&&ke(a.slice(0,-1)),oa=a=>pS.test(a),SS=()=>!0,_S=a=>gS.test(a)&&!yS.test(a),Dg=()=>!1,wS=a=>bS.test(a),ES=a=>xS.test(a),AS=a=>!he(a)&&!me(a),CS=a=>Er(a,Ug,Dg),he=a=>jg.test(a),yl=a=>Er(a,Vg,_S),uf=a=>Er(a,jS,ke),yp=a=>Er(a,zg,Dg),TS=a=>Er(a,kg,ES),eo=a=>Er(a,Bg,wS),me=a=>Mg.test(a),Ti=a=>Ar(a,Vg),OS=a=>Ar(a,MS),bp=a=>Ar(a,zg),RS=a=>Ar(a,Ug),NS=a=>Ar(a,kg),to=a=>Ar(a,Bg,!0),Er=(a,l,i)=>{const s=jg.exec(a);return s?s[1]?l(s[1]):i(s[2]):!1},Ar=(a,l,i=!1)=>{const s=Mg.exec(a);return s?s[1]?l(s[1]):i:!1},zg=a=>a==="position"||a==="percentage",kg=a=>a==="image"||a==="url",Ug=a=>a==="length"||a==="size"||a==="bg-size",Vg=a=>a==="length",jS=a=>a==="number",MS=a=>a==="family-name",Bg=a=>a==="shadow",DS=()=>{const a=Rt("color"),l=Rt("font"),i=Rt("text"),s=Rt("font-weight"),u=Rt("tracking"),f=Rt("leading"),d=Rt("breakpoint"),h=Rt("container"),p=Rt("spacing"),v=Rt("radius"),b=Rt("shadow"),S=Rt("inset-shadow"),T=Rt("text-shadow"),z=Rt("drop-shadow"),L=Rt("blur"),_=Rt("perspective"),N=Rt("aspect"),q=Rt("ease"),j=Rt("animate"),H=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],Z=()=>[...P(),me,he],se=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto","contain","none"],J=()=>[me,he,p],ge=()=>[fr,"full","auto",...J()],Te=()=>[Ha,"none","subgrid",me,he],Ce=()=>["auto",{span:["full",Ha,me,he]},Ha,me,he],ne=()=>[Ha,"auto",me,he],ce=()=>["auto","min","max","fr",me,he],fe=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],ve=()=>["start","end","center","stretch","center-safe","end-safe"],O=()=>["auto",...J()],G=()=>[fr,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...J()],V=()=>[a,me,he],pe=()=>[...P(),bp,yp,{position:[me,he]}],w=()=>["no-repeat",{repeat:["","x","y","space","round"]}],K=()=>["auto","cover","contain",RS,CS,{size:[me,he]}],ae=()=>[of,Ti,yl],W=()=>["","none","full",v,me,he],re=()=>["",ke,Ti,yl],Re=()=>["solid","dashed","dotted","double"],Se=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],te=()=>[ke,of,bp,yp],we=()=>["","none",L,me,he],Xe=()=>["none",ke,me,he],Ue=()=>["none",ke,me,he],Ze=()=>[ke,me,he],Ke=()=>[fr,"full",...J()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[oa],breakpoint:[oa],color:[SS],container:[oa],"drop-shadow":[oa],ease:["in","out","in-out"],font:[AS],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[oa],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[oa],shadow:[oa],spacing:["px",ke],text:[oa],"text-shadow":[oa],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",fr,he,me,N]}],container:["container"],columns:[{columns:[ke,he,me,h]}],"break-after":[{"break-after":H()}],"break-before":[{"break-before":H()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:Z()}],overflow:[{overflow:se()}],"overflow-x":[{"overflow-x":se()}],"overflow-y":[{"overflow-y":se()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:ge()}],"inset-x":[{"inset-x":ge()}],"inset-y":[{"inset-y":ge()}],start:[{start:ge()}],end:[{end:ge()}],top:[{top:ge()}],right:[{right:ge()}],bottom:[{bottom:ge()}],left:[{left:ge()}],visibility:["visible","invisible","collapse"],z:[{z:[Ha,"auto",me,he]}],basis:[{basis:[fr,"full","auto",h,...J()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ke,fr,"auto","initial","none",he]}],grow:[{grow:["",ke,me,he]}],shrink:[{shrink:["",ke,me,he]}],order:[{order:[Ha,"first","last","none",me,he]}],"grid-cols":[{"grid-cols":Te()}],"col-start-end":[{col:Ce()}],"col-start":[{"col-start":ne()}],"col-end":[{"col-end":ne()}],"grid-rows":[{"grid-rows":Te()}],"row-start-end":[{row:Ce()}],"row-start":[{"row-start":ne()}],"row-end":[{"row-end":ne()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ce()}],"auto-rows":[{"auto-rows":ce()}],gap:[{gap:J()}],"gap-x":[{"gap-x":J()}],"gap-y":[{"gap-y":J()}],"justify-content":[{justify:[...fe(),"normal"]}],"justify-items":[{"justify-items":[...ve(),"normal"]}],"justify-self":[{"justify-self":["auto",...ve()]}],"align-content":[{content:["normal",...fe()]}],"align-items":[{items:[...ve(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...ve(),{baseline:["","last"]}]}],"place-content":[{"place-content":fe()}],"place-items":[{"place-items":[...ve(),"baseline"]}],"place-self":[{"place-self":["auto",...ve()]}],p:[{p:J()}],px:[{px:J()}],py:[{py:J()}],ps:[{ps:J()}],pe:[{pe:J()}],pt:[{pt:J()}],pr:[{pr:J()}],pb:[{pb:J()}],pl:[{pl:J()}],m:[{m:O()}],mx:[{mx:O()}],my:[{my:O()}],ms:[{ms:O()}],me:[{me:O()}],mt:[{mt:O()}],mr:[{mr:O()}],mb:[{mb:O()}],ml:[{ml:O()}],"space-x":[{"space-x":J()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":J()}],"space-y-reverse":["space-y-reverse"],size:[{size:G()}],w:[{w:[h,"screen",...G()]}],"min-w":[{"min-w":[h,"screen","none",...G()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[d]},...G()]}],h:[{h:["screen","lh",...G()]}],"min-h":[{"min-h":["screen","lh","none",...G()]}],"max-h":[{"max-h":["screen","lh",...G()]}],"font-size":[{text:["base",i,Ti,yl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,me,uf]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",of,he]}],"font-family":[{font:[OS,he,l]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[u,me,he]}],"line-clamp":[{"line-clamp":[ke,"none",me,uf]}],leading:[{leading:[f,...J()]}],"list-image":[{"list-image":["none",me,he]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",me,he]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:V()}],"text-color":[{text:V()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Re(),"wavy"]}],"text-decoration-thickness":[{decoration:[ke,"from-font","auto",me,yl]}],"text-decoration-color":[{decoration:V()}],"underline-offset":[{"underline-offset":[ke,"auto",me,he]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:J()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",me,he]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",me,he]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:pe()}],"bg-repeat":[{bg:w()}],"bg-size":[{bg:K()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ha,me,he],radial:["",me,he],conic:[Ha,me,he]},NS,TS]}],"bg-color":[{bg:V()}],"gradient-from-pos":[{from:ae()}],"gradient-via-pos":[{via:ae()}],"gradient-to-pos":[{to:ae()}],"gradient-from":[{from:V()}],"gradient-via":[{via:V()}],"gradient-to":[{to:V()}],rounded:[{rounded:W()}],"rounded-s":[{"rounded-s":W()}],"rounded-e":[{"rounded-e":W()}],"rounded-t":[{"rounded-t":W()}],"rounded-r":[{"rounded-r":W()}],"rounded-b":[{"rounded-b":W()}],"rounded-l":[{"rounded-l":W()}],"rounded-ss":[{"rounded-ss":W()}],"rounded-se":[{"rounded-se":W()}],"rounded-ee":[{"rounded-ee":W()}],"rounded-es":[{"rounded-es":W()}],"rounded-tl":[{"rounded-tl":W()}],"rounded-tr":[{"rounded-tr":W()}],"rounded-br":[{"rounded-br":W()}],"rounded-bl":[{"rounded-bl":W()}],"border-w":[{border:re()}],"border-w-x":[{"border-x":re()}],"border-w-y":[{"border-y":re()}],"border-w-s":[{"border-s":re()}],"border-w-e":[{"border-e":re()}],"border-w-t":[{"border-t":re()}],"border-w-r":[{"border-r":re()}],"border-w-b":[{"border-b":re()}],"border-w-l":[{"border-l":re()}],"divide-x":[{"divide-x":re()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":re()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...Re(),"hidden","none"]}],"divide-style":[{divide:[...Re(),"hidden","none"]}],"border-color":[{border:V()}],"border-color-x":[{"border-x":V()}],"border-color-y":[{"border-y":V()}],"border-color-s":[{"border-s":V()}],"border-color-e":[{"border-e":V()}],"border-color-t":[{"border-t":V()}],"border-color-r":[{"border-r":V()}],"border-color-b":[{"border-b":V()}],"border-color-l":[{"border-l":V()}],"divide-color":[{divide:V()}],"outline-style":[{outline:[...Re(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ke,me,he]}],"outline-w":[{outline:["",ke,Ti,yl]}],"outline-color":[{outline:V()}],shadow:[{shadow:["","none",b,to,eo]}],"shadow-color":[{shadow:V()}],"inset-shadow":[{"inset-shadow":["none",S,to,eo]}],"inset-shadow-color":[{"inset-shadow":V()}],"ring-w":[{ring:re()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:V()}],"ring-offset-w":[{"ring-offset":[ke,yl]}],"ring-offset-color":[{"ring-offset":V()}],"inset-ring-w":[{"inset-ring":re()}],"inset-ring-color":[{"inset-ring":V()}],"text-shadow":[{"text-shadow":["none",T,to,eo]}],"text-shadow-color":[{"text-shadow":V()}],opacity:[{opacity:[ke,me,he]}],"mix-blend":[{"mix-blend":[...Se(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Se()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ke]}],"mask-image-linear-from-pos":[{"mask-linear-from":te()}],"mask-image-linear-to-pos":[{"mask-linear-to":te()}],"mask-image-linear-from-color":[{"mask-linear-from":V()}],"mask-image-linear-to-color":[{"mask-linear-to":V()}],"mask-image-t-from-pos":[{"mask-t-from":te()}],"mask-image-t-to-pos":[{"mask-t-to":te()}],"mask-image-t-from-color":[{"mask-t-from":V()}],"mask-image-t-to-color":[{"mask-t-to":V()}],"mask-image-r-from-pos":[{"mask-r-from":te()}],"mask-image-r-to-pos":[{"mask-r-to":te()}],"mask-image-r-from-color":[{"mask-r-from":V()}],"mask-image-r-to-color":[{"mask-r-to":V()}],"mask-image-b-from-pos":[{"mask-b-from":te()}],"mask-image-b-to-pos":[{"mask-b-to":te()}],"mask-image-b-from-color":[{"mask-b-from":V()}],"mask-image-b-to-color":[{"mask-b-to":V()}],"mask-image-l-from-pos":[{"mask-l-from":te()}],"mask-image-l-to-pos":[{"mask-l-to":te()}],"mask-image-l-from-color":[{"mask-l-from":V()}],"mask-image-l-to-color":[{"mask-l-to":V()}],"mask-image-x-from-pos":[{"mask-x-from":te()}],"mask-image-x-to-pos":[{"mask-x-to":te()}],"mask-image-x-from-color":[{"mask-x-from":V()}],"mask-image-x-to-color":[{"mask-x-to":V()}],"mask-image-y-from-pos":[{"mask-y-from":te()}],"mask-image-y-to-pos":[{"mask-y-to":te()}],"mask-image-y-from-color":[{"mask-y-from":V()}],"mask-image-y-to-color":[{"mask-y-to":V()}],"mask-image-radial":[{"mask-radial":[me,he]}],"mask-image-radial-from-pos":[{"mask-radial-from":te()}],"mask-image-radial-to-pos":[{"mask-radial-to":te()}],"mask-image-radial-from-color":[{"mask-radial-from":V()}],"mask-image-radial-to-color":[{"mask-radial-to":V()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":P()}],"mask-image-conic-pos":[{"mask-conic":[ke]}],"mask-image-conic-from-pos":[{"mask-conic-from":te()}],"mask-image-conic-to-pos":[{"mask-conic-to":te()}],"mask-image-conic-from-color":[{"mask-conic-from":V()}],"mask-image-conic-to-color":[{"mask-conic-to":V()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:pe()}],"mask-repeat":[{mask:w()}],"mask-size":[{mask:K()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",me,he]}],filter:[{filter:["","none",me,he]}],blur:[{blur:we()}],brightness:[{brightness:[ke,me,he]}],contrast:[{contrast:[ke,me,he]}],"drop-shadow":[{"drop-shadow":["","none",z,to,eo]}],"drop-shadow-color":[{"drop-shadow":V()}],grayscale:[{grayscale:["",ke,me,he]}],"hue-rotate":[{"hue-rotate":[ke,me,he]}],invert:[{invert:["",ke,me,he]}],saturate:[{saturate:[ke,me,he]}],sepia:[{sepia:["",ke,me,he]}],"backdrop-filter":[{"backdrop-filter":["","none",me,he]}],"backdrop-blur":[{"backdrop-blur":we()}],"backdrop-brightness":[{"backdrop-brightness":[ke,me,he]}],"backdrop-contrast":[{"backdrop-contrast":[ke,me,he]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ke,me,he]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ke,me,he]}],"backdrop-invert":[{"backdrop-invert":["",ke,me,he]}],"backdrop-opacity":[{"backdrop-opacity":[ke,me,he]}],"backdrop-saturate":[{"backdrop-saturate":[ke,me,he]}],"backdrop-sepia":[{"backdrop-sepia":["",ke,me,he]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":J()}],"border-spacing-x":[{"border-spacing-x":J()}],"border-spacing-y":[{"border-spacing-y":J()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",me,he]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ke,"initial",me,he]}],ease:[{ease:["linear","initial",q,me,he]}],delay:[{delay:[ke,me,he]}],animate:[{animate:["none",j,me,he]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[_,me,he]}],"perspective-origin":[{"perspective-origin":Z()}],rotate:[{rotate:Xe()}],"rotate-x":[{"rotate-x":Xe()}],"rotate-y":[{"rotate-y":Xe()}],"rotate-z":[{"rotate-z":Xe()}],scale:[{scale:Ue()}],"scale-x":[{"scale-x":Ue()}],"scale-y":[{"scale-y":Ue()}],"scale-z":[{"scale-z":Ue()}],"scale-3d":["scale-3d"],skew:[{skew:Ze()}],"skew-x":[{"skew-x":Ze()}],"skew-y":[{"skew-y":Ze()}],transform:[{transform:[me,he,"","none","gpu","cpu"]}],"transform-origin":[{origin:Z()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ke()}],"translate-x":[{"translate-x":Ke()}],"translate-y":[{"translate-y":Ke()}],"translate-z":[{"translate-z":Ke()}],"translate-none":["translate-none"],accent:[{accent:V()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:V()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",me,he]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":J()}],"scroll-mx":[{"scroll-mx":J()}],"scroll-my":[{"scroll-my":J()}],"scroll-ms":[{"scroll-ms":J()}],"scroll-me":[{"scroll-me":J()}],"scroll-mt":[{"scroll-mt":J()}],"scroll-mr":[{"scroll-mr":J()}],"scroll-mb":[{"scroll-mb":J()}],"scroll-ml":[{"scroll-ml":J()}],"scroll-p":[{"scroll-p":J()}],"scroll-px":[{"scroll-px":J()}],"scroll-py":[{"scroll-py":J()}],"scroll-ps":[{"scroll-ps":J()}],"scroll-pe":[{"scroll-pe":J()}],"scroll-pt":[{"scroll-pt":J()}],"scroll-pr":[{"scroll-pr":J()}],"scroll-pb":[{"scroll-pb":J()}],"scroll-pl":[{"scroll-pl":J()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",me,he]}],fill:[{fill:["none",...V()]}],"stroke-w":[{stroke:[ke,Ti,yl,uf]}],stroke:[{stroke:["none",...V()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},zS=mS(DS);function jt(...a){return zS(Og(a))}const kS=eS("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function ca({className:a,variant:l,size:i,asChild:s=!1,...u}){const f=s?Cg:"button";return g.jsx(f,{"data-slot":"button",className:jt(kS({variant:l,size:i,className:a})),...u})}function US({currentPage:a,onNavigate:l}){return g.jsx("nav",{className:"bg-white shadow-sm border-b",children:g.jsx("div",{className:"container mx-auto px-4",children:g.jsxs("div",{className:"flex items-center justify-between h-16",children:[g.jsxs("div",{className:"flex items-center space-x-4",children:[g.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Invoicer"}),g.jsx("span",{className:"text-sm text-gray-500",children:"Bulgarian Invoicing System"})]}),g.jsxs("div",{className:"flex items-center space-x-4",children:[g.jsx(ca,{variant:a==="home"?"default":"ghost",onClick:()=>l("home"),children:"Home"}),g.jsx(ca,{variant:a==="create-company"?"default":"ghost",onClick:()=>l("create-company"),children:"Create Company"}),g.jsx(ca,{variant:a==="create-customer"?"default":"ghost",onClick:()=>l("create-customer"),children:"Create Customer"})]})]})})})}function mo({className:a,...l}){return g.jsx("div",{"data-slot":"card",className:jt("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...l})}function vo({className:a,...l}){return g.jsx("div",{"data-slot":"card-header",className:jt("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...l})}function po({className:a,...l}){return g.jsx("div",{"data-slot":"card-title",className:jt("leading-none font-semibold",a),...l})}function go({className:a,...l}){return g.jsx("div",{"data-slot":"card-description",className:jt("text-muted-foreground text-sm",a),...l})}function yo({className:a,...l}){return g.jsx("div",{"data-slot":"card-content",className:jt("px-6",a),...l})}function xp({onNavigate:a}){return g.jsx("div",{className:"container mx-auto py-8 px-4",children:g.jsxs("div",{className:"max-w-4xl mx-auto",children:[g.jsxs("div",{className:"text-center mb-12",children:[g.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Welcome to Invoicer"}),g.jsx("p",{className:"text-xl text-gray-600 mb-2",children:"Bulgarian Invoicing System"}),g.jsx("p",{className:"text-gray-500",children:"Manage your companies, customers, and invoices with Bulgarian business compliance"})]}),g.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-12",children:[g.jsxs(mo,{className:"hover:shadow-lg transition-shadow cursor-pointer",onClick:()=>a("create-company"),children:[g.jsxs(vo,{children:[g.jsx(po,{className:"flex items-center gap-2",children:"🏢 Create Company"}),g.jsx(go,{children:"Add a new company (доставчик на услугите/продуктите) to your system"})]}),g.jsxs(yo,{children:[g.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Register companies with Bulgarian ЕИК/Булстат numbers, VAT information, and contact details."}),g.jsx(ca,{className:"w-full",children:"Create New Company"})]})]}),g.jsxs(mo,{className:"hover:shadow-lg transition-shadow cursor-pointer",onClick:()=>a("create-customer"),children:[g.jsxs(vo,{children:[g.jsx(po,{className:"flex items-center gap-2",children:"👥 Create Customer"}),g.jsx(go,{children:"Add residential or business customers to your system"})]}),g.jsxs(yo,{children:[g.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Register customers with proper Bulgarian identification - ЕИК/Булстат for businesses, ЕГН for individuals."}),g.jsx(ca,{className:"w-full",children:"Create New Customer"})]})]})]}),g.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[g.jsx("h2",{className:"text-lg font-semibold text-blue-900 mb-2",children:"🇧🇬 Bulgarian Business Compliance"}),g.jsxs("div",{className:"text-sm text-blue-800 space-y-2",children:[g.jsxs("p",{children:[g.jsx("strong",{children:"Companies:"})," Must have valid ЕИК/Булстат identification numbers (9-13 digits)"]}),g.jsxs("p",{children:[g.jsx("strong",{children:"Business Customers:"})," Require ЕИК/Булстат numbers for proper invoicing"]}),g.jsxs("p",{children:[g.jsx("strong",{children:"Residential Customers:"})," Use ЕГН (Personal ID) numbers for identification"]}),g.jsxs("p",{children:[g.jsx("strong",{children:"VAT:"})," Default Bulgarian VAT rate of 20% applied to invoices"]})]})]})]})})}var Hi=a=>a.type==="checkbox",bl=a=>a instanceof Date,Qt=a=>a==null;const Lg=a=>typeof a=="object";var xt=a=>!Qt(a)&&!Array.isArray(a)&&Lg(a)&&!bl(a),Hg=a=>xt(a)&&a.target?Hi(a.target)?a.target.checked:a.target.value:a,VS=a=>a.substring(0,a.search(/\.\d+(\.|$)/))||a,qg=(a,l)=>a.has(VS(l)),BS=a=>{const l=a.constructor&&a.constructor.prototype;return xt(l)&&l.hasOwnProperty("isPrototypeOf")},Xf=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function Nt(a){let l;const i=Array.isArray(a),s=typeof FileList<"u"?a instanceof FileList:!1;if(a instanceof Date)l=new Date(a);else if(!(Xf&&(a instanceof Blob||s))&&(i||xt(a)))if(l=i?[]:{},!i&&!BS(a))l=a;else for(const u in a)a.hasOwnProperty(u)&&(l[u]=Nt(a[u]));else return a;return l}var Do=a=>/^\w*$/.test(a),bt=a=>a===void 0,Qf=a=>Array.isArray(a)?a.filter(Boolean):[],Kf=a=>Qf(a.replace(/["|']|\]/g,"").split(/\.|\[/)),ie=(a,l,i)=>{if(!l||!xt(a))return i;const s=(Do(l)?[l]:Kf(l)).reduce((u,f)=>Qt(u)?u:u[f],a);return bt(s)||s===a?bt(a[l])?i:a[l]:s},cn=a=>typeof a=="boolean",Ie=(a,l,i)=>{let s=-1;const u=Do(l)?[l]:Kf(l),f=u.length,d=f-1;for(;++s<f;){const h=u[s];let p=i;if(s!==d){const v=a[h];p=xt(v)||Array.isArray(v)?v:isNaN(+u[s+1])?{}:[]}if(h==="__proto__"||h==="constructor"||h==="prototype")return;a[h]=p,a=a[h]}};const bo={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},Rn={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},ua={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Pf=Be.createContext(null);Pf.displayName="HookFormContext";const zo=()=>Be.useContext(Pf),LS=a=>{const{children:l,...i}=a;return Be.createElement(Pf.Provider,{value:i},l)};var Zg=(a,l,i,s=!0)=>{const u={defaultValues:l._defaultValues};for(const f in a)Object.defineProperty(u,f,{get:()=>{const d=f;return l._proxyFormState[d]!==Rn.all&&(l._proxyFormState[d]=!s||Rn.all),i&&(i[d]=!0),a[d]}});return u};const Ff=typeof window<"u"?E.useLayoutEffect:E.useEffect;function Yg(a){const l=zo(),{control:i=l.control,disabled:s,name:u,exact:f}=a||{},[d,h]=Be.useState(i._formState),p=Be.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return Ff(()=>i._subscribe({name:u,formState:p.current,exact:f,callback:v=>{!s&&h({...i._formState,...v})}}),[u,s,f]),Be.useEffect(()=>{p.current.isValid&&i._setValid(!0)},[i]),Be.useMemo(()=>Zg(d,i,p.current,!1),[d,i])}var Hn=a=>typeof a=="string",Gg=(a,l,i,s,u)=>Hn(a)?(s&&l.watch.add(a),ie(i,a,u)):Array.isArray(a)?a.map(f=>(s&&l.watch.add(f),ie(i,f))):(s&&(l.watchAll=!0),i);function HS(a){const l=zo(),{control:i=l.control,name:s,defaultValue:u,disabled:f,exact:d}=a||{},h=Be.useRef(u),[p,v]=Be.useState(i._getWatch(s,h.current));return Ff(()=>i._subscribe({name:s,formState:{values:!0},exact:d,callback:b=>!f&&v(Gg(s,i._names,b.values||i._formValues,!1,h.current))}),[s,i,f,d]),Be.useEffect(()=>i._removeUnmounted()),p}function qS(a){const l=zo(),{name:i,disabled:s,control:u=l.control,shouldUnregister:f}=a,d=qg(u._names.array,i),h=HS({control:u,name:i,defaultValue:ie(u._formValues,i,ie(u._defaultValues,i,a.defaultValue)),exact:!0}),p=Yg({control:u,name:i,exact:!0}),v=Be.useRef(a),b=Be.useRef(u.register(i,{...a.rules,value:h,...cn(a.disabled)?{disabled:a.disabled}:{}})),S=Be.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!ie(p.errors,i)},isDirty:{enumerable:!0,get:()=>!!ie(p.dirtyFields,i)},isTouched:{enumerable:!0,get:()=>!!ie(p.touchedFields,i)},isValidating:{enumerable:!0,get:()=>!!ie(p.validatingFields,i)},error:{enumerable:!0,get:()=>ie(p.errors,i)}}),[p,i]),T=Be.useCallback(N=>b.current.onChange({target:{value:Hg(N),name:i},type:bo.CHANGE}),[i]),z=Be.useCallback(()=>b.current.onBlur({target:{value:ie(u._formValues,i),name:i},type:bo.BLUR}),[i,u._formValues]),L=Be.useCallback(N=>{const q=ie(u._fields,i);q&&N&&(q._f.ref={focus:()=>N.focus&&N.focus(),select:()=>N.select&&N.select(),setCustomValidity:j=>N.setCustomValidity(j),reportValidity:()=>N.reportValidity()})},[u._fields,i]),_=Be.useMemo(()=>({name:i,value:h,...cn(s)||p.disabled?{disabled:p.disabled||s}:{},onChange:T,onBlur:z,ref:L}),[i,s,p.disabled,T,z,L,h]);return Be.useEffect(()=>{const N=u._options.shouldUnregister||f;u.register(i,{...v.current.rules,...cn(v.current.disabled)?{disabled:v.current.disabled}:{}});const q=(j,H)=>{const P=ie(u._fields,j);P&&P._f&&(P._f.mount=H)};if(q(i,!0),N){const j=Nt(ie(u._options.defaultValues,i));Ie(u._defaultValues,i,j),bt(ie(u._formValues,i))&&Ie(u._formValues,i,j)}return!d&&u.register(i),()=>{(d?N&&!u._state.action:N)?u.unregister(i):q(i,!1)}},[i,u,d,f]),Be.useEffect(()=>{u._setDisabledField({disabled:s,name:i})},[s,i,u]),Be.useMemo(()=>({field:_,formState:p,fieldState:S}),[_,p,S])}const ZS=a=>a.render(qS(a));var $f=(a,l,i,s,u)=>l?{...i[a],types:{...i[a]&&i[a].types?i[a].types:{},[s]:u||!0}}:{},Mi=a=>Array.isArray(a)?a:[a],Sp=()=>{let a=[];return{get observers(){return a},next:u=>{for(const f of a)f.next&&f.next(u)},subscribe:u=>(a.push(u),{unsubscribe:()=>{a=a.filter(f=>f!==u)}}),unsubscribe:()=>{a=[]}}},Ef=a=>Qt(a)||!Lg(a);function Ya(a,l,i=new WeakSet){if(Ef(a)||Ef(l))return a===l;if(bl(a)&&bl(l))return a.getTime()===l.getTime();const s=Object.keys(a),u=Object.keys(l);if(s.length!==u.length)return!1;if(i.has(a)||i.has(l))return!0;i.add(a),i.add(l);for(const f of s){const d=a[f];if(!u.includes(f))return!1;if(f!=="ref"){const h=l[f];if(bl(d)&&bl(h)||xt(d)&&xt(h)||Array.isArray(d)&&Array.isArray(h)?!Ya(d,h,i):d!==h)return!1}}return!0}var It=a=>xt(a)&&!Object.keys(a).length,Jf=a=>a.type==="file",Nn=a=>typeof a=="function",xo=a=>{if(!Xf)return!1;const l=a?a.ownerDocument:0;return a instanceof(l&&l.defaultView?l.defaultView.HTMLElement:HTMLElement)},Xg=a=>a.type==="select-multiple",Wf=a=>a.type==="radio",YS=a=>Wf(a)||Hi(a),cf=a=>xo(a)&&a.isConnected;function GS(a,l){const i=l.slice(0,-1).length;let s=0;for(;s<i;)a=bt(a)?s++:a[l[s++]];return a}function XS(a){for(const l in a)if(a.hasOwnProperty(l)&&!bt(a[l]))return!1;return!0}function wt(a,l){const i=Array.isArray(l)?l:Do(l)?[l]:Kf(l),s=i.length===1?a:GS(a,i),u=i.length-1,f=i[u];return s&&delete s[f],u!==0&&(xt(s)&&It(s)||Array.isArray(s)&&XS(s))&&wt(a,i.slice(0,-1)),a}var Qg=a=>{for(const l in a)if(Nn(a[l]))return!0;return!1};function So(a,l={}){const i=Array.isArray(a);if(xt(a)||i)for(const s in a)Array.isArray(a[s])||xt(a[s])&&!Qg(a[s])?(l[s]=Array.isArray(a[s])?[]:{},So(a[s],l[s])):Qt(a[s])||(l[s]=!0);return l}function Kg(a,l,i){const s=Array.isArray(a);if(xt(a)||s)for(const u in a)Array.isArray(a[u])||xt(a[u])&&!Qg(a[u])?bt(l)||Ef(i[u])?i[u]=Array.isArray(a[u])?So(a[u],[]):{...So(a[u])}:Kg(a[u],Qt(l)?{}:l[u],i[u]):i[u]=!Ya(a[u],l[u]);return i}var Oi=(a,l)=>Kg(a,l,So(l));const _p={value:!1,isValid:!1},wp={value:!0,isValid:!0};var Pg=a=>{if(Array.isArray(a)){if(a.length>1){const l=a.filter(i=>i&&i.checked&&!i.disabled).map(i=>i.value);return{value:l,isValid:!!l.length}}return a[0].checked&&!a[0].disabled?a[0].attributes&&!bt(a[0].attributes.value)?bt(a[0].value)||a[0].value===""?wp:{value:a[0].value,isValid:!0}:wp:_p}return _p},Fg=(a,{valueAsNumber:l,valueAsDate:i,setValueAs:s})=>bt(a)?a:l?a===""?NaN:a&&+a:i&&Hn(a)?new Date(a):s?s(a):a;const Ep={isValid:!1,value:null};var $g=a=>Array.isArray(a)?a.reduce((l,i)=>i&&i.checked&&!i.disabled?{isValid:!0,value:i.value}:l,Ep):Ep;function Ap(a){const l=a.ref;return Jf(l)?l.files:Wf(l)?$g(a.refs).value:Xg(l)?[...l.selectedOptions].map(({value:i})=>i):Hi(l)?Pg(a.refs).value:Fg(bt(l.value)?a.ref.value:l.value,a)}var QS=(a,l,i,s)=>{const u={};for(const f of a){const d=ie(l,f);d&&Ie(u,f,d._f)}return{criteriaMode:i,names:[...a],fields:u,shouldUseNativeValidation:s}},_o=a=>a instanceof RegExp,Ri=a=>bt(a)?a:_o(a)?a.source:xt(a)?_o(a.value)?a.value.source:a.value:a,Cp=a=>({isOnSubmit:!a||a===Rn.onSubmit,isOnBlur:a===Rn.onBlur,isOnChange:a===Rn.onChange,isOnAll:a===Rn.all,isOnTouch:a===Rn.onTouched});const Tp="AsyncFunction";var KS=a=>!!a&&!!a.validate&&!!(Nn(a.validate)&&a.validate.constructor.name===Tp||xt(a.validate)&&Object.values(a.validate).find(l=>l.constructor.name===Tp)),PS=a=>a.mount&&(a.required||a.min||a.max||a.maxLength||a.minLength||a.pattern||a.validate),Op=(a,l,i)=>!i&&(l.watchAll||l.watch.has(a)||[...l.watch].some(s=>a.startsWith(s)&&/^\.\w+/.test(a.slice(s.length))));const Di=(a,l,i,s)=>{for(const u of i||Object.keys(a)){const f=ie(a,u);if(f){const{_f:d,...h}=f;if(d){if(d.refs&&d.refs[0]&&l(d.refs[0],u)&&!s)return!0;if(d.ref&&l(d.ref,d.name)&&!s)return!0;if(Di(h,l))break}else if(xt(h)&&Di(h,l))break}}};function Rp(a,l,i){const s=ie(a,i);if(s||Do(i))return{error:s,name:i};const u=i.split(".");for(;u.length;){const f=u.join("."),d=ie(l,f),h=ie(a,f);if(d&&!Array.isArray(d)&&i!==f)return{name:i};if(h&&h.type)return{name:f,error:h};if(h&&h.root&&h.root.type)return{name:`${f}.root`,error:h.root};u.pop()}return{name:i}}var FS=(a,l,i,s)=>{i(a);const{name:u,...f}=a;return It(f)||Object.keys(f).length>=Object.keys(l).length||Object.keys(f).find(d=>l[d]===(!s||Rn.all))},$S=(a,l,i)=>!a||!l||a===l||Mi(a).some(s=>s&&(i?s===l:s.startsWith(l)||l.startsWith(s))),JS=(a,l,i,s,u)=>u.isOnAll?!1:!i&&u.isOnTouch?!(l||a):(i?s.isOnBlur:u.isOnBlur)?!a:(i?s.isOnChange:u.isOnChange)?a:!0,WS=(a,l)=>!Qf(ie(a,l)).length&&wt(a,l),IS=(a,l,i)=>{const s=Mi(ie(a,i));return Ie(s,"root",l[i]),Ie(a,i,s),a},oo=a=>Hn(a);function Np(a,l,i="validate"){if(oo(a)||Array.isArray(a)&&a.every(oo)||cn(a)&&!a)return{type:i,message:oo(a)?a:"",ref:l}}var dr=a=>xt(a)&&!_o(a)?a:{value:a,message:""},jp=async(a,l,i,s,u,f)=>{const{ref:d,refs:h,required:p,maxLength:v,minLength:b,min:S,max:T,pattern:z,validate:L,name:_,valueAsNumber:N,mount:q}=a._f,j=ie(i,_);if(!q||l.has(_))return{};const H=h?h[0]:d,P=ne=>{u&&H.reportValidity&&(H.setCustomValidity(cn(ne)?"":ne||""),H.reportValidity())},Z={},se=Wf(d),$=Hi(d),J=se||$,ge=(N||Jf(d))&&bt(d.value)&&bt(j)||xo(d)&&d.value===""||j===""||Array.isArray(j)&&!j.length,Te=$f.bind(null,_,s,Z),Ce=(ne,ce,fe,ve=ua.maxLength,O=ua.minLength)=>{const G=ne?ce:fe;Z[_]={type:ne?ve:O,message:G,ref:d,...Te(ne?ve:O,G)}};if(f?!Array.isArray(j)||!j.length:p&&(!J&&(ge||Qt(j))||cn(j)&&!j||$&&!Pg(h).isValid||se&&!$g(h).isValid)){const{value:ne,message:ce}=oo(p)?{value:!!p,message:p}:dr(p);if(ne&&(Z[_]={type:ua.required,message:ce,ref:H,...Te(ua.required,ce)},!s))return P(ce),Z}if(!ge&&(!Qt(S)||!Qt(T))){let ne,ce;const fe=dr(T),ve=dr(S);if(!Qt(j)&&!isNaN(j)){const O=d.valueAsNumber||j&&+j;Qt(fe.value)||(ne=O>fe.value),Qt(ve.value)||(ce=O<ve.value)}else{const O=d.valueAsDate||new Date(j),G=w=>new Date(new Date().toDateString()+" "+w),V=d.type=="time",pe=d.type=="week";Hn(fe.value)&&j&&(ne=V?G(j)>G(fe.value):pe?j>fe.value:O>new Date(fe.value)),Hn(ve.value)&&j&&(ce=V?G(j)<G(ve.value):pe?j<ve.value:O<new Date(ve.value))}if((ne||ce)&&(Ce(!!ne,fe.message,ve.message,ua.max,ua.min),!s))return P(Z[_].message),Z}if((v||b)&&!ge&&(Hn(j)||f&&Array.isArray(j))){const ne=dr(v),ce=dr(b),fe=!Qt(ne.value)&&j.length>+ne.value,ve=!Qt(ce.value)&&j.length<+ce.value;if((fe||ve)&&(Ce(fe,ne.message,ce.message),!s))return P(Z[_].message),Z}if(z&&!ge&&Hn(j)){const{value:ne,message:ce}=dr(z);if(_o(ne)&&!j.match(ne)&&(Z[_]={type:ua.pattern,message:ce,ref:d,...Te(ua.pattern,ce)},!s))return P(ce),Z}if(L){if(Nn(L)){const ne=await L(j,i),ce=Np(ne,H);if(ce&&(Z[_]={...ce,...Te(ua.validate,ce.message)},!s))return P(ce.message),Z}else if(xt(L)){let ne={};for(const ce in L){if(!It(ne)&&!s)break;const fe=Np(await L[ce](j,i),H,ce);fe&&(ne={...fe,...Te(ce,fe.message)},P(fe.message),s&&(Z[_]=ne))}if(!It(ne)&&(Z[_]={ref:H,...ne},!s))return Z}}return P(!0),Z};const e_={mode:Rn.onSubmit,reValidateMode:Rn.onChange,shouldFocusError:!0};function t_(a={}){let l={...e_,...a},i={submitCount:0,isDirty:!1,isReady:!1,isLoading:Nn(l.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:l.errors||{},disabled:l.disabled||!1},s={},u=xt(l.defaultValues)||xt(l.values)?Nt(l.defaultValues||l.values)||{}:{},f=l.shouldUnregister?{}:Nt(u),d={action:!1,mount:!1,watch:!1},h={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},p,v=0;const b={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let S={...b};const T={array:Sp(),state:Sp()},z=l.criteriaMode===Rn.all,L=A=>k=>{clearTimeout(v),v=setTimeout(A,k)},_=async A=>{if(!l.disabled&&(b.isValid||S.isValid||A)){const k=l.resolver?It((await $()).errors):await ge(s,!0);k!==i.isValid&&T.state.next({isValid:k})}},N=(A,k)=>{!l.disabled&&(b.isValidating||b.validatingFields||S.isValidating||S.validatingFields)&&((A||Array.from(h.mount)).forEach(X=>{X&&(k?Ie(i.validatingFields,X,k):wt(i.validatingFields,X))}),T.state.next({validatingFields:i.validatingFields,isValidating:!It(i.validatingFields)}))},q=(A,k=[],X,oe,le=!0,ee=!0)=>{if(oe&&X&&!l.disabled){if(d.action=!0,ee&&Array.isArray(ie(s,A))){const be=X(ie(s,A),oe.argA,oe.argB);le&&Ie(s,A,be)}if(ee&&Array.isArray(ie(i.errors,A))){const be=X(ie(i.errors,A),oe.argA,oe.argB);le&&Ie(i.errors,A,be),WS(i.errors,A)}if((b.touchedFields||S.touchedFields)&&ee&&Array.isArray(ie(i.touchedFields,A))){const be=X(ie(i.touchedFields,A),oe.argA,oe.argB);le&&Ie(i.touchedFields,A,be)}(b.dirtyFields||S.dirtyFields)&&(i.dirtyFields=Oi(u,f)),T.state.next({name:A,isDirty:Ce(A,k),dirtyFields:i.dirtyFields,errors:i.errors,isValid:i.isValid})}else Ie(f,A,k)},j=(A,k)=>{Ie(i.errors,A,k),T.state.next({errors:i.errors})},H=A=>{i.errors=A,T.state.next({errors:i.errors,isValid:!1})},P=(A,k,X,oe)=>{const le=ie(s,A);if(le){const ee=ie(f,A,bt(X)?ie(u,A):X);bt(ee)||oe&&oe.defaultChecked||k?Ie(f,A,k?ee:Ap(le._f)):fe(A,ee),d.mount&&_()}},Z=(A,k,X,oe,le)=>{let ee=!1,be=!1;const Me={name:A};if(!l.disabled){if(!X||oe){(b.isDirty||S.isDirty)&&(be=i.isDirty,i.isDirty=Me.isDirty=Ce(),ee=be!==Me.isDirty);const Ye=Ya(ie(u,A),k);be=!!ie(i.dirtyFields,A),Ye?wt(i.dirtyFields,A):Ie(i.dirtyFields,A,!0),Me.dirtyFields=i.dirtyFields,ee=ee||(b.dirtyFields||S.dirtyFields)&&be!==!Ye}if(X){const Ye=ie(i.touchedFields,A);Ye||(Ie(i.touchedFields,A,X),Me.touchedFields=i.touchedFields,ee=ee||(b.touchedFields||S.touchedFields)&&Ye!==X)}ee&&le&&T.state.next(Me)}return ee?Me:{}},se=(A,k,X,oe)=>{const le=ie(i.errors,A),ee=(b.isValid||S.isValid)&&cn(k)&&i.isValid!==k;if(l.delayError&&X?(p=L(()=>j(A,X)),p(l.delayError)):(clearTimeout(v),p=null,X?Ie(i.errors,A,X):wt(i.errors,A)),(X?!Ya(le,X):le)||!It(oe)||ee){const be={...oe,...ee&&cn(k)?{isValid:k}:{},errors:i.errors,name:A};i={...i,...be},T.state.next(be)}},$=async A=>{N(A,!0);const k=await l.resolver(f,l.context,QS(A||h.mount,s,l.criteriaMode,l.shouldUseNativeValidation));return N(A),k},J=async A=>{const{errors:k}=await $(A);if(A)for(const X of A){const oe=ie(k,X);oe?Ie(i.errors,X,oe):wt(i.errors,X)}else i.errors=k;return k},ge=async(A,k,X={valid:!0})=>{for(const oe in A){const le=A[oe];if(le){const{_f:ee,...be}=le;if(ee){const Me=h.array.has(ee.name),Ye=le._f&&KS(le._f);Ye&&b.validatingFields&&N([oe],!0);const lt=await jp(le,h.disabled,f,z,l.shouldUseNativeValidation&&!k,Me);if(Ye&&b.validatingFields&&N([oe]),lt[ee.name]&&(X.valid=!1,k))break;!k&&(ie(lt,ee.name)?Me?IS(i.errors,lt,ee.name):Ie(i.errors,ee.name,lt[ee.name]):wt(i.errors,ee.name))}!It(be)&&await ge(be,k,X)}}return X.valid},Te=()=>{for(const A of h.unMount){const k=ie(s,A);k&&(k._f.refs?k._f.refs.every(X=>!cf(X)):!cf(k._f.ref))&&te(A)}h.unMount=new Set},Ce=(A,k)=>!l.disabled&&(A&&k&&Ie(f,A,k),!Ya(w(),u)),ne=(A,k,X)=>Gg(A,h,{...d.mount?f:bt(k)?u:Hn(A)?{[A]:k}:k},X,k),ce=A=>Qf(ie(d.mount?f:u,A,l.shouldUnregister?ie(u,A,[]):[])),fe=(A,k,X={})=>{const oe=ie(s,A);let le=k;if(oe){const ee=oe._f;ee&&(!ee.disabled&&Ie(f,A,Fg(k,ee)),le=xo(ee.ref)&&Qt(k)?"":k,Xg(ee.ref)?[...ee.ref.options].forEach(be=>be.selected=le.includes(be.value)):ee.refs?Hi(ee.ref)?ee.refs.forEach(be=>{(!be.defaultChecked||!be.disabled)&&(Array.isArray(le)?be.checked=!!le.find(Me=>Me===be.value):be.checked=le===be.value||!!le)}):ee.refs.forEach(be=>be.checked=be.value===le):Jf(ee.ref)?ee.ref.value="":(ee.ref.value=le,ee.ref.type||T.state.next({name:A,values:Nt(f)})))}(X.shouldDirty||X.shouldTouch)&&Z(A,le,X.shouldTouch,X.shouldDirty,!0),X.shouldValidate&&pe(A)},ve=(A,k,X)=>{for(const oe in k){if(!k.hasOwnProperty(oe))return;const le=k[oe],ee=A+"."+oe,be=ie(s,ee);(h.array.has(A)||xt(le)||be&&!be._f)&&!bl(le)?ve(ee,le,X):fe(ee,le,X)}},O=(A,k,X={})=>{const oe=ie(s,A),le=h.array.has(A),ee=Nt(k);Ie(f,A,ee),le?(T.array.next({name:A,values:Nt(f)}),(b.isDirty||b.dirtyFields||S.isDirty||S.dirtyFields)&&X.shouldDirty&&T.state.next({name:A,dirtyFields:Oi(u,f),isDirty:Ce(A,ee)})):oe&&!oe._f&&!Qt(ee)?ve(A,ee,X):fe(A,ee,X),Op(A,h)&&T.state.next({...i}),T.state.next({name:d.mount?A:void 0,values:Nt(f)})},G=async A=>{d.mount=!0;const k=A.target;let X=k.name,oe=!0;const le=ie(s,X),ee=Ye=>{oe=Number.isNaN(Ye)||bl(Ye)&&isNaN(Ye.getTime())||Ya(Ye,ie(f,X,Ye))},be=Cp(l.mode),Me=Cp(l.reValidateMode);if(le){let Ye,lt;const Al=k.type?Ap(le._f):Hg(A),Dn=A.type===bo.BLUR||A.type===bo.FOCUS_OUT,Yo=!PS(le._f)&&!l.resolver&&!ie(i.errors,X)&&!le._f.deps||JS(Dn,ie(i.touchedFields,X),i.isSubmitted,Me,be),va=Op(X,h,Dn);Ie(f,X,Al),Dn?(le._f.onBlur&&le._f.onBlur(A),p&&p(0)):le._f.onChange&&le._f.onChange(A);const pa=Z(X,Al,Dn),Qn=!It(pa)||va;if(!Dn&&T.state.next({name:X,type:A.type,values:Nt(f)}),Yo)return(b.isValid||S.isValid)&&(l.mode==="onBlur"?Dn&&_():Dn||_()),Qn&&T.state.next({name:X,...va?{}:pa});if(!Dn&&va&&T.state.next({...i}),l.resolver){const{errors:Ia}=await $([X]);if(ee(Al),oe){const el=Rp(i.errors,s,X),Yi=Rp(Ia,s,el.name||X);Ye=Yi.error,X=Yi.name,lt=It(Ia)}}else N([X],!0),Ye=(await jp(le,h.disabled,f,z,l.shouldUseNativeValidation))[X],N([X]),ee(Al),oe&&(Ye?lt=!1:(b.isValid||S.isValid)&&(lt=await ge(s,!0)));oe&&(le._f.deps&&pe(le._f.deps),se(X,lt,Ye,pa))}},V=(A,k)=>{if(ie(i.errors,k)&&A.focus)return A.focus(),1},pe=async(A,k={})=>{let X,oe;const le=Mi(A);if(l.resolver){const ee=await J(bt(A)?A:le);X=It(ee),oe=A?!le.some(be=>ie(ee,be)):X}else A?(oe=(await Promise.all(le.map(async ee=>{const be=ie(s,ee);return await ge(be&&be._f?{[ee]:be}:be)}))).every(Boolean),!(!oe&&!i.isValid)&&_()):oe=X=await ge(s);return T.state.next({...!Hn(A)||(b.isValid||S.isValid)&&X!==i.isValid?{}:{name:A},...l.resolver||!A?{isValid:X}:{},errors:i.errors}),k.shouldFocus&&!oe&&Di(s,V,A?le:h.mount),oe},w=A=>{const k={...d.mount?f:u};return bt(A)?k:Hn(A)?ie(k,A):A.map(X=>ie(k,X))},K=(A,k)=>({invalid:!!ie((k||i).errors,A),isDirty:!!ie((k||i).dirtyFields,A),error:ie((k||i).errors,A),isValidating:!!ie(i.validatingFields,A),isTouched:!!ie((k||i).touchedFields,A)}),ae=A=>{A&&Mi(A).forEach(k=>wt(i.errors,k)),T.state.next({errors:A?i.errors:{}})},W=(A,k,X)=>{const oe=(ie(s,A,{_f:{}})._f||{}).ref,le=ie(i.errors,A)||{},{ref:ee,message:be,type:Me,...Ye}=le;Ie(i.errors,A,{...Ye,...k,ref:oe}),T.state.next({name:A,errors:i.errors,isValid:!1}),X&&X.shouldFocus&&oe&&oe.focus&&oe.focus()},re=(A,k)=>Nn(A)?T.state.subscribe({next:X=>A(ne(void 0,k),X)}):ne(A,k,!0),Re=A=>T.state.subscribe({next:k=>{$S(A.name,k.name,A.exact)&&FS(k,A.formState||b,qt,A.reRenderRoot)&&A.callback({values:{...f},...i,...k})}}).unsubscribe,Se=A=>(d.mount=!0,S={...S,...A.formState},Re({...A,formState:S})),te=(A,k={})=>{for(const X of A?Mi(A):h.mount)h.mount.delete(X),h.array.delete(X),k.keepValue||(wt(s,X),wt(f,X)),!k.keepError&&wt(i.errors,X),!k.keepDirty&&wt(i.dirtyFields,X),!k.keepTouched&&wt(i.touchedFields,X),!k.keepIsValidating&&wt(i.validatingFields,X),!l.shouldUnregister&&!k.keepDefaultValue&&wt(u,X);T.state.next({values:Nt(f)}),T.state.next({...i,...k.keepDirty?{isDirty:Ce()}:{}}),!k.keepIsValid&&_()},we=({disabled:A,name:k})=>{(cn(A)&&d.mount||A||h.disabled.has(k))&&(A?h.disabled.add(k):h.disabled.delete(k))},Xe=(A,k={})=>{let X=ie(s,A);const oe=cn(k.disabled)||cn(l.disabled);return Ie(s,A,{...X||{},_f:{...X&&X._f?X._f:{ref:{name:A}},name:A,mount:!0,...k}}),h.mount.add(A),X?we({disabled:cn(k.disabled)?k.disabled:l.disabled,name:A}):P(A,!0,k.value),{...oe?{disabled:k.disabled||l.disabled}:{},...l.progressive?{required:!!k.required,min:Ri(k.min),max:Ri(k.max),minLength:Ri(k.minLength),maxLength:Ri(k.maxLength),pattern:Ri(k.pattern)}:{},name:A,onChange:G,onBlur:G,ref:le=>{if(le){Xe(A,k),X=ie(s,A);const ee=bt(le.value)&&le.querySelectorAll&&le.querySelectorAll("input,select,textarea")[0]||le,be=YS(ee),Me=X._f.refs||[];if(be?Me.find(Ye=>Ye===ee):ee===X._f.ref)return;Ie(s,A,{_f:{...X._f,...be?{refs:[...Me.filter(cf),ee,...Array.isArray(ie(u,A))?[{}]:[]],ref:{type:ee.type,name:A}}:{ref:ee}}}),P(A,!1,void 0,ee)}else X=ie(s,A,{}),X._f&&(X._f.mount=!1),(l.shouldUnregister||k.shouldUnregister)&&!(qg(h.array,A)&&d.action)&&h.unMount.add(A)}}},Ue=()=>l.shouldFocusError&&Di(s,V,h.mount),Ze=A=>{cn(A)&&(T.state.next({disabled:A}),Di(s,(k,X)=>{const oe=ie(s,X);oe&&(k.disabled=oe._f.disabled||A,Array.isArray(oe._f.refs)&&oe._f.refs.forEach(le=>{le.disabled=oe._f.disabled||A}))},0,!1))},Ke=(A,k)=>async X=>{let oe;X&&(X.preventDefault&&X.preventDefault(),X.persist&&X.persist());let le=Nt(f);if(T.state.next({isSubmitting:!0}),l.resolver){const{errors:ee,values:be}=await $();i.errors=ee,le=Nt(be)}else await ge(s);if(h.disabled.size)for(const ee of h.disabled)wt(le,ee);if(wt(i.errors,"root"),It(i.errors)){T.state.next({errors:{}});try{await A(le,X)}catch(ee){oe=ee}}else k&&await k({...i.errors},X),Ue(),setTimeout(Ue);if(T.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:It(i.errors)&&!oe,submitCount:i.submitCount+1,errors:i.errors}),oe)throw oe},Mt=(A,k={})=>{ie(s,A)&&(bt(k.defaultValue)?O(A,Nt(ie(u,A))):(O(A,k.defaultValue),Ie(u,A,Nt(k.defaultValue))),k.keepTouched||wt(i.touchedFields,A),k.keepDirty||(wt(i.dirtyFields,A),i.isDirty=k.defaultValue?Ce(A,Nt(ie(u,A))):Ce()),k.keepError||(wt(i.errors,A),b.isValid&&_()),T.state.next({...i}))},Kt=(A,k={})=>{const X=A?Nt(A):u,oe=Nt(X),le=It(A),ee=le?u:oe;if(k.keepDefaultValues||(u=X),!k.keepValues){if(k.keepDirtyValues){const be=new Set([...h.mount,...Object.keys(Oi(u,f))]);for(const Me of Array.from(be))ie(i.dirtyFields,Me)?Ie(ee,Me,ie(f,Me)):O(Me,ie(ee,Me))}else{if(Xf&&bt(A))for(const be of h.mount){const Me=ie(s,be);if(Me&&Me._f){const Ye=Array.isArray(Me._f.refs)?Me._f.refs[0]:Me._f.ref;if(xo(Ye)){const lt=Ye.closest("form");if(lt){lt.reset();break}}}}if(k.keepFieldsRef)for(const be of h.mount)O(be,ie(ee,be));else s={}}f=l.shouldUnregister?k.keepDefaultValues?Nt(u):{}:Nt(ee),T.array.next({values:{...ee}}),T.state.next({values:{...ee}})}h={mount:k.keepDirtyValues?h.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},d.mount=!b.isValid||!!k.keepIsValid||!!k.keepDirtyValues,d.watch=!!l.shouldUnregister,T.state.next({submitCount:k.keepSubmitCount?i.submitCount:0,isDirty:le?!1:k.keepDirty?i.isDirty:!!(k.keepDefaultValues&&!Ya(A,u)),isSubmitted:k.keepIsSubmitted?i.isSubmitted:!1,dirtyFields:le?{}:k.keepDirtyValues?k.keepDefaultValues&&f?Oi(u,f):i.dirtyFields:k.keepDefaultValues&&A?Oi(u,A):k.keepDirty?i.dirtyFields:{},touchedFields:k.keepTouched?i.touchedFields:{},errors:k.keepErrors?i.errors:{},isSubmitSuccessful:k.keepIsSubmitSuccessful?i.isSubmitSuccessful:!1,isSubmitting:!1})},ma=(A,k)=>Kt(Nn(A)?A(f):A,k),Wa=(A,k={})=>{const X=ie(s,A),oe=X&&X._f;if(oe){const le=oe.refs?oe.refs[0]:oe.ref;le.focus&&(le.focus(),k.shouldSelect&&Nn(le.select)&&le.select())}},qt=A=>{i={...i,...A}},Rr={control:{register:Xe,unregister:te,getFieldState:K,handleSubmit:Ke,setError:W,_subscribe:Re,_runSchema:$,_focusError:Ue,_getWatch:ne,_getDirty:Ce,_setValid:_,_setFieldArray:q,_setDisabledField:we,_setErrors:H,_getFieldArray:ce,_reset:Kt,_resetDefaultValues:()=>Nn(l.defaultValues)&&l.defaultValues().then(A=>{ma(A,l.resetOptions),T.state.next({isLoading:!1})}),_removeUnmounted:Te,_disableForm:Ze,_subjects:T,_proxyFormState:b,get _fields(){return s},get _formValues(){return f},get _state(){return d},set _state(A){d=A},get _defaultValues(){return u},get _names(){return h},set _names(A){h=A},get _formState(){return i},get _options(){return l},set _options(A){l={...l,...A}}},subscribe:Se,trigger:pe,register:Xe,handleSubmit:Ke,watch:re,setValue:O,getValues:w,reset:ma,resetField:Mt,clearErrors:ae,unregister:te,setError:W,setFocus:Wa,getFieldState:K};return{...Rr,formControl:Rr}}function Jg(a={}){const l=Be.useRef(void 0),i=Be.useRef(void 0),[s,u]=Be.useState({isDirty:!1,isValidating:!1,isLoading:Nn(a.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1,isReady:!1,defaultValues:Nn(a.defaultValues)?void 0:a.defaultValues});if(!l.current)if(a.formControl)l.current={...a.formControl,formState:s},a.defaultValues&&!Nn(a.defaultValues)&&a.formControl.reset(a.defaultValues,a.resetOptions);else{const{formControl:d,...h}=t_(a);l.current={...h,formState:s}}const f=l.current.control;return f._options=a,Ff(()=>{const d=f._subscribe({formState:f._proxyFormState,callback:()=>u({...f._formState}),reRenderRoot:!0});return u(h=>({...h,isReady:!0})),f._formState.isReady=!0,d},[f]),Be.useEffect(()=>f._disableForm(a.disabled),[f,a.disabled]),Be.useEffect(()=>{a.mode&&(f._options.mode=a.mode),a.reValidateMode&&(f._options.reValidateMode=a.reValidateMode)},[f,a.mode,a.reValidateMode]),Be.useEffect(()=>{a.errors&&(f._setErrors(a.errors),f._focusError())},[f,a.errors]),Be.useEffect(()=>{a.shouldUnregister&&f._subjects.state.next({values:f._getWatch()})},[f,a.shouldUnregister]),Be.useEffect(()=>{if(f._proxyFormState.isDirty){const d=f._getDirty();d!==s.isDirty&&f._subjects.state.next({isDirty:d})}},[f,s.isDirty]),Be.useEffect(()=>{a.values&&!Ya(a.values,i.current)?(f._reset(a.values,{keepFieldsRef:!0,...f._options.resetOptions}),i.current=a.values,u(d=>({...d}))):f._resetDefaultValues()},[f,a.values]),Be.useEffect(()=>{f._state.mount||(f._setValid(),f._state.mount=!0),f._state.watch&&(f._state.watch=!1,f._subjects.state.next({...f._formState})),f._removeUnmounted()}),l.current.formState=Zg(s,f),l.current}const Mp=(a,l,i)=>{if(a&&"reportValidity"in a){const s=ie(i,l);a.setCustomValidity(s&&s.message||""),a.reportValidity()}},Af=(a,l)=>{for(const i in l.fields){const s=l.fields[i];s&&s.ref&&"reportValidity"in s.ref?Mp(s.ref,i,a):s&&s.refs&&s.refs.forEach(u=>Mp(u,i,a))}},Dp=(a,l)=>{l.shouldUseNativeValidation&&Af(a,l);const i={};for(const s in a){const u=ie(l.fields,s),f=Object.assign(a[s]||{},{ref:u&&u.ref});if(n_(l.names||Object.keys(a),s)){const d=Object.assign({},ie(i,s));Ie(d,"root",f),Ie(i,s,d)}else Ie(i,s,f)}return i},n_=(a,l)=>{const i=zp(l);return a.some(s=>zp(s).match(`^${i}\\.\\d+`))};function zp(a){return a.replace(/\]|\[/g,"")}function Wg(a,l,i){function s(h,p){var v;Object.defineProperty(h,"_zod",{value:h._zod??{},enumerable:!1}),(v=h._zod).traits??(v.traits=new Set),h._zod.traits.add(a),l(h,p);for(const b in d.prototype)b in h||Object.defineProperty(h,b,{value:d.prototype[b].bind(h)});h._zod.constr=d,h._zod.def=p}const u=i?.Parent??Object;class f extends u{}Object.defineProperty(f,"name",{value:a});function d(h){var p;const v=i?.Parent?new f:this;s(v,h),(p=v._zod).deferred??(p.deferred=[]);for(const b of v._zod.deferred)b();return v}return Object.defineProperty(d,"init",{value:s}),Object.defineProperty(d,Symbol.hasInstance,{value:h=>i?.Parent&&h instanceof i.Parent?!0:h?._zod?.traits?.has(a)}),Object.defineProperty(d,"name",{value:a}),d}class a_ extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const l_={};function Ig(a){return l_}function r_(a,l){return typeof l=="bigint"?l.toString():l}const ey=Error.captureStackTrace?Error.captureStackTrace:(...a)=>{};function no(a){return typeof a=="string"?a:a?.message}function ty(a,l,i){const s={...a,path:a.path??[]};if(!a.message){const u=no(a.inst?._zod.def?.error?.(a))??no(l?.error?.(a))??no(i.customError?.(a))??no(i.localeError?.(a))??"Invalid input";s.message=u}return delete s.inst,delete s.continue,l?.reportInput||delete s.input,s}const ny=(a,l)=>{a.name="$ZodError",Object.defineProperty(a,"_zod",{value:a._zod,enumerable:!1}),Object.defineProperty(a,"issues",{value:l,enumerable:!1}),Object.defineProperty(a,"message",{get(){return JSON.stringify(l,r_,2)},enumerable:!0})},i_=Wg("$ZodError",ny),ay=Wg("$ZodError",ny,{Parent:Error}),s_=a=>(l,i,s,u)=>{const f=s?Object.assign(s,{async:!1}):{async:!1},d=l._zod.run({value:i,issues:[]},f);if(d instanceof Promise)throw new a_;if(d.issues.length){const h=new(u?.Err??a)(d.issues.map(p=>ty(p,f,Ig())));throw ey(h,u?.callee),h}return d.value},o_=s_(ay),u_=a=>async(l,i,s,u)=>{const f=s?Object.assign(s,{async:!0}):{async:!0};let d=l._zod.run({value:i,issues:[]},f);if(d instanceof Promise&&(d=await d),d.issues.length){const h=new(u?.Err??a)(d.issues.map(p=>ty(p,f,Ig())));throw ey(h,u?.callee),h}return d.value},c_=u_(ay);function kp(a,l){try{var i=a()}catch(s){return l(s)}return i&&i.then?i.then(void 0,l):i}function f_(a,l){for(var i={};a.length;){var s=a[0],u=s.code,f=s.message,d=s.path.join(".");if(!i[d])if("unionErrors"in s){var h=s.unionErrors[0].errors[0];i[d]={message:h.message,type:h.code}}else i[d]={message:f,type:u};if("unionErrors"in s&&s.unionErrors.forEach(function(b){return b.errors.forEach(function(S){return a.push(S)})}),l){var p=i[d].types,v=p&&p[s.code];i[d]=$f(d,l,i,u,v?[].concat(v,s.message):s.message)}a.shift()}return i}function d_(a,l){for(var i={};a.length;){var s=a[0],u=s.code,f=s.message,d=s.path.join(".");if(!i[d])if(s.code==="invalid_union"){var h=s.errors[0][0];i[d]={message:h.message,type:h.code}}else i[d]={message:f,type:u};if(s.code==="invalid_union"&&s.errors.forEach(function(b){return b.forEach(function(S){return a.push(S)})}),l){var p=i[d].types,v=p&&p[s.code];i[d]=$f(d,l,i,u,v?[].concat(v,s.message):s.message)}a.shift()}return i}function ly(a,l,i){if(i===void 0&&(i={}),function(s){return"_def"in s&&typeof s._def=="object"&&"typeName"in s._def}(a))return function(s,u,f){try{return Promise.resolve(kp(function(){return Promise.resolve(a[i.mode==="sync"?"parse":"parseAsync"](s,l)).then(function(d){return f.shouldUseNativeValidation&&Af({},f),{errors:{},values:i.raw?Object.assign({},s):d}})},function(d){if(function(h){return Array.isArray(h?.issues)}(d))return{values:{},errors:Dp(f_(d.errors,!f.shouldUseNativeValidation&&f.criteriaMode==="all"),f)};throw d}))}catch(d){return Promise.reject(d)}};if(function(s){return"_zod"in s&&typeof s._zod=="object"}(a))return function(s,u,f){try{return Promise.resolve(kp(function(){return Promise.resolve((i.mode==="sync"?o_:c_)(a,s,l)).then(function(d){return f.shouldUseNativeValidation&&Af({},f),{errors:{},values:i.raw?Object.assign({},s):d}})},function(d){if(function(h){return h instanceof i_}(d))return{values:{},errors:Dp(d_(d.issues,!f.shouldUseNativeValidation&&f.criteriaMode==="all"),f)};throw d}))}catch(d){return Promise.reject(d)}};throw new Error("Invalid input: not a Zod schema")}var Fe;(function(a){a.assertEqual=u=>{};function l(u){}a.assertIs=l;function i(u){throw new Error}a.assertNever=i,a.arrayToEnum=u=>{const f={};for(const d of u)f[d]=d;return f},a.getValidEnumValues=u=>{const f=a.objectKeys(u).filter(h=>typeof u[u[h]]!="number"),d={};for(const h of f)d[h]=u[h];return a.objectValues(d)},a.objectValues=u=>a.objectKeys(u).map(function(f){return u[f]}),a.objectKeys=typeof Object.keys=="function"?u=>Object.keys(u):u=>{const f=[];for(const d in u)Object.prototype.hasOwnProperty.call(u,d)&&f.push(d);return f},a.find=(u,f)=>{for(const d of u)if(f(d))return d},a.isInteger=typeof Number.isInteger=="function"?u=>Number.isInteger(u):u=>typeof u=="number"&&Number.isFinite(u)&&Math.floor(u)===u;function s(u,f=" | "){return u.map(d=>typeof d=="string"?`'${d}'`:d).join(f)}a.joinValues=s,a.jsonStringifyReplacer=(u,f)=>typeof f=="bigint"?f.toString():f})(Fe||(Fe={}));var Up;(function(a){a.mergeShapes=(l,i)=>({...l,...i})})(Up||(Up={}));const ye=Fe.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Za=a=>{switch(typeof a){case"undefined":return ye.undefined;case"string":return ye.string;case"number":return Number.isNaN(a)?ye.nan:ye.number;case"boolean":return ye.boolean;case"function":return ye.function;case"bigint":return ye.bigint;case"symbol":return ye.symbol;case"object":return Array.isArray(a)?ye.array:a===null?ye.null:a.then&&typeof a.then=="function"&&a.catch&&typeof a.catch=="function"?ye.promise:typeof Map<"u"&&a instanceof Map?ye.map:typeof Set<"u"&&a instanceof Set?ye.set:typeof Date<"u"&&a instanceof Date?ye.date:ye.object;default:return ye.unknown}},I=Fe.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class fa extends Error{get errors(){return this.issues}constructor(l){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};const i=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,i):this.__proto__=i,this.name="ZodError",this.issues=l}format(l){const i=l||function(f){return f.message},s={_errors:[]},u=f=>{for(const d of f.issues)if(d.code==="invalid_union")d.unionErrors.map(u);else if(d.code==="invalid_return_type")u(d.returnTypeError);else if(d.code==="invalid_arguments")u(d.argumentsError);else if(d.path.length===0)s._errors.push(i(d));else{let h=s,p=0;for(;p<d.path.length;){const v=d.path[p];p===d.path.length-1?(h[v]=h[v]||{_errors:[]},h[v]._errors.push(i(d))):h[v]=h[v]||{_errors:[]},h=h[v],p++}}};return u(this),s}static assert(l){if(!(l instanceof fa))throw new Error(`Not a ZodError: ${l}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,Fe.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(l=i=>i.message){const i={},s=[];for(const u of this.issues)if(u.path.length>0){const f=u.path[0];i[f]=i[f]||[],i[f].push(l(u))}else s.push(l(u));return{formErrors:s,fieldErrors:i}}get formErrors(){return this.flatten()}}fa.create=a=>new fa(a);const Cf=(a,l)=>{let i;switch(a.code){case I.invalid_type:a.received===ye.undefined?i="Required":i=`Expected ${a.expected}, received ${a.received}`;break;case I.invalid_literal:i=`Invalid literal value, expected ${JSON.stringify(a.expected,Fe.jsonStringifyReplacer)}`;break;case I.unrecognized_keys:i=`Unrecognized key(s) in object: ${Fe.joinValues(a.keys,", ")}`;break;case I.invalid_union:i="Invalid input";break;case I.invalid_union_discriminator:i=`Invalid discriminator value. Expected ${Fe.joinValues(a.options)}`;break;case I.invalid_enum_value:i=`Invalid enum value. Expected ${Fe.joinValues(a.options)}, received '${a.received}'`;break;case I.invalid_arguments:i="Invalid function arguments";break;case I.invalid_return_type:i="Invalid function return type";break;case I.invalid_date:i="Invalid date";break;case I.invalid_string:typeof a.validation=="object"?"includes"in a.validation?(i=`Invalid input: must include "${a.validation.includes}"`,typeof a.validation.position=="number"&&(i=`${i} at one or more positions greater than or equal to ${a.validation.position}`)):"startsWith"in a.validation?i=`Invalid input: must start with "${a.validation.startsWith}"`:"endsWith"in a.validation?i=`Invalid input: must end with "${a.validation.endsWith}"`:Fe.assertNever(a.validation):a.validation!=="regex"?i=`Invalid ${a.validation}`:i="Invalid";break;case I.too_small:a.type==="array"?i=`Array must contain ${a.exact?"exactly":a.inclusive?"at least":"more than"} ${a.minimum} element(s)`:a.type==="string"?i=`String must contain ${a.exact?"exactly":a.inclusive?"at least":"over"} ${a.minimum} character(s)`:a.type==="number"?i=`Number must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${a.minimum}`:a.type==="bigint"?i=`Number must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${a.minimum}`:a.type==="date"?i=`Date must be ${a.exact?"exactly equal to ":a.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(a.minimum))}`:i="Invalid input";break;case I.too_big:a.type==="array"?i=`Array must contain ${a.exact?"exactly":a.inclusive?"at most":"less than"} ${a.maximum} element(s)`:a.type==="string"?i=`String must contain ${a.exact?"exactly":a.inclusive?"at most":"under"} ${a.maximum} character(s)`:a.type==="number"?i=`Number must be ${a.exact?"exactly":a.inclusive?"less than or equal to":"less than"} ${a.maximum}`:a.type==="bigint"?i=`BigInt must be ${a.exact?"exactly":a.inclusive?"less than or equal to":"less than"} ${a.maximum}`:a.type==="date"?i=`Date must be ${a.exact?"exactly":a.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(a.maximum))}`:i="Invalid input";break;case I.custom:i="Invalid input";break;case I.invalid_intersection_types:i="Intersection results could not be merged";break;case I.not_multiple_of:i=`Number must be a multiple of ${a.multipleOf}`;break;case I.not_finite:i="Number must be finite";break;default:i=l.defaultError,Fe.assertNever(a)}return{message:i}};let h_=Cf;function m_(){return h_}const v_=a=>{const{data:l,path:i,errorMaps:s,issueData:u}=a,f=[...i,...u.path||[]],d={...u,path:f};if(u.message!==void 0)return{...u,path:f,message:u.message};let h="";const p=s.filter(v=>!!v).slice().reverse();for(const v of p)h=v(d,{data:l,defaultError:h}).message;return{...u,path:f,message:h}};function ue(a,l){const i=m_(),s=v_({issueData:l,data:a.data,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,i,i===Cf?void 0:Cf].filter(u=>!!u)});a.common.issues.push(s)}class mn{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(l,i){const s=[];for(const u of i){if(u.status==="aborted")return Ne;u.status==="dirty"&&l.dirty(),s.push(u.value)}return{status:l.value,value:s}}static async mergeObjectAsync(l,i){const s=[];for(const u of i){const f=await u.key,d=await u.value;s.push({key:f,value:d})}return mn.mergeObjectSync(l,s)}static mergeObjectSync(l,i){const s={};for(const u of i){const{key:f,value:d}=u;if(f.status==="aborted"||d.status==="aborted")return Ne;f.status==="dirty"&&l.dirty(),d.status==="dirty"&&l.dirty(),f.value!=="__proto__"&&(typeof d.value<"u"||u.alwaysSet)&&(s[f.value]=d.value)}return{status:l.value,value:s}}}const Ne=Object.freeze({status:"aborted"}),Ni=a=>({status:"dirty",value:a}),En=a=>({status:"valid",value:a}),Vp=a=>a.status==="aborted",Bp=a=>a.status==="dirty",br=a=>a.status==="valid",wo=a=>typeof Promise<"u"&&a instanceof Promise;var xe;(function(a){a.errToObj=l=>typeof l=="string"?{message:l}:l||{},a.toString=l=>typeof l=="string"?l:l?.message})(xe||(xe={}));class Qa{constructor(l,i,s,u){this._cachedPath=[],this.parent=l,this.data=i,this._path=s,this._key=u}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Lp=(a,l)=>{if(br(l))return{success:!0,data:l.value};if(!a.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const i=new fa(a.common.issues);return this._error=i,this._error}}};function Le(a){if(!a)return{};const{errorMap:l,invalid_type_error:i,required_error:s,description:u}=a;if(l&&(i||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return l?{errorMap:l,description:u}:{errorMap:(d,h)=>{const{message:p}=a;return d.code==="invalid_enum_value"?{message:p??h.defaultError}:typeof h.data>"u"?{message:p??s??h.defaultError}:d.code!=="invalid_type"?{message:h.defaultError}:{message:p??i??h.defaultError}},description:u}}class Qe{get description(){return this._def.description}_getType(l){return Za(l.data)}_getOrReturnCtx(l,i){return i||{common:l.parent.common,data:l.data,parsedType:Za(l.data),schemaErrorMap:this._def.errorMap,path:l.path,parent:l.parent}}_processInputParams(l){return{status:new mn,ctx:{common:l.parent.common,data:l.data,parsedType:Za(l.data),schemaErrorMap:this._def.errorMap,path:l.path,parent:l.parent}}}_parseSync(l){const i=this._parse(l);if(wo(i))throw new Error("Synchronous parse encountered promise.");return i}_parseAsync(l){const i=this._parse(l);return Promise.resolve(i)}parse(l,i){const s=this.safeParse(l,i);if(s.success)return s.data;throw s.error}safeParse(l,i){const s={common:{issues:[],async:i?.async??!1,contextualErrorMap:i?.errorMap},path:i?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:l,parsedType:Za(l)},u=this._parseSync({data:l,path:s.path,parent:s});return Lp(s,u)}"~validate"(l){const i={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:l,parsedType:Za(l)};if(!this["~standard"].async)try{const s=this._parseSync({data:l,path:[],parent:i});return br(s)?{value:s.value}:{issues:i.common.issues}}catch(s){s?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),i.common={issues:[],async:!0}}return this._parseAsync({data:l,path:[],parent:i}).then(s=>br(s)?{value:s.value}:{issues:i.common.issues})}async parseAsync(l,i){const s=await this.safeParseAsync(l,i);if(s.success)return s.data;throw s.error}async safeParseAsync(l,i){const s={common:{issues:[],contextualErrorMap:i?.errorMap,async:!0},path:i?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:l,parsedType:Za(l)},u=this._parse({data:l,path:s.path,parent:s}),f=await(wo(u)?u:Promise.resolve(u));return Lp(s,f)}refine(l,i){const s=u=>typeof i=="string"||typeof i>"u"?{message:i}:typeof i=="function"?i(u):i;return this._refinement((u,f)=>{const d=l(u),h=()=>f.addIssue({code:I.custom,...s(u)});return typeof Promise<"u"&&d instanceof Promise?d.then(p=>p?!0:(h(),!1)):d?!0:(h(),!1)})}refinement(l,i){return this._refinement((s,u)=>l(s)?!0:(u.addIssue(typeof i=="function"?i(s,u):i),!1))}_refinement(l){return new Sr({schema:this,typeName:je.ZodEffects,effect:{type:"refinement",refinement:l}})}superRefine(l){return this._refinement(l)}constructor(l){this.spa=this.safeParseAsync,this._def=l,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:i=>this["~validate"](i)}}optional(){return Xa.create(this,this._def)}nullable(){return _r.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Zn.create(this)}promise(){return To.create(this,this._def)}or(l){return Ao.create([this,l],this._def)}and(l){return Co.create(this,l,this._def)}transform(l){return new Sr({...Le(this._def),schema:this,typeName:je.ZodEffects,effect:{type:"transform",transform:l}})}default(l){const i=typeof l=="function"?l:()=>l;return new Nf({...Le(this._def),innerType:this,defaultValue:i,typeName:je.ZodDefault})}brand(){return new B_({typeName:je.ZodBranded,type:this,...Le(this._def)})}catch(l){const i=typeof l=="function"?l:()=>l;return new jf({...Le(this._def),innerType:this,catchValue:i,typeName:je.ZodCatch})}describe(l){const i=this.constructor;return new i({...this._def,description:l})}pipe(l){return If.create(this,l)}readonly(){return Mf.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const p_=/^c[^\s-]{8,}$/i,g_=/^[0-9a-z]+$/,y_=/^[0-9A-HJKMNP-TV-Z]{26}$/i,b_=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,x_=/^[a-z0-9_-]{21}$/i,S_=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,__=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,w_=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,E_="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let ff;const A_=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,C_=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,T_=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,O_=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,R_=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,N_=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ry="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",j_=new RegExp(`^${ry}$`);function iy(a){let l="[0-5]\\d";a.precision?l=`${l}\\.\\d{${a.precision}}`:a.precision==null&&(l=`${l}(\\.\\d+)?`);const i=a.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${l})${i}`}function M_(a){return new RegExp(`^${iy(a)}$`)}function D_(a){let l=`${ry}T${iy(a)}`;const i=[];return i.push(a.local?"Z?":"Z"),a.offset&&i.push("([+-]\\d{2}:?\\d{2})"),l=`${l}(${i.join("|")})`,new RegExp(`^${l}$`)}function z_(a,l){return!!((l==="v4"||!l)&&A_.test(a)||(l==="v6"||!l)&&T_.test(a))}function k_(a,l){if(!S_.test(a))return!1;try{const[i]=a.split(".");if(!i)return!1;const s=i.replace(/-/g,"+").replace(/_/g,"/").padEnd(i.length+(4-i.length%4)%4,"="),u=JSON.parse(atob(s));return!(typeof u!="object"||u===null||"typ"in u&&u?.typ!=="JWT"||!u.alg||l&&u.alg!==l)}catch{return!1}}function U_(a,l){return!!((l==="v4"||!l)&&C_.test(a)||(l==="v6"||!l)&&O_.test(a))}class Ga extends Qe{_parse(l){if(this._def.coerce&&(l.data=String(l.data)),this._getType(l)!==ye.string){const f=this._getOrReturnCtx(l);return ue(f,{code:I.invalid_type,expected:ye.string,received:f.parsedType}),Ne}const s=new mn;let u;for(const f of this._def.checks)if(f.kind==="min")l.data.length<f.value&&(u=this._getOrReturnCtx(l,u),ue(u,{code:I.too_small,minimum:f.value,type:"string",inclusive:!0,exact:!1,message:f.message}),s.dirty());else if(f.kind==="max")l.data.length>f.value&&(u=this._getOrReturnCtx(l,u),ue(u,{code:I.too_big,maximum:f.value,type:"string",inclusive:!0,exact:!1,message:f.message}),s.dirty());else if(f.kind==="length"){const d=l.data.length>f.value,h=l.data.length<f.value;(d||h)&&(u=this._getOrReturnCtx(l,u),d?ue(u,{code:I.too_big,maximum:f.value,type:"string",inclusive:!0,exact:!0,message:f.message}):h&&ue(u,{code:I.too_small,minimum:f.value,type:"string",inclusive:!0,exact:!0,message:f.message}),s.dirty())}else if(f.kind==="email")w_.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"email",code:I.invalid_string,message:f.message}),s.dirty());else if(f.kind==="emoji")ff||(ff=new RegExp(E_,"u")),ff.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"emoji",code:I.invalid_string,message:f.message}),s.dirty());else if(f.kind==="uuid")b_.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"uuid",code:I.invalid_string,message:f.message}),s.dirty());else if(f.kind==="nanoid")x_.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"nanoid",code:I.invalid_string,message:f.message}),s.dirty());else if(f.kind==="cuid")p_.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"cuid",code:I.invalid_string,message:f.message}),s.dirty());else if(f.kind==="cuid2")g_.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"cuid2",code:I.invalid_string,message:f.message}),s.dirty());else if(f.kind==="ulid")y_.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"ulid",code:I.invalid_string,message:f.message}),s.dirty());else if(f.kind==="url")try{new URL(l.data)}catch{u=this._getOrReturnCtx(l,u),ue(u,{validation:"url",code:I.invalid_string,message:f.message}),s.dirty()}else f.kind==="regex"?(f.regex.lastIndex=0,f.regex.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"regex",code:I.invalid_string,message:f.message}),s.dirty())):f.kind==="trim"?l.data=l.data.trim():f.kind==="includes"?l.data.includes(f.value,f.position)||(u=this._getOrReturnCtx(l,u),ue(u,{code:I.invalid_string,validation:{includes:f.value,position:f.position},message:f.message}),s.dirty()):f.kind==="toLowerCase"?l.data=l.data.toLowerCase():f.kind==="toUpperCase"?l.data=l.data.toUpperCase():f.kind==="startsWith"?l.data.startsWith(f.value)||(u=this._getOrReturnCtx(l,u),ue(u,{code:I.invalid_string,validation:{startsWith:f.value},message:f.message}),s.dirty()):f.kind==="endsWith"?l.data.endsWith(f.value)||(u=this._getOrReturnCtx(l,u),ue(u,{code:I.invalid_string,validation:{endsWith:f.value},message:f.message}),s.dirty()):f.kind==="datetime"?D_(f).test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{code:I.invalid_string,validation:"datetime",message:f.message}),s.dirty()):f.kind==="date"?j_.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{code:I.invalid_string,validation:"date",message:f.message}),s.dirty()):f.kind==="time"?M_(f).test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{code:I.invalid_string,validation:"time",message:f.message}),s.dirty()):f.kind==="duration"?__.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"duration",code:I.invalid_string,message:f.message}),s.dirty()):f.kind==="ip"?z_(l.data,f.version)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"ip",code:I.invalid_string,message:f.message}),s.dirty()):f.kind==="jwt"?k_(l.data,f.alg)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"jwt",code:I.invalid_string,message:f.message}),s.dirty()):f.kind==="cidr"?U_(l.data,f.version)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"cidr",code:I.invalid_string,message:f.message}),s.dirty()):f.kind==="base64"?R_.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"base64",code:I.invalid_string,message:f.message}),s.dirty()):f.kind==="base64url"?N_.test(l.data)||(u=this._getOrReturnCtx(l,u),ue(u,{validation:"base64url",code:I.invalid_string,message:f.message}),s.dirty()):Fe.assertNever(f);return{status:s.value,value:l.data}}_regex(l,i,s){return this.refinement(u=>l.test(u),{validation:i,code:I.invalid_string,...xe.errToObj(s)})}_addCheck(l){return new Ga({...this._def,checks:[...this._def.checks,l]})}email(l){return this._addCheck({kind:"email",...xe.errToObj(l)})}url(l){return this._addCheck({kind:"url",...xe.errToObj(l)})}emoji(l){return this._addCheck({kind:"emoji",...xe.errToObj(l)})}uuid(l){return this._addCheck({kind:"uuid",...xe.errToObj(l)})}nanoid(l){return this._addCheck({kind:"nanoid",...xe.errToObj(l)})}cuid(l){return this._addCheck({kind:"cuid",...xe.errToObj(l)})}cuid2(l){return this._addCheck({kind:"cuid2",...xe.errToObj(l)})}ulid(l){return this._addCheck({kind:"ulid",...xe.errToObj(l)})}base64(l){return this._addCheck({kind:"base64",...xe.errToObj(l)})}base64url(l){return this._addCheck({kind:"base64url",...xe.errToObj(l)})}jwt(l){return this._addCheck({kind:"jwt",...xe.errToObj(l)})}ip(l){return this._addCheck({kind:"ip",...xe.errToObj(l)})}cidr(l){return this._addCheck({kind:"cidr",...xe.errToObj(l)})}datetime(l){return typeof l=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:l}):this._addCheck({kind:"datetime",precision:typeof l?.precision>"u"?null:l?.precision,offset:l?.offset??!1,local:l?.local??!1,...xe.errToObj(l?.message)})}date(l){return this._addCheck({kind:"date",message:l})}time(l){return typeof l=="string"?this._addCheck({kind:"time",precision:null,message:l}):this._addCheck({kind:"time",precision:typeof l?.precision>"u"?null:l?.precision,...xe.errToObj(l?.message)})}duration(l){return this._addCheck({kind:"duration",...xe.errToObj(l)})}regex(l,i){return this._addCheck({kind:"regex",regex:l,...xe.errToObj(i)})}includes(l,i){return this._addCheck({kind:"includes",value:l,position:i?.position,...xe.errToObj(i?.message)})}startsWith(l,i){return this._addCheck({kind:"startsWith",value:l,...xe.errToObj(i)})}endsWith(l,i){return this._addCheck({kind:"endsWith",value:l,...xe.errToObj(i)})}min(l,i){return this._addCheck({kind:"min",value:l,...xe.errToObj(i)})}max(l,i){return this._addCheck({kind:"max",value:l,...xe.errToObj(i)})}length(l,i){return this._addCheck({kind:"length",value:l,...xe.errToObj(i)})}nonempty(l){return this.min(1,xe.errToObj(l))}trim(){return new Ga({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Ga({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Ga({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(l=>l.kind==="datetime")}get isDate(){return!!this._def.checks.find(l=>l.kind==="date")}get isTime(){return!!this._def.checks.find(l=>l.kind==="time")}get isDuration(){return!!this._def.checks.find(l=>l.kind==="duration")}get isEmail(){return!!this._def.checks.find(l=>l.kind==="email")}get isURL(){return!!this._def.checks.find(l=>l.kind==="url")}get isEmoji(){return!!this._def.checks.find(l=>l.kind==="emoji")}get isUUID(){return!!this._def.checks.find(l=>l.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(l=>l.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(l=>l.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(l=>l.kind==="cuid2")}get isULID(){return!!this._def.checks.find(l=>l.kind==="ulid")}get isIP(){return!!this._def.checks.find(l=>l.kind==="ip")}get isCIDR(){return!!this._def.checks.find(l=>l.kind==="cidr")}get isBase64(){return!!this._def.checks.find(l=>l.kind==="base64")}get isBase64url(){return!!this._def.checks.find(l=>l.kind==="base64url")}get minLength(){let l=null;for(const i of this._def.checks)i.kind==="min"&&(l===null||i.value>l)&&(l=i.value);return l}get maxLength(){let l=null;for(const i of this._def.checks)i.kind==="max"&&(l===null||i.value<l)&&(l=i.value);return l}}Ga.create=a=>new Ga({checks:[],typeName:je.ZodString,coerce:a?.coerce??!1,...Le(a)});function V_(a,l){const i=(a.toString().split(".")[1]||"").length,s=(l.toString().split(".")[1]||"").length,u=i>s?i:s,f=Number.parseInt(a.toFixed(u).replace(".","")),d=Number.parseInt(l.toFixed(u).replace(".",""));return f%d/10**u}class ki extends Qe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(l){if(this._def.coerce&&(l.data=Number(l.data)),this._getType(l)!==ye.number){const f=this._getOrReturnCtx(l);return ue(f,{code:I.invalid_type,expected:ye.number,received:f.parsedType}),Ne}let s;const u=new mn;for(const f of this._def.checks)f.kind==="int"?Fe.isInteger(l.data)||(s=this._getOrReturnCtx(l,s),ue(s,{code:I.invalid_type,expected:"integer",received:"float",message:f.message}),u.dirty()):f.kind==="min"?(f.inclusive?l.data<f.value:l.data<=f.value)&&(s=this._getOrReturnCtx(l,s),ue(s,{code:I.too_small,minimum:f.value,type:"number",inclusive:f.inclusive,exact:!1,message:f.message}),u.dirty()):f.kind==="max"?(f.inclusive?l.data>f.value:l.data>=f.value)&&(s=this._getOrReturnCtx(l,s),ue(s,{code:I.too_big,maximum:f.value,type:"number",inclusive:f.inclusive,exact:!1,message:f.message}),u.dirty()):f.kind==="multipleOf"?V_(l.data,f.value)!==0&&(s=this._getOrReturnCtx(l,s),ue(s,{code:I.not_multiple_of,multipleOf:f.value,message:f.message}),u.dirty()):f.kind==="finite"?Number.isFinite(l.data)||(s=this._getOrReturnCtx(l,s),ue(s,{code:I.not_finite,message:f.message}),u.dirty()):Fe.assertNever(f);return{status:u.value,value:l.data}}gte(l,i){return this.setLimit("min",l,!0,xe.toString(i))}gt(l,i){return this.setLimit("min",l,!1,xe.toString(i))}lte(l,i){return this.setLimit("max",l,!0,xe.toString(i))}lt(l,i){return this.setLimit("max",l,!1,xe.toString(i))}setLimit(l,i,s,u){return new ki({...this._def,checks:[...this._def.checks,{kind:l,value:i,inclusive:s,message:xe.toString(u)}]})}_addCheck(l){return new ki({...this._def,checks:[...this._def.checks,l]})}int(l){return this._addCheck({kind:"int",message:xe.toString(l)})}positive(l){return this._addCheck({kind:"min",value:0,inclusive:!1,message:xe.toString(l)})}negative(l){return this._addCheck({kind:"max",value:0,inclusive:!1,message:xe.toString(l)})}nonpositive(l){return this._addCheck({kind:"max",value:0,inclusive:!0,message:xe.toString(l)})}nonnegative(l){return this._addCheck({kind:"min",value:0,inclusive:!0,message:xe.toString(l)})}multipleOf(l,i){return this._addCheck({kind:"multipleOf",value:l,message:xe.toString(i)})}finite(l){return this._addCheck({kind:"finite",message:xe.toString(l)})}safe(l){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:xe.toString(l)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:xe.toString(l)})}get minValue(){let l=null;for(const i of this._def.checks)i.kind==="min"&&(l===null||i.value>l)&&(l=i.value);return l}get maxValue(){let l=null;for(const i of this._def.checks)i.kind==="max"&&(l===null||i.value<l)&&(l=i.value);return l}get isInt(){return!!this._def.checks.find(l=>l.kind==="int"||l.kind==="multipleOf"&&Fe.isInteger(l.value))}get isFinite(){let l=null,i=null;for(const s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(i===null||s.value>i)&&(i=s.value):s.kind==="max"&&(l===null||s.value<l)&&(l=s.value)}return Number.isFinite(i)&&Number.isFinite(l)}}ki.create=a=>new ki({checks:[],typeName:je.ZodNumber,coerce:a?.coerce||!1,...Le(a)});class Ui extends Qe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(l){if(this._def.coerce)try{l.data=BigInt(l.data)}catch{return this._getInvalidInput(l)}if(this._getType(l)!==ye.bigint)return this._getInvalidInput(l);let s;const u=new mn;for(const f of this._def.checks)f.kind==="min"?(f.inclusive?l.data<f.value:l.data<=f.value)&&(s=this._getOrReturnCtx(l,s),ue(s,{code:I.too_small,type:"bigint",minimum:f.value,inclusive:f.inclusive,message:f.message}),u.dirty()):f.kind==="max"?(f.inclusive?l.data>f.value:l.data>=f.value)&&(s=this._getOrReturnCtx(l,s),ue(s,{code:I.too_big,type:"bigint",maximum:f.value,inclusive:f.inclusive,message:f.message}),u.dirty()):f.kind==="multipleOf"?l.data%f.value!==BigInt(0)&&(s=this._getOrReturnCtx(l,s),ue(s,{code:I.not_multiple_of,multipleOf:f.value,message:f.message}),u.dirty()):Fe.assertNever(f);return{status:u.value,value:l.data}}_getInvalidInput(l){const i=this._getOrReturnCtx(l);return ue(i,{code:I.invalid_type,expected:ye.bigint,received:i.parsedType}),Ne}gte(l,i){return this.setLimit("min",l,!0,xe.toString(i))}gt(l,i){return this.setLimit("min",l,!1,xe.toString(i))}lte(l,i){return this.setLimit("max",l,!0,xe.toString(i))}lt(l,i){return this.setLimit("max",l,!1,xe.toString(i))}setLimit(l,i,s,u){return new Ui({...this._def,checks:[...this._def.checks,{kind:l,value:i,inclusive:s,message:xe.toString(u)}]})}_addCheck(l){return new Ui({...this._def,checks:[...this._def.checks,l]})}positive(l){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:xe.toString(l)})}negative(l){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:xe.toString(l)})}nonpositive(l){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:xe.toString(l)})}nonnegative(l){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:xe.toString(l)})}multipleOf(l,i){return this._addCheck({kind:"multipleOf",value:l,message:xe.toString(i)})}get minValue(){let l=null;for(const i of this._def.checks)i.kind==="min"&&(l===null||i.value>l)&&(l=i.value);return l}get maxValue(){let l=null;for(const i of this._def.checks)i.kind==="max"&&(l===null||i.value<l)&&(l=i.value);return l}}Ui.create=a=>new Ui({checks:[],typeName:je.ZodBigInt,coerce:a?.coerce??!1,...Le(a)});class Hp extends Qe{_parse(l){if(this._def.coerce&&(l.data=!!l.data),this._getType(l)!==ye.boolean){const s=this._getOrReturnCtx(l);return ue(s,{code:I.invalid_type,expected:ye.boolean,received:s.parsedType}),Ne}return En(l.data)}}Hp.create=a=>new Hp({typeName:je.ZodBoolean,coerce:a?.coerce||!1,...Le(a)});class Eo extends Qe{_parse(l){if(this._def.coerce&&(l.data=new Date(l.data)),this._getType(l)!==ye.date){const f=this._getOrReturnCtx(l);return ue(f,{code:I.invalid_type,expected:ye.date,received:f.parsedType}),Ne}if(Number.isNaN(l.data.getTime())){const f=this._getOrReturnCtx(l);return ue(f,{code:I.invalid_date}),Ne}const s=new mn;let u;for(const f of this._def.checks)f.kind==="min"?l.data.getTime()<f.value&&(u=this._getOrReturnCtx(l,u),ue(u,{code:I.too_small,message:f.message,inclusive:!0,exact:!1,minimum:f.value,type:"date"}),s.dirty()):f.kind==="max"?l.data.getTime()>f.value&&(u=this._getOrReturnCtx(l,u),ue(u,{code:I.too_big,message:f.message,inclusive:!0,exact:!1,maximum:f.value,type:"date"}),s.dirty()):Fe.assertNever(f);return{status:s.value,value:new Date(l.data.getTime())}}_addCheck(l){return new Eo({...this._def,checks:[...this._def.checks,l]})}min(l,i){return this._addCheck({kind:"min",value:l.getTime(),message:xe.toString(i)})}max(l,i){return this._addCheck({kind:"max",value:l.getTime(),message:xe.toString(i)})}get minDate(){let l=null;for(const i of this._def.checks)i.kind==="min"&&(l===null||i.value>l)&&(l=i.value);return l!=null?new Date(l):null}get maxDate(){let l=null;for(const i of this._def.checks)i.kind==="max"&&(l===null||i.value<l)&&(l=i.value);return l!=null?new Date(l):null}}Eo.create=a=>new Eo({checks:[],coerce:a?.coerce||!1,typeName:je.ZodDate,...Le(a)});class qp extends Qe{_parse(l){if(this._getType(l)!==ye.symbol){const s=this._getOrReturnCtx(l);return ue(s,{code:I.invalid_type,expected:ye.symbol,received:s.parsedType}),Ne}return En(l.data)}}qp.create=a=>new qp({typeName:je.ZodSymbol,...Le(a)});class Zp extends Qe{_parse(l){if(this._getType(l)!==ye.undefined){const s=this._getOrReturnCtx(l);return ue(s,{code:I.invalid_type,expected:ye.undefined,received:s.parsedType}),Ne}return En(l.data)}}Zp.create=a=>new Zp({typeName:je.ZodUndefined,...Le(a)});class Yp extends Qe{_parse(l){if(this._getType(l)!==ye.null){const s=this._getOrReturnCtx(l);return ue(s,{code:I.invalid_type,expected:ye.null,received:s.parsedType}),Ne}return En(l.data)}}Yp.create=a=>new Yp({typeName:je.ZodNull,...Le(a)});class Gp extends Qe{constructor(){super(...arguments),this._any=!0}_parse(l){return En(l.data)}}Gp.create=a=>new Gp({typeName:je.ZodAny,...Le(a)});class Xp extends Qe{constructor(){super(...arguments),this._unknown=!0}_parse(l){return En(l.data)}}Xp.create=a=>new Xp({typeName:je.ZodUnknown,...Le(a)});class Ka extends Qe{_parse(l){const i=this._getOrReturnCtx(l);return ue(i,{code:I.invalid_type,expected:ye.never,received:i.parsedType}),Ne}}Ka.create=a=>new Ka({typeName:je.ZodNever,...Le(a)});class Qp extends Qe{_parse(l){if(this._getType(l)!==ye.undefined){const s=this._getOrReturnCtx(l);return ue(s,{code:I.invalid_type,expected:ye.void,received:s.parsedType}),Ne}return En(l.data)}}Qp.create=a=>new Qp({typeName:je.ZodVoid,...Le(a)});class Zn extends Qe{_parse(l){const{ctx:i,status:s}=this._processInputParams(l),u=this._def;if(i.parsedType!==ye.array)return ue(i,{code:I.invalid_type,expected:ye.array,received:i.parsedType}),Ne;if(u.exactLength!==null){const d=i.data.length>u.exactLength.value,h=i.data.length<u.exactLength.value;(d||h)&&(ue(i,{code:d?I.too_big:I.too_small,minimum:h?u.exactLength.value:void 0,maximum:d?u.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:u.exactLength.message}),s.dirty())}if(u.minLength!==null&&i.data.length<u.minLength.value&&(ue(i,{code:I.too_small,minimum:u.minLength.value,type:"array",inclusive:!0,exact:!1,message:u.minLength.message}),s.dirty()),u.maxLength!==null&&i.data.length>u.maxLength.value&&(ue(i,{code:I.too_big,maximum:u.maxLength.value,type:"array",inclusive:!0,exact:!1,message:u.maxLength.message}),s.dirty()),i.common.async)return Promise.all([...i.data].map((d,h)=>u.type._parseAsync(new Qa(i,d,i.path,h)))).then(d=>mn.mergeArray(s,d));const f=[...i.data].map((d,h)=>u.type._parseSync(new Qa(i,d,i.path,h)));return mn.mergeArray(s,f)}get element(){return this._def.type}min(l,i){return new Zn({...this._def,minLength:{value:l,message:xe.toString(i)}})}max(l,i){return new Zn({...this._def,maxLength:{value:l,message:xe.toString(i)}})}length(l,i){return new Zn({...this._def,exactLength:{value:l,message:xe.toString(i)}})}nonempty(l){return this.min(1,l)}}Zn.create=(a,l)=>new Zn({type:a,minLength:null,maxLength:null,exactLength:null,typeName:je.ZodArray,...Le(l)});function pr(a){if(a instanceof Et){const l={};for(const i in a.shape){const s=a.shape[i];l[i]=Xa.create(pr(s))}return new Et({...a._def,shape:()=>l})}else return a instanceof Zn?new Zn({...a._def,type:pr(a.element)}):a instanceof Xa?Xa.create(pr(a.unwrap())):a instanceof _r?_r.create(pr(a.unwrap())):a instanceof xl?xl.create(a.items.map(l=>pr(l))):a}class Et extends Qe{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const l=this._def.shape(),i=Fe.objectKeys(l);return this._cached={shape:l,keys:i},this._cached}_parse(l){if(this._getType(l)!==ye.object){const v=this._getOrReturnCtx(l);return ue(v,{code:I.invalid_type,expected:ye.object,received:v.parsedType}),Ne}const{status:s,ctx:u}=this._processInputParams(l),{shape:f,keys:d}=this._getCached(),h=[];if(!(this._def.catchall instanceof Ka&&this._def.unknownKeys==="strip"))for(const v in u.data)d.includes(v)||h.push(v);const p=[];for(const v of d){const b=f[v],S=u.data[v];p.push({key:{status:"valid",value:v},value:b._parse(new Qa(u,S,u.path,v)),alwaysSet:v in u.data})}if(this._def.catchall instanceof Ka){const v=this._def.unknownKeys;if(v==="passthrough")for(const b of h)p.push({key:{status:"valid",value:b},value:{status:"valid",value:u.data[b]}});else if(v==="strict")h.length>0&&(ue(u,{code:I.unrecognized_keys,keys:h}),s.dirty());else if(v!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const v=this._def.catchall;for(const b of h){const S=u.data[b];p.push({key:{status:"valid",value:b},value:v._parse(new Qa(u,S,u.path,b)),alwaysSet:b in u.data})}}return u.common.async?Promise.resolve().then(async()=>{const v=[];for(const b of p){const S=await b.key,T=await b.value;v.push({key:S,value:T,alwaysSet:b.alwaysSet})}return v}).then(v=>mn.mergeObjectSync(s,v)):mn.mergeObjectSync(s,p)}get shape(){return this._def.shape()}strict(l){return xe.errToObj,new Et({...this._def,unknownKeys:"strict",...l!==void 0?{errorMap:(i,s)=>{const u=this._def.errorMap?.(i,s).message??s.defaultError;return i.code==="unrecognized_keys"?{message:xe.errToObj(l).message??u}:{message:u}}}:{}})}strip(){return new Et({...this._def,unknownKeys:"strip"})}passthrough(){return new Et({...this._def,unknownKeys:"passthrough"})}extend(l){return new Et({...this._def,shape:()=>({...this._def.shape(),...l})})}merge(l){return new Et({unknownKeys:l._def.unknownKeys,catchall:l._def.catchall,shape:()=>({...this._def.shape(),...l._def.shape()}),typeName:je.ZodObject})}setKey(l,i){return this.augment({[l]:i})}catchall(l){return new Et({...this._def,catchall:l})}pick(l){const i={};for(const s of Fe.objectKeys(l))l[s]&&this.shape[s]&&(i[s]=this.shape[s]);return new Et({...this._def,shape:()=>i})}omit(l){const i={};for(const s of Fe.objectKeys(this.shape))l[s]||(i[s]=this.shape[s]);return new Et({...this._def,shape:()=>i})}deepPartial(){return pr(this)}partial(l){const i={};for(const s of Fe.objectKeys(this.shape)){const u=this.shape[s];l&&!l[s]?i[s]=u:i[s]=u.optional()}return new Et({...this._def,shape:()=>i})}required(l){const i={};for(const s of Fe.objectKeys(this.shape))if(l&&!l[s])i[s]=this.shape[s];else{let f=this.shape[s];for(;f instanceof Xa;)f=f._def.innerType;i[s]=f}return new Et({...this._def,shape:()=>i})}keyof(){return sy(Fe.objectKeys(this.shape))}}Et.create=(a,l)=>new Et({shape:()=>a,unknownKeys:"strip",catchall:Ka.create(),typeName:je.ZodObject,...Le(l)});Et.strictCreate=(a,l)=>new Et({shape:()=>a,unknownKeys:"strict",catchall:Ka.create(),typeName:je.ZodObject,...Le(l)});Et.lazycreate=(a,l)=>new Et({shape:a,unknownKeys:"strip",catchall:Ka.create(),typeName:je.ZodObject,...Le(l)});class Ao extends Qe{_parse(l){const{ctx:i}=this._processInputParams(l),s=this._def.options;function u(f){for(const h of f)if(h.result.status==="valid")return h.result;for(const h of f)if(h.result.status==="dirty")return i.common.issues.push(...h.ctx.common.issues),h.result;const d=f.map(h=>new fa(h.ctx.common.issues));return ue(i,{code:I.invalid_union,unionErrors:d}),Ne}if(i.common.async)return Promise.all(s.map(async f=>{const d={...i,common:{...i.common,issues:[]},parent:null};return{result:await f._parseAsync({data:i.data,path:i.path,parent:d}),ctx:d}})).then(u);{let f;const d=[];for(const p of s){const v={...i,common:{...i.common,issues:[]},parent:null},b=p._parseSync({data:i.data,path:i.path,parent:v});if(b.status==="valid")return b;b.status==="dirty"&&!f&&(f={result:b,ctx:v}),v.common.issues.length&&d.push(v.common.issues)}if(f)return i.common.issues.push(...f.ctx.common.issues),f.result;const h=d.map(p=>new fa(p));return ue(i,{code:I.invalid_union,unionErrors:h}),Ne}}get options(){return this._def.options}}Ao.create=(a,l)=>new Ao({options:a,typeName:je.ZodUnion,...Le(l)});function Tf(a,l){const i=Za(a),s=Za(l);if(a===l)return{valid:!0,data:a};if(i===ye.object&&s===ye.object){const u=Fe.objectKeys(l),f=Fe.objectKeys(a).filter(h=>u.indexOf(h)!==-1),d={...a,...l};for(const h of f){const p=Tf(a[h],l[h]);if(!p.valid)return{valid:!1};d[h]=p.data}return{valid:!0,data:d}}else if(i===ye.array&&s===ye.array){if(a.length!==l.length)return{valid:!1};const u=[];for(let f=0;f<a.length;f++){const d=a[f],h=l[f],p=Tf(d,h);if(!p.valid)return{valid:!1};u.push(p.data)}return{valid:!0,data:u}}else return i===ye.date&&s===ye.date&&+a==+l?{valid:!0,data:a}:{valid:!1}}class Co extends Qe{_parse(l){const{status:i,ctx:s}=this._processInputParams(l),u=(f,d)=>{if(Vp(f)||Vp(d))return Ne;const h=Tf(f.value,d.value);return h.valid?((Bp(f)||Bp(d))&&i.dirty(),{status:i.value,value:h.data}):(ue(s,{code:I.invalid_intersection_types}),Ne)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([f,d])=>u(f,d)):u(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}Co.create=(a,l,i)=>new Co({left:a,right:l,typeName:je.ZodIntersection,...Le(i)});class xl extends Qe{_parse(l){const{status:i,ctx:s}=this._processInputParams(l);if(s.parsedType!==ye.array)return ue(s,{code:I.invalid_type,expected:ye.array,received:s.parsedType}),Ne;if(s.data.length<this._def.items.length)return ue(s,{code:I.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),Ne;!this._def.rest&&s.data.length>this._def.items.length&&(ue(s,{code:I.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),i.dirty());const f=[...s.data].map((d,h)=>{const p=this._def.items[h]||this._def.rest;return p?p._parse(new Qa(s,d,s.path,h)):null}).filter(d=>!!d);return s.common.async?Promise.all(f).then(d=>mn.mergeArray(i,d)):mn.mergeArray(i,f)}get items(){return this._def.items}rest(l){return new xl({...this._def,rest:l})}}xl.create=(a,l)=>{if(!Array.isArray(a))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new xl({items:a,typeName:je.ZodTuple,rest:null,...Le(l)})};class Kp extends Qe{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(l){const{status:i,ctx:s}=this._processInputParams(l);if(s.parsedType!==ye.map)return ue(s,{code:I.invalid_type,expected:ye.map,received:s.parsedType}),Ne;const u=this._def.keyType,f=this._def.valueType,d=[...s.data.entries()].map(([h,p],v)=>({key:u._parse(new Qa(s,h,s.path,[v,"key"])),value:f._parse(new Qa(s,p,s.path,[v,"value"]))}));if(s.common.async){const h=new Map;return Promise.resolve().then(async()=>{for(const p of d){const v=await p.key,b=await p.value;if(v.status==="aborted"||b.status==="aborted")return Ne;(v.status==="dirty"||b.status==="dirty")&&i.dirty(),h.set(v.value,b.value)}return{status:i.value,value:h}})}else{const h=new Map;for(const p of d){const v=p.key,b=p.value;if(v.status==="aborted"||b.status==="aborted")return Ne;(v.status==="dirty"||b.status==="dirty")&&i.dirty(),h.set(v.value,b.value)}return{status:i.value,value:h}}}}Kp.create=(a,l,i)=>new Kp({valueType:l,keyType:a,typeName:je.ZodMap,...Le(i)});class Vi extends Qe{_parse(l){const{status:i,ctx:s}=this._processInputParams(l);if(s.parsedType!==ye.set)return ue(s,{code:I.invalid_type,expected:ye.set,received:s.parsedType}),Ne;const u=this._def;u.minSize!==null&&s.data.size<u.minSize.value&&(ue(s,{code:I.too_small,minimum:u.minSize.value,type:"set",inclusive:!0,exact:!1,message:u.minSize.message}),i.dirty()),u.maxSize!==null&&s.data.size>u.maxSize.value&&(ue(s,{code:I.too_big,maximum:u.maxSize.value,type:"set",inclusive:!0,exact:!1,message:u.maxSize.message}),i.dirty());const f=this._def.valueType;function d(p){const v=new Set;for(const b of p){if(b.status==="aborted")return Ne;b.status==="dirty"&&i.dirty(),v.add(b.value)}return{status:i.value,value:v}}const h=[...s.data.values()].map((p,v)=>f._parse(new Qa(s,p,s.path,v)));return s.common.async?Promise.all(h).then(p=>d(p)):d(h)}min(l,i){return new Vi({...this._def,minSize:{value:l,message:xe.toString(i)}})}max(l,i){return new Vi({...this._def,maxSize:{value:l,message:xe.toString(i)}})}size(l,i){return this.min(l,i).max(l,i)}nonempty(l){return this.min(1,l)}}Vi.create=(a,l)=>new Vi({valueType:a,minSize:null,maxSize:null,typeName:je.ZodSet,...Le(l)});class Pp extends Qe{get schema(){return this._def.getter()}_parse(l){const{ctx:i}=this._processInputParams(l);return this._def.getter()._parse({data:i.data,path:i.path,parent:i})}}Pp.create=(a,l)=>new Pp({getter:a,typeName:je.ZodLazy,...Le(l)});class Of extends Qe{_parse(l){if(l.data!==this._def.value){const i=this._getOrReturnCtx(l);return ue(i,{received:i.data,code:I.invalid_literal,expected:this._def.value}),Ne}return{status:"valid",value:l.data}}get value(){return this._def.value}}Of.create=(a,l)=>new Of({value:a,typeName:je.ZodLiteral,...Le(l)});function sy(a,l){return new xr({values:a,typeName:je.ZodEnum,...Le(l)})}class xr extends Qe{_parse(l){if(typeof l.data!="string"){const i=this._getOrReturnCtx(l),s=this._def.values;return ue(i,{expected:Fe.joinValues(s),received:i.parsedType,code:I.invalid_type}),Ne}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(l.data)){const i=this._getOrReturnCtx(l),s=this._def.values;return ue(i,{received:i.data,code:I.invalid_enum_value,options:s}),Ne}return En(l.data)}get options(){return this._def.values}get enum(){const l={};for(const i of this._def.values)l[i]=i;return l}get Values(){const l={};for(const i of this._def.values)l[i]=i;return l}get Enum(){const l={};for(const i of this._def.values)l[i]=i;return l}extract(l,i=this._def){return xr.create(l,{...this._def,...i})}exclude(l,i=this._def){return xr.create(this.options.filter(s=>!l.includes(s)),{...this._def,...i})}}xr.create=sy;class Rf extends Qe{_parse(l){const i=Fe.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(l);if(s.parsedType!==ye.string&&s.parsedType!==ye.number){const u=Fe.objectValues(i);return ue(s,{expected:Fe.joinValues(u),received:s.parsedType,code:I.invalid_type}),Ne}if(this._cache||(this._cache=new Set(Fe.getValidEnumValues(this._def.values))),!this._cache.has(l.data)){const u=Fe.objectValues(i);return ue(s,{received:s.data,code:I.invalid_enum_value,options:u}),Ne}return En(l.data)}get enum(){return this._def.values}}Rf.create=(a,l)=>new Rf({values:a,typeName:je.ZodNativeEnum,...Le(l)});class To extends Qe{unwrap(){return this._def.type}_parse(l){const{ctx:i}=this._processInputParams(l);if(i.parsedType!==ye.promise&&i.common.async===!1)return ue(i,{code:I.invalid_type,expected:ye.promise,received:i.parsedType}),Ne;const s=i.parsedType===ye.promise?i.data:Promise.resolve(i.data);return En(s.then(u=>this._def.type.parseAsync(u,{path:i.path,errorMap:i.common.contextualErrorMap})))}}To.create=(a,l)=>new To({type:a,typeName:je.ZodPromise,...Le(l)});class Sr extends Qe{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===je.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(l){const{status:i,ctx:s}=this._processInputParams(l),u=this._def.effect||null,f={addIssue:d=>{ue(s,d),d.fatal?i.abort():i.dirty()},get path(){return s.path}};if(f.addIssue=f.addIssue.bind(f),u.type==="preprocess"){const d=u.transform(s.data,f);if(s.common.async)return Promise.resolve(d).then(async h=>{if(i.value==="aborted")return Ne;const p=await this._def.schema._parseAsync({data:h,path:s.path,parent:s});return p.status==="aborted"?Ne:p.status==="dirty"||i.value==="dirty"?Ni(p.value):p});{if(i.value==="aborted")return Ne;const h=this._def.schema._parseSync({data:d,path:s.path,parent:s});return h.status==="aborted"?Ne:h.status==="dirty"||i.value==="dirty"?Ni(h.value):h}}if(u.type==="refinement"){const d=h=>{const p=u.refinement(h,f);if(s.common.async)return Promise.resolve(p);if(p instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return h};if(s.common.async===!1){const h=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return h.status==="aborted"?Ne:(h.status==="dirty"&&i.dirty(),d(h.value),{status:i.value,value:h.value})}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(h=>h.status==="aborted"?Ne:(h.status==="dirty"&&i.dirty(),d(h.value).then(()=>({status:i.value,value:h.value}))))}if(u.type==="transform")if(s.common.async===!1){const d=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!br(d))return Ne;const h=u.transform(d.value,f);if(h instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:i.value,value:h}}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(d=>br(d)?Promise.resolve(u.transform(d.value,f)).then(h=>({status:i.value,value:h})):Ne);Fe.assertNever(u)}}Sr.create=(a,l,i)=>new Sr({schema:a,typeName:je.ZodEffects,effect:l,...Le(i)});Sr.createWithPreprocess=(a,l,i)=>new Sr({schema:l,effect:{type:"preprocess",transform:a},typeName:je.ZodEffects,...Le(i)});class Xa extends Qe{_parse(l){return this._getType(l)===ye.undefined?En(void 0):this._def.innerType._parse(l)}unwrap(){return this._def.innerType}}Xa.create=(a,l)=>new Xa({innerType:a,typeName:je.ZodOptional,...Le(l)});class _r extends Qe{_parse(l){return this._getType(l)===ye.null?En(null):this._def.innerType._parse(l)}unwrap(){return this._def.innerType}}_r.create=(a,l)=>new _r({innerType:a,typeName:je.ZodNullable,...Le(l)});class Nf extends Qe{_parse(l){const{ctx:i}=this._processInputParams(l);let s=i.data;return i.parsedType===ye.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:i.path,parent:i})}removeDefault(){return this._def.innerType}}Nf.create=(a,l)=>new Nf({innerType:a,typeName:je.ZodDefault,defaultValue:typeof l.default=="function"?l.default:()=>l.default,...Le(l)});class jf extends Qe{_parse(l){const{ctx:i}=this._processInputParams(l),s={...i,common:{...i.common,issues:[]}},u=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return wo(u)?u.then(f=>({status:"valid",value:f.status==="valid"?f.value:this._def.catchValue({get error(){return new fa(s.common.issues)},input:s.data})})):{status:"valid",value:u.status==="valid"?u.value:this._def.catchValue({get error(){return new fa(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}jf.create=(a,l)=>new jf({innerType:a,typeName:je.ZodCatch,catchValue:typeof l.catch=="function"?l.catch:()=>l.catch,...Le(l)});class Fp extends Qe{_parse(l){if(this._getType(l)!==ye.nan){const s=this._getOrReturnCtx(l);return ue(s,{code:I.invalid_type,expected:ye.nan,received:s.parsedType}),Ne}return{status:"valid",value:l.data}}}Fp.create=a=>new Fp({typeName:je.ZodNaN,...Le(a)});class B_ extends Qe{_parse(l){const{ctx:i}=this._processInputParams(l),s=i.data;return this._def.type._parse({data:s,path:i.path,parent:i})}unwrap(){return this._def.type}}class If extends Qe{_parse(l){const{status:i,ctx:s}=this._processInputParams(l);if(s.common.async)return(async()=>{const f=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return f.status==="aborted"?Ne:f.status==="dirty"?(i.dirty(),Ni(f.value)):this._def.out._parseAsync({data:f.value,path:s.path,parent:s})})();{const u=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return u.status==="aborted"?Ne:u.status==="dirty"?(i.dirty(),{status:"dirty",value:u.value}):this._def.out._parseSync({data:u.value,path:s.path,parent:s})}}static create(l,i){return new If({in:l,out:i,typeName:je.ZodPipeline})}}class Mf extends Qe{_parse(l){const i=this._def.innerType._parse(l),s=u=>(br(u)&&(u.value=Object.freeze(u.value)),u);return wo(i)?i.then(u=>s(u)):s(i)}unwrap(){return this._def.innerType}}Mf.create=(a,l)=>new Mf({innerType:a,typeName:je.ZodReadonly,...Le(l)});var je;(function(a){a.ZodString="ZodString",a.ZodNumber="ZodNumber",a.ZodNaN="ZodNaN",a.ZodBigInt="ZodBigInt",a.ZodBoolean="ZodBoolean",a.ZodDate="ZodDate",a.ZodSymbol="ZodSymbol",a.ZodUndefined="ZodUndefined",a.ZodNull="ZodNull",a.ZodAny="ZodAny",a.ZodUnknown="ZodUnknown",a.ZodNever="ZodNever",a.ZodVoid="ZodVoid",a.ZodArray="ZodArray",a.ZodObject="ZodObject",a.ZodUnion="ZodUnion",a.ZodDiscriminatedUnion="ZodDiscriminatedUnion",a.ZodIntersection="ZodIntersection",a.ZodTuple="ZodTuple",a.ZodRecord="ZodRecord",a.ZodMap="ZodMap",a.ZodSet="ZodSet",a.ZodFunction="ZodFunction",a.ZodLazy="ZodLazy",a.ZodLiteral="ZodLiteral",a.ZodEnum="ZodEnum",a.ZodEffects="ZodEffects",a.ZodNativeEnum="ZodNativeEnum",a.ZodOptional="ZodOptional",a.ZodNullable="ZodNullable",a.ZodDefault="ZodDefault",a.ZodCatch="ZodCatch",a.ZodPromise="ZodPromise",a.ZodBranded="ZodBranded",a.ZodPipeline="ZodPipeline",a.ZodReadonly="ZodReadonly"})(je||(je={}));const vt=Ga.create;Ka.create;Zn.create;const oy=Et.create;Ao.create;Co.create;xl.create;const fn=Of.create;xr.create;const L_=Rf.create;To.create;Xa.create;_r.create;function At({className:a,type:l,...i}){return g.jsx("input",{type:l,"data-slot":"input",className:jt("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...i})}function uy({className:a,...l}){return g.jsx("textarea",{"data-slot":"textarea",className:jt("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...l})}var qi=Eg();const H_=_g(qi);var q_=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],pt=q_.reduce((a,l)=>{const i=zi(`Primitive.${l}`),s=E.forwardRef((u,f)=>{const{asChild:d,...h}=u,p=d?i:l;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),g.jsx(p,{...h,ref:f})});return s.displayName=`Primitive.${l}`,{...a,[l]:s}},{});function Z_(a,l){a&&qi.flushSync(()=>a.dispatchEvent(l))}var Y_="Label",cy=E.forwardRef((a,l)=>g.jsx(pt.label,{...a,ref:l,onMouseDown:i=>{i.target.closest("button, input, select, textarea")||(a.onMouseDown?.(i),!i.defaultPrevented&&i.detail>1&&i.preventDefault())}}));cy.displayName=Y_;var G_=cy;function X_({className:a,...l}){return g.jsx(G_,{"data-slot":"label",className:jt("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...l})}const fy=LS,dy=E.createContext({}),ot=({...a})=>g.jsx(dy.Provider,{value:{name:a.name},children:g.jsx(ZS,{...a})}),ko=()=>{const a=E.useContext(dy),l=E.useContext(hy),{getFieldState:i}=zo(),s=Yg({name:a.name}),u=i(a.name,s);if(!a)throw new Error("useFormField should be used within <FormField>");const{id:f}=l;return{id:f,name:a.name,formItemId:`${f}-form-item`,formDescriptionId:`${f}-form-item-description`,formMessageId:`${f}-form-item-message`,...u}},hy=E.createContext({});function ut({className:a,...l}){const i=E.useId();return g.jsx(hy.Provider,{value:{id:i},children:g.jsx("div",{"data-slot":"form-item",className:jt("grid gap-2",a),...l})})}function ct({className:a,...l}){const{error:i,formItemId:s}=ko();return g.jsx(X_,{"data-slot":"form-label","data-error":!!i,className:jt("data-[error=true]:text-destructive",a),htmlFor:s,...l})}function ft({...a}){const{error:l,formItemId:i,formDescriptionId:s,formMessageId:u}=ko();return g.jsx(Cg,{"data-slot":"form-control",id:i,"aria-describedby":l?`${s} ${u}`:`${s}`,"aria-invalid":!!l,...a})}function uo({className:a,...l}){const{formDescriptionId:i}=ko();return g.jsx("p",{"data-slot":"form-description",id:i,className:jt("text-muted-foreground text-sm",a),...l})}function dt({className:a,...l}){const{error:i,formMessageId:s}=ko(),u=i?String(i?.message??""):l.children;return u?g.jsx("p",{"data-slot":"form-message",id:s,className:jt("text-destructive text-sm",a),...l,children:u}):null}const Q_="http://localhost:5000/api";class K_{async request(l,i={}){const s=`${Q_}${l}`,u={headers:{"Content-Type":"application/json",...i.headers},...i};try{const f=await fetch(s,u);if(!f.ok){const d=await f.json().catch(()=>({}));throw{message:d.message||`HTTP error! status: ${f.status}`,errors:d.errors||[]}}return await f.json()}catch(f){throw f instanceof Error?{message:f.message,errors:[]}:f}}async getCompanies(){return this.request("/companies")}async getCompany(l){return this.request(`/companies/${l}`)}async createCompany(l){return this.request("/companies",{method:"POST",body:JSON.stringify(l)})}async updateCompany(l,i){return this.request(`/companies/${l}`,{method:"PUT",body:JSON.stringify(i)})}async deleteCompany(l){return this.request(`/companies/${l}`,{method:"DELETE"})}async checkBulstatExists(l,i){const s=i?`?excludeId=${i}`:"";return this.request(`/companies/bulstat/${l}/exists${s}`)}async getCustomers(){return this.request("/customers")}async getCustomer(l){return this.request(`/customers/${l}`)}async createCustomer(l){return this.request("/customers",{method:"POST",body:JSON.stringify(l)})}async updateCustomer(l,i){return this.request(`/customers/${l}`,{method:"PUT",body:JSON.stringify(i)})}async deleteCustomer(l){return this.request(`/customers/${l}`,{method:"DELETE"})}async checkCustomerBulstatExists(l,i){const s=i?`?excludeId=${i}`:"";return this.request(`/customers/bulstat/${l}/exists${s}`)}async checkPersonalIdExists(l,i){const s=i?`?excludeId=${i}`:"";return this.request(`/customers/personal-id/${l}/exists${s}`)}}const my=new K_,P_=oy({name:vt().min(1,"Company name is required").max(200,"Company name cannot exceed 200 characters"),bulstatNumber:vt().min(9,"ЕИК/Булстат must be at least 9 digits").max(13,"ЕИК/Булстат cannot exceed 13 digits").regex(/^\d{9,13}$/,"ЕИК/Булстат must contain only digits"),vatNumber:vt().max(20,"VAT number cannot exceed 20 characters").optional().or(fn("")),address:vt().min(1,"Address is required").max(500,"Address cannot exceed 500 characters"),city:vt().max(100,"City cannot exceed 100 characters").optional().or(fn("")),postalCode:vt().max(10,"Postal code cannot exceed 10 characters").optional().or(fn("")),country:vt().max(100,"Country cannot exceed 100 characters").default("Bulgaria"),phone:vt().max(20,"Phone number cannot exceed 20 characters").optional().or(fn("")),email:vt().email("Invalid email format").max(100,"Email cannot exceed 100 characters").optional().or(fn("")),website:vt().url("Invalid website URL format").max(200,"Website URL cannot exceed 200 characters").optional().or(fn("")),contactPerson:vt().max(100,"Contact person name cannot exceed 100 characters").optional().or(fn(""))});function F_({onSuccess:a,onCancel:l}){const[i,s]=E.useState(!1),[u,f]=E.useState(null),d=Jg({resolver:ly(P_),defaultValues:{name:"",bulstatNumber:"",vatNumber:"",address:"",city:"",postalCode:"",country:"Bulgaria",phone:"",email:"",website:"",contactPerson:""}}),h=async p=>{s(!0),f(null);try{const v={name:p.name,bulstatNumber:p.bulstatNumber,address:p.address,country:p.country,vatNumber:p.vatNumber||void 0,city:p.city||void 0,postalCode:p.postalCode||void 0,phone:p.phone||void 0,email:p.email||void 0,website:p.website||void 0,contactPerson:p.contactPerson||void 0},b=await my.createCompany(v);a?.(b),d.reset()}catch(v){const b=v;f(b.message),b.errors&&b.errors.forEach(S=>{d.setError(S.field,{type:"server",message:S.message})})}finally{s(!1)}};return g.jsxs(mo,{className:"w-full max-w-2xl mx-auto",children:[g.jsxs(vo,{children:[g.jsx(po,{children:"Create New Company"}),g.jsx(go,{children:"Add a new company (доставчик на услугите/продуктите) to your invoicing system."})]}),g.jsx(yo,{children:g.jsx(fy,{...d,children:g.jsxs("form",{onSubmit:d.handleSubmit(h),className:"space-y-6",children:[u&&g.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:u}),g.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[g.jsx(ot,{control:d.control,name:"name",render:({field:p})=>g.jsxs(ut,{className:"md:col-span-2",children:[g.jsx(ct,{children:"Company Name *"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"Enter company name",...p})}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"bulstatNumber",render:({field:p})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"ЕИК/Булстат Number *"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"*********",...p})}),g.jsx(uo,{children:"9-13 digit Bulgarian business identification number"}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"vatNumber",render:({field:p})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"VAT Number"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"BG*********",...p})}),g.jsx(dt,{})]})})]}),g.jsx(ot,{control:d.control,name:"address",render:({field:p})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Address *"}),g.jsx(ft,{children:g.jsx(uy,{placeholder:"Enter company address",className:"min-h-[80px]",...p})}),g.jsx(dt,{})]})}),g.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[g.jsx(ot,{control:d.control,name:"city",render:({field:p})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"City"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"Sofia",...p})}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"postalCode",render:({field:p})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Postal Code"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"1000",...p})}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"country",render:({field:p})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Country"}),g.jsx(ft,{children:g.jsx(At,{...p})}),g.jsx(dt,{})]})})]}),g.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[g.jsx(ot,{control:d.control,name:"phone",render:({field:p})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Phone"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"+359 2 123 4567",...p})}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"email",render:({field:p})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Email"}),g.jsx(ft,{children:g.jsx(At,{type:"email",placeholder:"<EMAIL>",...p})}),g.jsx(dt,{})]})})]}),g.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[g.jsx(ot,{control:d.control,name:"website",render:({field:p})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Website"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"https://www.company.bg",...p})}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"contactPerson",render:({field:p})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Contact Person"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"John Doe",...p})}),g.jsx(dt,{})]})})]}),g.jsxs("div",{className:"flex gap-4 pt-4",children:[g.jsx(ca,{type:"submit",disabled:i,className:"flex-1",children:i?"Creating...":"Create Company"}),l&&g.jsx(ca,{type:"button",variant:"outline",onClick:l,children:"Cancel"})]})]})})})]})}function $_(){const[a,l]=E.useState(null),i=u=>{l(u)},s=()=>{l(null)};return a?g.jsx("div",{className:"container mx-auto py-8 px-4",children:g.jsxs("div",{className:"max-w-2xl mx-auto",children:[g.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-lg mb-6",children:[g.jsx("h2",{className:"text-lg font-semibold mb-2",children:"✅ Company Created Successfully!"}),g.jsxs("p",{className:"mb-4",children:[g.jsx("strong",{children:a.name})," has been added to your system."]}),g.jsxs("div",{className:"space-y-1 text-sm",children:[g.jsxs("p",{children:[g.jsx("strong",{children:"ЕИК/Булстат:"})," ",a.bulstatNumber]}),a.vatNumber&&g.jsxs("p",{children:[g.jsx("strong",{children:"VAT Number:"})," ",a.vatNumber]}),g.jsxs("p",{children:[g.jsx("strong",{children:"Address:"})," ",a.address]}),a.city&&g.jsxs("p",{children:[g.jsx("strong",{children:"City:"})," ",a.city,", ",a.country]}),a.phone&&g.jsxs("p",{children:[g.jsx("strong",{children:"Phone:"})," ",a.phone]}),a.email&&g.jsxs("p",{children:[g.jsx("strong",{children:"Email:"})," ",a.email]})]})]}),g.jsxs("div",{className:"flex gap-4 justify-center",children:[g.jsx("button",{onClick:s,className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors",children:"Create Another Company"}),g.jsx("button",{onClick:()=>{console.log("Navigate to companies list")},className:"bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md transition-colors",children:"View All Companies"})]})]})}):g.jsxs("div",{className:"container mx-auto py-8 px-4",children:[g.jsxs("div",{className:"mb-8 text-center",children:[g.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Create New Company"}),g.jsx("p",{className:"text-gray-600",children:"Add a new company (доставчик на услугите/продуктите) to your invoicing system"})]}),g.jsx(F_,{onSuccess:i,onCancel:()=>{console.log("Cancel company creation")}})]})}function $p(a,[l,i]){return Math.min(i,Math.max(l,a))}function Ct(a,l,{checkForDefaultPrevented:i=!0}={}){return function(u){if(a?.(u),i===!1||!u.defaultPrevented)return l?.(u)}}function ed(a,l=[]){let i=[];function s(f,d){const h=E.createContext(d),p=i.length;i=[...i,d];const v=S=>{const{scope:T,children:z,...L}=S,_=T?.[a]?.[p]||h,N=E.useMemo(()=>L,Object.values(L));return g.jsx(_.Provider,{value:N,children:z})};v.displayName=f+"Provider";function b(S,T){const z=T?.[a]?.[p]||h,L=E.useContext(z);if(L)return L;if(d!==void 0)return d;throw new Error(`\`${S}\` must be used within \`${f}\``)}return[v,b]}const u=()=>{const f=i.map(d=>E.createContext(d));return function(h){const p=h?.[a]||f;return E.useMemo(()=>({[`__scope${a}`]:{...h,[a]:p}}),[h,p])}};return u.scopeName=a,[s,J_(u,...l)]}function J_(...a){const l=a[0];if(a.length===1)return l;const i=()=>{const s=a.map(u=>({useScope:u(),scopeName:u.scopeName}));return function(f){const d=s.reduce((h,{useScope:p,scopeName:v})=>{const S=p(f)[`__scope${v}`];return{...h,...S}},{});return E.useMemo(()=>({[`__scope${l.scopeName}`]:d}),[d])}};return i.scopeName=l.scopeName,i}function W_(a){const l=a+"CollectionProvider",[i,s]=ed(l),[u,f]=i(l,{collectionRef:{current:null},itemMap:new Map}),d=_=>{const{scope:N,children:q}=_,j=Be.useRef(null),H=Be.useRef(new Map).current;return g.jsx(u,{scope:N,itemMap:H,collectionRef:j,children:q})};d.displayName=l;const h=a+"CollectionSlot",p=zi(h),v=Be.forwardRef((_,N)=>{const{scope:q,children:j}=_,H=f(h,q),P=Ht(N,H.collectionRef);return g.jsx(p,{ref:P,children:j})});v.displayName=h;const b=a+"CollectionItemSlot",S="data-radix-collection-item",T=zi(b),z=Be.forwardRef((_,N)=>{const{scope:q,children:j,...H}=_,P=Be.useRef(null),Z=Ht(N,P),se=f(b,q);return Be.useEffect(()=>(se.itemMap.set(P,{ref:P,...H}),()=>void se.itemMap.delete(P))),g.jsx(T,{[S]:"",ref:Z,children:j})});z.displayName=b;function L(_){const N=f(a+"CollectionConsumer",_);return Be.useCallback(()=>{const j=N.collectionRef.current;if(!j)return[];const H=Array.from(j.querySelectorAll(`[${S}]`));return Array.from(N.itemMap.values()).sort((se,$)=>H.indexOf(se.ref.current)-H.indexOf($.ref.current))},[N.collectionRef,N.itemMap])}return[{Provider:d,Slot:v,ItemSlot:z},L,s]}var I_=E.createContext(void 0);function e1(a){const l=E.useContext(I_);return a||l||"ltr"}function Sl(a){const l=E.useRef(a);return E.useEffect(()=>{l.current=a}),E.useMemo(()=>(...i)=>l.current?.(...i),[])}function t1(a,l=globalThis?.document){const i=Sl(a);E.useEffect(()=>{const s=u=>{u.key==="Escape"&&i(u)};return l.addEventListener("keydown",s,{capture:!0}),()=>l.removeEventListener("keydown",s,{capture:!0})},[i,l])}var n1="DismissableLayer",Df="dismissableLayer.update",a1="dismissableLayer.pointerDownOutside",l1="dismissableLayer.focusOutside",Jp,vy=E.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),py=E.forwardRef((a,l)=>{const{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:s,onPointerDownOutside:u,onFocusOutside:f,onInteractOutside:d,onDismiss:h,...p}=a,v=E.useContext(vy),[b,S]=E.useState(null),T=b?.ownerDocument??globalThis?.document,[,z]=E.useState({}),L=Ht(l,$=>S($)),_=Array.from(v.layers),[N]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),q=_.indexOf(N),j=b?_.indexOf(b):-1,H=v.layersWithOutsidePointerEventsDisabled.size>0,P=j>=q,Z=s1($=>{const J=$.target,ge=[...v.branches].some(Te=>Te.contains(J));!P||ge||(u?.($),d?.($),$.defaultPrevented||h?.())},T),se=o1($=>{const J=$.target;[...v.branches].some(Te=>Te.contains(J))||(f?.($),d?.($),$.defaultPrevented||h?.())},T);return t1($=>{j===v.layers.size-1&&(s?.($),!$.defaultPrevented&&h&&($.preventDefault(),h()))},T),E.useEffect(()=>{if(b)return i&&(v.layersWithOutsidePointerEventsDisabled.size===0&&(Jp=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(b)),v.layers.add(b),Wp(),()=>{i&&v.layersWithOutsidePointerEventsDisabled.size===1&&(T.body.style.pointerEvents=Jp)}},[b,T,i,v]),E.useEffect(()=>()=>{b&&(v.layers.delete(b),v.layersWithOutsidePointerEventsDisabled.delete(b),Wp())},[b,v]),E.useEffect(()=>{const $=()=>z({});return document.addEventListener(Df,$),()=>document.removeEventListener(Df,$)},[]),g.jsx(pt.div,{...p,ref:L,style:{pointerEvents:H?P?"auto":"none":void 0,...a.style},onFocusCapture:Ct(a.onFocusCapture,se.onFocusCapture),onBlurCapture:Ct(a.onBlurCapture,se.onBlurCapture),onPointerDownCapture:Ct(a.onPointerDownCapture,Z.onPointerDownCapture)})});py.displayName=n1;var r1="DismissableLayerBranch",i1=E.forwardRef((a,l)=>{const i=E.useContext(vy),s=E.useRef(null),u=Ht(l,s);return E.useEffect(()=>{const f=s.current;if(f)return i.branches.add(f),()=>{i.branches.delete(f)}},[i.branches]),g.jsx(pt.div,{...a,ref:u})});i1.displayName=r1;function s1(a,l=globalThis?.document){const i=Sl(a),s=E.useRef(!1),u=E.useRef(()=>{});return E.useEffect(()=>{const f=h=>{if(h.target&&!s.current){let p=function(){gy(a1,i,v,{discrete:!0})};const v={originalEvent:h};h.pointerType==="touch"?(l.removeEventListener("click",u.current),u.current=p,l.addEventListener("click",u.current,{once:!0})):p()}else l.removeEventListener("click",u.current);s.current=!1},d=window.setTimeout(()=>{l.addEventListener("pointerdown",f)},0);return()=>{window.clearTimeout(d),l.removeEventListener("pointerdown",f),l.removeEventListener("click",u.current)}},[l,i]),{onPointerDownCapture:()=>s.current=!0}}function o1(a,l=globalThis?.document){const i=Sl(a),s=E.useRef(!1);return E.useEffect(()=>{const u=f=>{f.target&&!s.current&&gy(l1,i,{originalEvent:f},{discrete:!1})};return l.addEventListener("focusin",u),()=>l.removeEventListener("focusin",u)},[l,i]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}function Wp(){const a=new CustomEvent(Df);document.dispatchEvent(a)}function gy(a,l,i,{discrete:s}){const u=i.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:i});l&&u.addEventListener(a,l,{once:!0}),s?Z_(u,f):u.dispatchEvent(f)}var df=0;function u1(){E.useEffect(()=>{const a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??Ip()),document.body.insertAdjacentElement("beforeend",a[1]??Ip()),df++,()=>{df===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(l=>l.remove()),df--}},[])}function Ip(){const a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var hf="focusScope.autoFocusOnMount",mf="focusScope.autoFocusOnUnmount",eg={bubbles:!1,cancelable:!0},c1="FocusScope",yy=E.forwardRef((a,l)=>{const{loop:i=!1,trapped:s=!1,onMountAutoFocus:u,onUnmountAutoFocus:f,...d}=a,[h,p]=E.useState(null),v=Sl(u),b=Sl(f),S=E.useRef(null),T=Ht(l,_=>p(_)),z=E.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;E.useEffect(()=>{if(s){let _=function(H){if(z.paused||!h)return;const P=H.target;h.contains(P)?S.current=P:qa(S.current,{select:!0})},N=function(H){if(z.paused||!h)return;const P=H.relatedTarget;P!==null&&(h.contains(P)||qa(S.current,{select:!0}))},q=function(H){if(document.activeElement===document.body)for(const Z of H)Z.removedNodes.length>0&&qa(h)};document.addEventListener("focusin",_),document.addEventListener("focusout",N);const j=new MutationObserver(q);return h&&j.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",_),document.removeEventListener("focusout",N),j.disconnect()}}},[s,h,z.paused]),E.useEffect(()=>{if(h){ng.add(z);const _=document.activeElement;if(!h.contains(_)){const q=new CustomEvent(hf,eg);h.addEventListener(hf,v),h.dispatchEvent(q),q.defaultPrevented||(f1(p1(by(h)),{select:!0}),document.activeElement===_&&qa(h))}return()=>{h.removeEventListener(hf,v),setTimeout(()=>{const q=new CustomEvent(mf,eg);h.addEventListener(mf,b),h.dispatchEvent(q),q.defaultPrevented||qa(_??document.body,{select:!0}),h.removeEventListener(mf,b),ng.remove(z)},0)}}},[h,v,b,z]);const L=E.useCallback(_=>{if(!i&&!s||z.paused)return;const N=_.key==="Tab"&&!_.altKey&&!_.ctrlKey&&!_.metaKey,q=document.activeElement;if(N&&q){const j=_.currentTarget,[H,P]=d1(j);H&&P?!_.shiftKey&&q===P?(_.preventDefault(),i&&qa(H,{select:!0})):_.shiftKey&&q===H&&(_.preventDefault(),i&&qa(P,{select:!0})):q===j&&_.preventDefault()}},[i,s,z.paused]);return g.jsx(pt.div,{tabIndex:-1,...d,ref:T,onKeyDown:L})});yy.displayName=c1;function f1(a,{select:l=!1}={}){const i=document.activeElement;for(const s of a)if(qa(s,{select:l}),document.activeElement!==i)return}function d1(a){const l=by(a),i=tg(l,a),s=tg(l.reverse(),a);return[i,s]}function by(a){const l=[],i=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:s=>{const u=s.tagName==="INPUT"&&s.type==="hidden";return s.disabled||s.hidden||u?NodeFilter.FILTER_SKIP:s.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;i.nextNode();)l.push(i.currentNode);return l}function tg(a,l){for(const i of a)if(!h1(i,{upTo:l}))return i}function h1(a,{upTo:l}){if(getComputedStyle(a).visibility==="hidden")return!0;for(;a;){if(l!==void 0&&a===l)return!1;if(getComputedStyle(a).display==="none")return!0;a=a.parentElement}return!1}function m1(a){return a instanceof HTMLInputElement&&"select"in a}function qa(a,{select:l=!1}={}){if(a&&a.focus){const i=document.activeElement;a.focus({preventScroll:!0}),a!==i&&m1(a)&&l&&a.select()}}var ng=v1();function v1(){let a=[];return{add(l){const i=a[0];l!==i&&i?.pause(),a=ag(a,l),a.unshift(l)},remove(l){a=ag(a,l),a[0]?.resume()}}}function ag(a,l){const i=[...a],s=i.indexOf(l);return s!==-1&&i.splice(s,1),i}function p1(a){return a.filter(l=>l.tagName!=="A")}var en=globalThis?.document?E.useLayoutEffect:()=>{},g1=wg[" useId ".trim().toString()]||(()=>{}),y1=0;function td(a){const[l,i]=E.useState(g1());return en(()=>{i(s=>s??String(y1++))},[a]),a||(l?`radix-${l}`:"")}const b1=["top","right","bottom","left"],Pa=Math.min,dn=Math.max,Oo=Math.round,ao=Math.floor,Yn=a=>({x:a,y:a}),x1={left:"right",right:"left",bottom:"top",top:"bottom"},S1={start:"end",end:"start"};function zf(a,l,i){return dn(a,Pa(l,i))}function da(a,l){return typeof a=="function"?a(l):a}function ha(a){return a.split("-")[0]}function Cr(a){return a.split("-")[1]}function nd(a){return a==="x"?"y":"x"}function ad(a){return a==="y"?"height":"width"}const _1=new Set(["top","bottom"]);function qn(a){return _1.has(ha(a))?"y":"x"}function ld(a){return nd(qn(a))}function w1(a,l,i){i===void 0&&(i=!1);const s=Cr(a),u=ld(a),f=ad(u);let d=u==="x"?s===(i?"end":"start")?"right":"left":s==="start"?"bottom":"top";return l.reference[f]>l.floating[f]&&(d=Ro(d)),[d,Ro(d)]}function E1(a){const l=Ro(a);return[kf(a),l,kf(l)]}function kf(a){return a.replace(/start|end/g,l=>S1[l])}const lg=["left","right"],rg=["right","left"],A1=["top","bottom"],C1=["bottom","top"];function T1(a,l,i){switch(a){case"top":case"bottom":return i?l?rg:lg:l?lg:rg;case"left":case"right":return l?A1:C1;default:return[]}}function O1(a,l,i,s){const u=Cr(a);let f=T1(ha(a),i==="start",s);return u&&(f=f.map(d=>d+"-"+u),l&&(f=f.concat(f.map(kf)))),f}function Ro(a){return a.replace(/left|right|bottom|top/g,l=>x1[l])}function R1(a){return{top:0,right:0,bottom:0,left:0,...a}}function xy(a){return typeof a!="number"?R1(a):{top:a,right:a,bottom:a,left:a}}function No(a){const{x:l,y:i,width:s,height:u}=a;return{width:s,height:u,top:i,left:l,right:l+s,bottom:i+u,x:l,y:i}}function ig(a,l,i){let{reference:s,floating:u}=a;const f=qn(l),d=ld(l),h=ad(d),p=ha(l),v=f==="y",b=s.x+s.width/2-u.width/2,S=s.y+s.height/2-u.height/2,T=s[h]/2-u[h]/2;let z;switch(p){case"top":z={x:b,y:s.y-u.height};break;case"bottom":z={x:b,y:s.y+s.height};break;case"right":z={x:s.x+s.width,y:S};break;case"left":z={x:s.x-u.width,y:S};break;default:z={x:s.x,y:s.y}}switch(Cr(l)){case"start":z[d]-=T*(i&&v?-1:1);break;case"end":z[d]+=T*(i&&v?-1:1);break}return z}const N1=async(a,l,i)=>{const{placement:s="bottom",strategy:u="absolute",middleware:f=[],platform:d}=i,h=f.filter(Boolean),p=await(d.isRTL==null?void 0:d.isRTL(l));let v=await d.getElementRects({reference:a,floating:l,strategy:u}),{x:b,y:S}=ig(v,s,p),T=s,z={},L=0;for(let _=0;_<h.length;_++){const{name:N,fn:q}=h[_],{x:j,y:H,data:P,reset:Z}=await q({x:b,y:S,initialPlacement:s,placement:T,strategy:u,middlewareData:z,rects:v,platform:d,elements:{reference:a,floating:l}});b=j??b,S=H??S,z={...z,[N]:{...z[N],...P}},Z&&L<=50&&(L++,typeof Z=="object"&&(Z.placement&&(T=Z.placement),Z.rects&&(v=Z.rects===!0?await d.getElementRects({reference:a,floating:l,strategy:u}):Z.rects),{x:b,y:S}=ig(v,T,p)),_=-1)}return{x:b,y:S,placement:T,strategy:u,middlewareData:z}};async function Bi(a,l){var i;l===void 0&&(l={});const{x:s,y:u,platform:f,rects:d,elements:h,strategy:p}=a,{boundary:v="clippingAncestors",rootBoundary:b="viewport",elementContext:S="floating",altBoundary:T=!1,padding:z=0}=da(l,a),L=xy(z),N=h[T?S==="floating"?"reference":"floating":S],q=No(await f.getClippingRect({element:(i=await(f.isElement==null?void 0:f.isElement(N)))==null||i?N:N.contextElement||await(f.getDocumentElement==null?void 0:f.getDocumentElement(h.floating)),boundary:v,rootBoundary:b,strategy:p})),j=S==="floating"?{x:s,y:u,width:d.floating.width,height:d.floating.height}:d.reference,H=await(f.getOffsetParent==null?void 0:f.getOffsetParent(h.floating)),P=await(f.isElement==null?void 0:f.isElement(H))?await(f.getScale==null?void 0:f.getScale(H))||{x:1,y:1}:{x:1,y:1},Z=No(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:j,offsetParent:H,strategy:p}):j);return{top:(q.top-Z.top+L.top)/P.y,bottom:(Z.bottom-q.bottom+L.bottom)/P.y,left:(q.left-Z.left+L.left)/P.x,right:(Z.right-q.right+L.right)/P.x}}const j1=a=>({name:"arrow",options:a,async fn(l){const{x:i,y:s,placement:u,rects:f,platform:d,elements:h,middlewareData:p}=l,{element:v,padding:b=0}=da(a,l)||{};if(v==null)return{};const S=xy(b),T={x:i,y:s},z=ld(u),L=ad(z),_=await d.getDimensions(v),N=z==="y",q=N?"top":"left",j=N?"bottom":"right",H=N?"clientHeight":"clientWidth",P=f.reference[L]+f.reference[z]-T[z]-f.floating[L],Z=T[z]-f.reference[z],se=await(d.getOffsetParent==null?void 0:d.getOffsetParent(v));let $=se?se[H]:0;(!$||!await(d.isElement==null?void 0:d.isElement(se)))&&($=h.floating[H]||f.floating[L]);const J=P/2-Z/2,ge=$/2-_[L]/2-1,Te=Pa(S[q],ge),Ce=Pa(S[j],ge),ne=Te,ce=$-_[L]-Ce,fe=$/2-_[L]/2+J,ve=zf(ne,fe,ce),O=!p.arrow&&Cr(u)!=null&&fe!==ve&&f.reference[L]/2-(fe<ne?Te:Ce)-_[L]/2<0,G=O?fe<ne?fe-ne:fe-ce:0;return{[z]:T[z]+G,data:{[z]:ve,centerOffset:fe-ve-G,...O&&{alignmentOffset:G}},reset:O}}}),M1=function(a){return a===void 0&&(a={}),{name:"flip",options:a,async fn(l){var i,s;const{placement:u,middlewareData:f,rects:d,initialPlacement:h,platform:p,elements:v}=l,{mainAxis:b=!0,crossAxis:S=!0,fallbackPlacements:T,fallbackStrategy:z="bestFit",fallbackAxisSideDirection:L="none",flipAlignment:_=!0,...N}=da(a,l);if((i=f.arrow)!=null&&i.alignmentOffset)return{};const q=ha(u),j=qn(h),H=ha(h)===h,P=await(p.isRTL==null?void 0:p.isRTL(v.floating)),Z=T||(H||!_?[Ro(h)]:E1(h)),se=L!=="none";!T&&se&&Z.push(...O1(h,_,L,P));const $=[h,...Z],J=await Bi(l,N),ge=[];let Te=((s=f.flip)==null?void 0:s.overflows)||[];if(b&&ge.push(J[q]),S){const fe=w1(u,d,P);ge.push(J[fe[0]],J[fe[1]])}if(Te=[...Te,{placement:u,overflows:ge}],!ge.every(fe=>fe<=0)){var Ce,ne;const fe=(((Ce=f.flip)==null?void 0:Ce.index)||0)+1,ve=$[fe];if(ve&&(!(S==="alignment"?j!==qn(ve):!1)||Te.every(V=>V.overflows[0]>0&&qn(V.placement)===j)))return{data:{index:fe,overflows:Te},reset:{placement:ve}};let O=(ne=Te.filter(G=>G.overflows[0]<=0).sort((G,V)=>G.overflows[1]-V.overflows[1])[0])==null?void 0:ne.placement;if(!O)switch(z){case"bestFit":{var ce;const G=(ce=Te.filter(V=>{if(se){const pe=qn(V.placement);return pe===j||pe==="y"}return!0}).map(V=>[V.placement,V.overflows.filter(pe=>pe>0).reduce((pe,w)=>pe+w,0)]).sort((V,pe)=>V[1]-pe[1])[0])==null?void 0:ce[0];G&&(O=G);break}case"initialPlacement":O=h;break}if(u!==O)return{reset:{placement:O}}}return{}}}};function sg(a,l){return{top:a.top-l.height,right:a.right-l.width,bottom:a.bottom-l.height,left:a.left-l.width}}function og(a){return b1.some(l=>a[l]>=0)}const D1=function(a){return a===void 0&&(a={}),{name:"hide",options:a,async fn(l){const{rects:i}=l,{strategy:s="referenceHidden",...u}=da(a,l);switch(s){case"referenceHidden":{const f=await Bi(l,{...u,elementContext:"reference"}),d=sg(f,i.reference);return{data:{referenceHiddenOffsets:d,referenceHidden:og(d)}}}case"escaped":{const f=await Bi(l,{...u,altBoundary:!0}),d=sg(f,i.floating);return{data:{escapedOffsets:d,escaped:og(d)}}}default:return{}}}}},Sy=new Set(["left","top"]);async function z1(a,l){const{placement:i,platform:s,elements:u}=a,f=await(s.isRTL==null?void 0:s.isRTL(u.floating)),d=ha(i),h=Cr(i),p=qn(i)==="y",v=Sy.has(d)?-1:1,b=f&&p?-1:1,S=da(l,a);let{mainAxis:T,crossAxis:z,alignmentAxis:L}=typeof S=="number"?{mainAxis:S,crossAxis:0,alignmentAxis:null}:{mainAxis:S.mainAxis||0,crossAxis:S.crossAxis||0,alignmentAxis:S.alignmentAxis};return h&&typeof L=="number"&&(z=h==="end"?L*-1:L),p?{x:z*b,y:T*v}:{x:T*v,y:z*b}}const k1=function(a){return a===void 0&&(a=0),{name:"offset",options:a,async fn(l){var i,s;const{x:u,y:f,placement:d,middlewareData:h}=l,p=await z1(l,a);return d===((i=h.offset)==null?void 0:i.placement)&&(s=h.arrow)!=null&&s.alignmentOffset?{}:{x:u+p.x,y:f+p.y,data:{...p,placement:d}}}}},U1=function(a){return a===void 0&&(a={}),{name:"shift",options:a,async fn(l){const{x:i,y:s,placement:u}=l,{mainAxis:f=!0,crossAxis:d=!1,limiter:h={fn:N=>{let{x:q,y:j}=N;return{x:q,y:j}}},...p}=da(a,l),v={x:i,y:s},b=await Bi(l,p),S=qn(ha(u)),T=nd(S);let z=v[T],L=v[S];if(f){const N=T==="y"?"top":"left",q=T==="y"?"bottom":"right",j=z+b[N],H=z-b[q];z=zf(j,z,H)}if(d){const N=S==="y"?"top":"left",q=S==="y"?"bottom":"right",j=L+b[N],H=L-b[q];L=zf(j,L,H)}const _=h.fn({...l,[T]:z,[S]:L});return{..._,data:{x:_.x-i,y:_.y-s,enabled:{[T]:f,[S]:d}}}}}},V1=function(a){return a===void 0&&(a={}),{options:a,fn(l){const{x:i,y:s,placement:u,rects:f,middlewareData:d}=l,{offset:h=0,mainAxis:p=!0,crossAxis:v=!0}=da(a,l),b={x:i,y:s},S=qn(u),T=nd(S);let z=b[T],L=b[S];const _=da(h,l),N=typeof _=="number"?{mainAxis:_,crossAxis:0}:{mainAxis:0,crossAxis:0,..._};if(p){const H=T==="y"?"height":"width",P=f.reference[T]-f.floating[H]+N.mainAxis,Z=f.reference[T]+f.reference[H]-N.mainAxis;z<P?z=P:z>Z&&(z=Z)}if(v){var q,j;const H=T==="y"?"width":"height",P=Sy.has(ha(u)),Z=f.reference[S]-f.floating[H]+(P&&((q=d.offset)==null?void 0:q[S])||0)+(P?0:N.crossAxis),se=f.reference[S]+f.reference[H]+(P?0:((j=d.offset)==null?void 0:j[S])||0)-(P?N.crossAxis:0);L<Z?L=Z:L>se&&(L=se)}return{[T]:z,[S]:L}}}},B1=function(a){return a===void 0&&(a={}),{name:"size",options:a,async fn(l){var i,s;const{placement:u,rects:f,platform:d,elements:h}=l,{apply:p=()=>{},...v}=da(a,l),b=await Bi(l,v),S=ha(u),T=Cr(u),z=qn(u)==="y",{width:L,height:_}=f.floating;let N,q;S==="top"||S==="bottom"?(N=S,q=T===(await(d.isRTL==null?void 0:d.isRTL(h.floating))?"start":"end")?"left":"right"):(q=S,N=T==="end"?"top":"bottom");const j=_-b.top-b.bottom,H=L-b.left-b.right,P=Pa(_-b[N],j),Z=Pa(L-b[q],H),se=!l.middlewareData.shift;let $=P,J=Z;if((i=l.middlewareData.shift)!=null&&i.enabled.x&&(J=H),(s=l.middlewareData.shift)!=null&&s.enabled.y&&($=j),se&&!T){const Te=dn(b.left,0),Ce=dn(b.right,0),ne=dn(b.top,0),ce=dn(b.bottom,0);z?J=L-2*(Te!==0||Ce!==0?Te+Ce:dn(b.left,b.right)):$=_-2*(ne!==0||ce!==0?ne+ce:dn(b.top,b.bottom))}await p({...l,availableWidth:J,availableHeight:$});const ge=await d.getDimensions(h.floating);return L!==ge.width||_!==ge.height?{reset:{rects:!0}}:{}}}};function Uo(){return typeof window<"u"}function Tr(a){return _y(a)?(a.nodeName||"").toLowerCase():"#document"}function hn(a){var l;return(a==null||(l=a.ownerDocument)==null?void 0:l.defaultView)||window}function Xn(a){var l;return(l=(_y(a)?a.ownerDocument:a.document)||window.document)==null?void 0:l.documentElement}function _y(a){return Uo()?a instanceof Node||a instanceof hn(a).Node:!1}function jn(a){return Uo()?a instanceof Element||a instanceof hn(a).Element:!1}function Gn(a){return Uo()?a instanceof HTMLElement||a instanceof hn(a).HTMLElement:!1}function ug(a){return!Uo()||typeof ShadowRoot>"u"?!1:a instanceof ShadowRoot||a instanceof hn(a).ShadowRoot}const L1=new Set(["inline","contents"]);function Zi(a){const{overflow:l,overflowX:i,overflowY:s,display:u}=Mn(a);return/auto|scroll|overlay|hidden|clip/.test(l+s+i)&&!L1.has(u)}const H1=new Set(["table","td","th"]);function q1(a){return H1.has(Tr(a))}const Z1=[":popover-open",":modal"];function Vo(a){return Z1.some(l=>{try{return a.matches(l)}catch{return!1}})}const Y1=["transform","translate","scale","rotate","perspective"],G1=["transform","translate","scale","rotate","perspective","filter"],X1=["paint","layout","strict","content"];function rd(a){const l=id(),i=jn(a)?Mn(a):a;return Y1.some(s=>i[s]?i[s]!=="none":!1)||(i.containerType?i.containerType!=="normal":!1)||!l&&(i.backdropFilter?i.backdropFilter!=="none":!1)||!l&&(i.filter?i.filter!=="none":!1)||G1.some(s=>(i.willChange||"").includes(s))||X1.some(s=>(i.contain||"").includes(s))}function Q1(a){let l=Fa(a);for(;Gn(l)&&!wr(l);){if(rd(l))return l;if(Vo(l))return null;l=Fa(l)}return null}function id(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const K1=new Set(["html","body","#document"]);function wr(a){return K1.has(Tr(a))}function Mn(a){return hn(a).getComputedStyle(a)}function Bo(a){return jn(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function Fa(a){if(Tr(a)==="html")return a;const l=a.assignedSlot||a.parentNode||ug(a)&&a.host||Xn(a);return ug(l)?l.host:l}function wy(a){const l=Fa(a);return wr(l)?a.ownerDocument?a.ownerDocument.body:a.body:Gn(l)&&Zi(l)?l:wy(l)}function Li(a,l,i){var s;l===void 0&&(l=[]),i===void 0&&(i=!0);const u=wy(a),f=u===((s=a.ownerDocument)==null?void 0:s.body),d=hn(u);if(f){const h=Uf(d);return l.concat(d,d.visualViewport||[],Zi(u)?u:[],h&&i?Li(h):[])}return l.concat(u,Li(u,[],i))}function Uf(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function Ey(a){const l=Mn(a);let i=parseFloat(l.width)||0,s=parseFloat(l.height)||0;const u=Gn(a),f=u?a.offsetWidth:i,d=u?a.offsetHeight:s,h=Oo(i)!==f||Oo(s)!==d;return h&&(i=f,s=d),{width:i,height:s,$:h}}function sd(a){return jn(a)?a:a.contextElement}function gr(a){const l=sd(a);if(!Gn(l))return Yn(1);const i=l.getBoundingClientRect(),{width:s,height:u,$:f}=Ey(l);let d=(f?Oo(i.width):i.width)/s,h=(f?Oo(i.height):i.height)/u;return(!d||!Number.isFinite(d))&&(d=1),(!h||!Number.isFinite(h))&&(h=1),{x:d,y:h}}const P1=Yn(0);function Ay(a){const l=hn(a);return!id()||!l.visualViewport?P1:{x:l.visualViewport.offsetLeft,y:l.visualViewport.offsetTop}}function F1(a,l,i){return l===void 0&&(l=!1),!i||l&&i!==hn(a)?!1:l}function _l(a,l,i,s){l===void 0&&(l=!1),i===void 0&&(i=!1);const u=a.getBoundingClientRect(),f=sd(a);let d=Yn(1);l&&(s?jn(s)&&(d=gr(s)):d=gr(a));const h=F1(f,i,s)?Ay(f):Yn(0);let p=(u.left+h.x)/d.x,v=(u.top+h.y)/d.y,b=u.width/d.x,S=u.height/d.y;if(f){const T=hn(f),z=s&&jn(s)?hn(s):s;let L=T,_=Uf(L);for(;_&&s&&z!==L;){const N=gr(_),q=_.getBoundingClientRect(),j=Mn(_),H=q.left+(_.clientLeft+parseFloat(j.paddingLeft))*N.x,P=q.top+(_.clientTop+parseFloat(j.paddingTop))*N.y;p*=N.x,v*=N.y,b*=N.x,S*=N.y,p+=H,v+=P,L=hn(_),_=Uf(L)}}return No({width:b,height:S,x:p,y:v})}function od(a,l){const i=Bo(a).scrollLeft;return l?l.left+i:_l(Xn(a)).left+i}function Cy(a,l,i){i===void 0&&(i=!1);const s=a.getBoundingClientRect(),u=s.left+l.scrollLeft-(i?0:od(a,s)),f=s.top+l.scrollTop;return{x:u,y:f}}function $1(a){let{elements:l,rect:i,offsetParent:s,strategy:u}=a;const f=u==="fixed",d=Xn(s),h=l?Vo(l.floating):!1;if(s===d||h&&f)return i;let p={scrollLeft:0,scrollTop:0},v=Yn(1);const b=Yn(0),S=Gn(s);if((S||!S&&!f)&&((Tr(s)!=="body"||Zi(d))&&(p=Bo(s)),Gn(s))){const z=_l(s);v=gr(s),b.x=z.x+s.clientLeft,b.y=z.y+s.clientTop}const T=d&&!S&&!f?Cy(d,p,!0):Yn(0);return{width:i.width*v.x,height:i.height*v.y,x:i.x*v.x-p.scrollLeft*v.x+b.x+T.x,y:i.y*v.y-p.scrollTop*v.y+b.y+T.y}}function J1(a){return Array.from(a.getClientRects())}function W1(a){const l=Xn(a),i=Bo(a),s=a.ownerDocument.body,u=dn(l.scrollWidth,l.clientWidth,s.scrollWidth,s.clientWidth),f=dn(l.scrollHeight,l.clientHeight,s.scrollHeight,s.clientHeight);let d=-i.scrollLeft+od(a);const h=-i.scrollTop;return Mn(s).direction==="rtl"&&(d+=dn(l.clientWidth,s.clientWidth)-u),{width:u,height:f,x:d,y:h}}function I1(a,l){const i=hn(a),s=Xn(a),u=i.visualViewport;let f=s.clientWidth,d=s.clientHeight,h=0,p=0;if(u){f=u.width,d=u.height;const v=id();(!v||v&&l==="fixed")&&(h=u.offsetLeft,p=u.offsetTop)}return{width:f,height:d,x:h,y:p}}const ew=new Set(["absolute","fixed"]);function tw(a,l){const i=_l(a,!0,l==="fixed"),s=i.top+a.clientTop,u=i.left+a.clientLeft,f=Gn(a)?gr(a):Yn(1),d=a.clientWidth*f.x,h=a.clientHeight*f.y,p=u*f.x,v=s*f.y;return{width:d,height:h,x:p,y:v}}function cg(a,l,i){let s;if(l==="viewport")s=I1(a,i);else if(l==="document")s=W1(Xn(a));else if(jn(l))s=tw(l,i);else{const u=Ay(a);s={x:l.x-u.x,y:l.y-u.y,width:l.width,height:l.height}}return No(s)}function Ty(a,l){const i=Fa(a);return i===l||!jn(i)||wr(i)?!1:Mn(i).position==="fixed"||Ty(i,l)}function nw(a,l){const i=l.get(a);if(i)return i;let s=Li(a,[],!1).filter(h=>jn(h)&&Tr(h)!=="body"),u=null;const f=Mn(a).position==="fixed";let d=f?Fa(a):a;for(;jn(d)&&!wr(d);){const h=Mn(d),p=rd(d);!p&&h.position==="fixed"&&(u=null),(f?!p&&!u:!p&&h.position==="static"&&!!u&&ew.has(u.position)||Zi(d)&&!p&&Ty(a,d))?s=s.filter(b=>b!==d):u=h,d=Fa(d)}return l.set(a,s),s}function aw(a){let{element:l,boundary:i,rootBoundary:s,strategy:u}=a;const d=[...i==="clippingAncestors"?Vo(l)?[]:nw(l,this._c):[].concat(i),s],h=d[0],p=d.reduce((v,b)=>{const S=cg(l,b,u);return v.top=dn(S.top,v.top),v.right=Pa(S.right,v.right),v.bottom=Pa(S.bottom,v.bottom),v.left=dn(S.left,v.left),v},cg(l,h,u));return{width:p.right-p.left,height:p.bottom-p.top,x:p.left,y:p.top}}function lw(a){const{width:l,height:i}=Ey(a);return{width:l,height:i}}function rw(a,l,i){const s=Gn(l),u=Xn(l),f=i==="fixed",d=_l(a,!0,f,l);let h={scrollLeft:0,scrollTop:0};const p=Yn(0);function v(){p.x=od(u)}if(s||!s&&!f)if((Tr(l)!=="body"||Zi(u))&&(h=Bo(l)),s){const z=_l(l,!0,f,l);p.x=z.x+l.clientLeft,p.y=z.y+l.clientTop}else u&&v();f&&!s&&u&&v();const b=u&&!s&&!f?Cy(u,h):Yn(0),S=d.left+h.scrollLeft-p.x-b.x,T=d.top+h.scrollTop-p.y-b.y;return{x:S,y:T,width:d.width,height:d.height}}function vf(a){return Mn(a).position==="static"}function fg(a,l){if(!Gn(a)||Mn(a).position==="fixed")return null;if(l)return l(a);let i=a.offsetParent;return Xn(a)===i&&(i=i.ownerDocument.body),i}function Oy(a,l){const i=hn(a);if(Vo(a))return i;if(!Gn(a)){let u=Fa(a);for(;u&&!wr(u);){if(jn(u)&&!vf(u))return u;u=Fa(u)}return i}let s=fg(a,l);for(;s&&q1(s)&&vf(s);)s=fg(s,l);return s&&wr(s)&&vf(s)&&!rd(s)?i:s||Q1(a)||i}const iw=async function(a){const l=this.getOffsetParent||Oy,i=this.getDimensions,s=await i(a.floating);return{reference:rw(a.reference,await l(a.floating),a.strategy),floating:{x:0,y:0,width:s.width,height:s.height}}};function sw(a){return Mn(a).direction==="rtl"}const ow={convertOffsetParentRelativeRectToViewportRelativeRect:$1,getDocumentElement:Xn,getClippingRect:aw,getOffsetParent:Oy,getElementRects:iw,getClientRects:J1,getDimensions:lw,getScale:gr,isElement:jn,isRTL:sw};function Ry(a,l){return a.x===l.x&&a.y===l.y&&a.width===l.width&&a.height===l.height}function uw(a,l){let i=null,s;const u=Xn(a);function f(){var h;clearTimeout(s),(h=i)==null||h.disconnect(),i=null}function d(h,p){h===void 0&&(h=!1),p===void 0&&(p=1),f();const v=a.getBoundingClientRect(),{left:b,top:S,width:T,height:z}=v;if(h||l(),!T||!z)return;const L=ao(S),_=ao(u.clientWidth-(b+T)),N=ao(u.clientHeight-(S+z)),q=ao(b),H={rootMargin:-L+"px "+-_+"px "+-N+"px "+-q+"px",threshold:dn(0,Pa(1,p))||1};let P=!0;function Z(se){const $=se[0].intersectionRatio;if($!==p){if(!P)return d();$?d(!1,$):s=setTimeout(()=>{d(!1,1e-7)},1e3)}$===1&&!Ry(v,a.getBoundingClientRect())&&d(),P=!1}try{i=new IntersectionObserver(Z,{...H,root:u.ownerDocument})}catch{i=new IntersectionObserver(Z,H)}i.observe(a)}return d(!0),f}function cw(a,l,i,s){s===void 0&&(s={});const{ancestorScroll:u=!0,ancestorResize:f=!0,elementResize:d=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:p=!1}=s,v=sd(a),b=u||f?[...v?Li(v):[],...Li(l)]:[];b.forEach(q=>{u&&q.addEventListener("scroll",i,{passive:!0}),f&&q.addEventListener("resize",i)});const S=v&&h?uw(v,i):null;let T=-1,z=null;d&&(z=new ResizeObserver(q=>{let[j]=q;j&&j.target===v&&z&&(z.unobserve(l),cancelAnimationFrame(T),T=requestAnimationFrame(()=>{var H;(H=z)==null||H.observe(l)})),i()}),v&&!p&&z.observe(v),z.observe(l));let L,_=p?_l(a):null;p&&N();function N(){const q=_l(a);_&&!Ry(_,q)&&i(),_=q,L=requestAnimationFrame(N)}return i(),()=>{var q;b.forEach(j=>{u&&j.removeEventListener("scroll",i),f&&j.removeEventListener("resize",i)}),S?.(),(q=z)==null||q.disconnect(),z=null,p&&cancelAnimationFrame(L)}}const fw=k1,dw=U1,hw=M1,mw=B1,vw=D1,dg=j1,pw=V1,gw=(a,l,i)=>{const s=new Map,u={platform:ow,...i},f={...u.platform,_c:s};return N1(a,l,{...u,platform:f})};var yw=typeof document<"u",bw=function(){},co=yw?E.useLayoutEffect:bw;function jo(a,l){if(a===l)return!0;if(typeof a!=typeof l)return!1;if(typeof a=="function"&&a.toString()===l.toString())return!0;let i,s,u;if(a&&l&&typeof a=="object"){if(Array.isArray(a)){if(i=a.length,i!==l.length)return!1;for(s=i;s--!==0;)if(!jo(a[s],l[s]))return!1;return!0}if(u=Object.keys(a),i=u.length,i!==Object.keys(l).length)return!1;for(s=i;s--!==0;)if(!{}.hasOwnProperty.call(l,u[s]))return!1;for(s=i;s--!==0;){const f=u[s];if(!(f==="_owner"&&a.$$typeof)&&!jo(a[f],l[f]))return!1}return!0}return a!==a&&l!==l}function Ny(a){return typeof window>"u"?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function hg(a,l){const i=Ny(a);return Math.round(l*i)/i}function pf(a){const l=E.useRef(a);return co(()=>{l.current=a}),l}function xw(a){a===void 0&&(a={});const{placement:l="bottom",strategy:i="absolute",middleware:s=[],platform:u,elements:{reference:f,floating:d}={},transform:h=!0,whileElementsMounted:p,open:v}=a,[b,S]=E.useState({x:0,y:0,strategy:i,placement:l,middlewareData:{},isPositioned:!1}),[T,z]=E.useState(s);jo(T,s)||z(s);const[L,_]=E.useState(null),[N,q]=E.useState(null),j=E.useCallback(V=>{V!==se.current&&(se.current=V,_(V))},[]),H=E.useCallback(V=>{V!==$.current&&($.current=V,q(V))},[]),P=f||L,Z=d||N,se=E.useRef(null),$=E.useRef(null),J=E.useRef(b),ge=p!=null,Te=pf(p),Ce=pf(u),ne=pf(v),ce=E.useCallback(()=>{if(!se.current||!$.current)return;const V={placement:l,strategy:i,middleware:T};Ce.current&&(V.platform=Ce.current),gw(se.current,$.current,V).then(pe=>{const w={...pe,isPositioned:ne.current!==!1};fe.current&&!jo(J.current,w)&&(J.current=w,qi.flushSync(()=>{S(w)}))})},[T,l,i,Ce,ne]);co(()=>{v===!1&&J.current.isPositioned&&(J.current.isPositioned=!1,S(V=>({...V,isPositioned:!1})))},[v]);const fe=E.useRef(!1);co(()=>(fe.current=!0,()=>{fe.current=!1}),[]),co(()=>{if(P&&(se.current=P),Z&&($.current=Z),P&&Z){if(Te.current)return Te.current(P,Z,ce);ce()}},[P,Z,ce,Te,ge]);const ve=E.useMemo(()=>({reference:se,floating:$,setReference:j,setFloating:H}),[j,H]),O=E.useMemo(()=>({reference:P,floating:Z}),[P,Z]),G=E.useMemo(()=>{const V={position:i,left:0,top:0};if(!O.floating)return V;const pe=hg(O.floating,b.x),w=hg(O.floating,b.y);return h?{...V,transform:"translate("+pe+"px, "+w+"px)",...Ny(O.floating)>=1.5&&{willChange:"transform"}}:{position:i,left:pe,top:w}},[i,h,O.floating,b.x,b.y]);return E.useMemo(()=>({...b,update:ce,refs:ve,elements:O,floatingStyles:G}),[b,ce,ve,O,G])}const Sw=a=>{function l(i){return{}.hasOwnProperty.call(i,"current")}return{name:"arrow",options:a,fn(i){const{element:s,padding:u}=typeof a=="function"?a(i):a;return s&&l(s)?s.current!=null?dg({element:s.current,padding:u}).fn(i):{}:s?dg({element:s,padding:u}).fn(i):{}}}},_w=(a,l)=>({...fw(a),options:[a,l]}),ww=(a,l)=>({...dw(a),options:[a,l]}),Ew=(a,l)=>({...pw(a),options:[a,l]}),Aw=(a,l)=>({...hw(a),options:[a,l]}),Cw=(a,l)=>({...mw(a),options:[a,l]}),Tw=(a,l)=>({...vw(a),options:[a,l]}),Ow=(a,l)=>({...Sw(a),options:[a,l]});var Rw="Arrow",jy=E.forwardRef((a,l)=>{const{children:i,width:s=10,height:u=5,...f}=a;return g.jsx(pt.svg,{...f,ref:l,width:s,height:u,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?i:g.jsx("polygon",{points:"0,0 30,0 15,10"})})});jy.displayName=Rw;var Nw=jy;function jw(a){const[l,i]=E.useState(void 0);return en(()=>{if(a){i({width:a.offsetWidth,height:a.offsetHeight});const s=new ResizeObserver(u=>{if(!Array.isArray(u)||!u.length)return;const f=u[0];let d,h;if("borderBoxSize"in f){const p=f.borderBoxSize,v=Array.isArray(p)?p[0]:p;d=v.inlineSize,h=v.blockSize}else d=a.offsetWidth,h=a.offsetHeight;i({width:d,height:h})});return s.observe(a,{box:"border-box"}),()=>s.unobserve(a)}else i(void 0)},[a]),l}var ud="Popper",[My,Dy]=ed(ud),[Mw,zy]=My(ud),ky=a=>{const{__scopePopper:l,children:i}=a,[s,u]=E.useState(null);return g.jsx(Mw,{scope:l,anchor:s,onAnchorChange:u,children:i})};ky.displayName=ud;var Uy="PopperAnchor",Vy=E.forwardRef((a,l)=>{const{__scopePopper:i,virtualRef:s,...u}=a,f=zy(Uy,i),d=E.useRef(null),h=Ht(l,d);return E.useEffect(()=>{f.onAnchorChange(s?.current||d.current)}),s?null:g.jsx(pt.div,{...u,ref:h})});Vy.displayName=Uy;var cd="PopperContent",[Dw,zw]=My(cd),By=E.forwardRef((a,l)=>{const{__scopePopper:i,side:s="bottom",sideOffset:u=0,align:f="center",alignOffset:d=0,arrowPadding:h=0,avoidCollisions:p=!0,collisionBoundary:v=[],collisionPadding:b=0,sticky:S="partial",hideWhenDetached:T=!1,updatePositionStrategy:z="optimized",onPlaced:L,..._}=a,N=zy(cd,i),[q,j]=E.useState(null),H=Ht(l,te=>j(te)),[P,Z]=E.useState(null),se=jw(P),$=se?.width??0,J=se?.height??0,ge=s+(f!=="center"?"-"+f:""),Te=typeof b=="number"?b:{top:0,right:0,bottom:0,left:0,...b},Ce=Array.isArray(v)?v:[v],ne=Ce.length>0,ce={padding:Te,boundary:Ce.filter(Uw),altBoundary:ne},{refs:fe,floatingStyles:ve,placement:O,isPositioned:G,middlewareData:V}=xw({strategy:"fixed",placement:ge,whileElementsMounted:(...te)=>cw(...te,{animationFrame:z==="always"}),elements:{reference:N.anchor},middleware:[_w({mainAxis:u+J,alignmentAxis:d}),p&&ww({mainAxis:!0,crossAxis:!1,limiter:S==="partial"?Ew():void 0,...ce}),p&&Aw({...ce}),Cw({...ce,apply:({elements:te,rects:we,availableWidth:Xe,availableHeight:Ue})=>{const{width:Ze,height:Ke}=we.reference,Mt=te.floating.style;Mt.setProperty("--radix-popper-available-width",`${Xe}px`),Mt.setProperty("--radix-popper-available-height",`${Ue}px`),Mt.setProperty("--radix-popper-anchor-width",`${Ze}px`),Mt.setProperty("--radix-popper-anchor-height",`${Ke}px`)}}),P&&Ow({element:P,padding:h}),Vw({arrowWidth:$,arrowHeight:J}),T&&Tw({strategy:"referenceHidden",...ce})]}),[pe,w]=qy(O),K=Sl(L);en(()=>{G&&K?.()},[G,K]);const ae=V.arrow?.x,W=V.arrow?.y,re=V.arrow?.centerOffset!==0,[Re,Se]=E.useState();return en(()=>{q&&Se(window.getComputedStyle(q).zIndex)},[q]),g.jsx("div",{ref:fe.setFloating,"data-radix-popper-content-wrapper":"",style:{...ve,transform:G?ve.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Re,"--radix-popper-transform-origin":[V.transformOrigin?.x,V.transformOrigin?.y].join(" "),...V.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:g.jsx(Dw,{scope:i,placedSide:pe,onArrowChange:Z,arrowX:ae,arrowY:W,shouldHideArrow:re,children:g.jsx(pt.div,{"data-side":pe,"data-align":w,..._,ref:H,style:{..._.style,animation:G?void 0:"none"}})})})});By.displayName=cd;var Ly="PopperArrow",kw={top:"bottom",right:"left",bottom:"top",left:"right"},Hy=E.forwardRef(function(l,i){const{__scopePopper:s,...u}=l,f=zw(Ly,s),d=kw[f.placedSide];return g.jsx("span",{ref:f.onArrowChange,style:{position:"absolute",left:f.arrowX,top:f.arrowY,[d]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f.placedSide],visibility:f.shouldHideArrow?"hidden":void 0},children:g.jsx(Nw,{...u,ref:i,style:{...u.style,display:"block"}})})});Hy.displayName=Ly;function Uw(a){return a!==null}var Vw=a=>({name:"transformOrigin",options:a,fn(l){const{placement:i,rects:s,middlewareData:u}=l,d=u.arrow?.centerOffset!==0,h=d?0:a.arrowWidth,p=d?0:a.arrowHeight,[v,b]=qy(i),S={start:"0%",center:"50%",end:"100%"}[b],T=(u.arrow?.x??0)+h/2,z=(u.arrow?.y??0)+p/2;let L="",_="";return v==="bottom"?(L=d?S:`${T}px`,_=`${-p}px`):v==="top"?(L=d?S:`${T}px`,_=`${s.floating.height+p}px`):v==="right"?(L=`${-p}px`,_=d?S:`${z}px`):v==="left"&&(L=`${s.floating.width+p}px`,_=d?S:`${z}px`),{data:{x:L,y:_}}}});function qy(a){const[l,i="center"]=a.split("-");return[l,i]}var Bw=ky,Lw=Vy,Hw=By,qw=Hy,Zw="Portal",Zy=E.forwardRef((a,l)=>{const{container:i,...s}=a,[u,f]=E.useState(!1);en(()=>f(!0),[]);const d=i||u&&globalThis?.document?.body;return d?H_.createPortal(g.jsx(pt.div,{...s,ref:l}),d):null});Zy.displayName=Zw;var Yw=wg[" useInsertionEffect ".trim().toString()]||en;function mg({prop:a,defaultProp:l,onChange:i=()=>{},caller:s}){const[u,f,d]=Gw({defaultProp:l,onChange:i}),h=a!==void 0,p=h?a:u;{const b=E.useRef(a!==void 0);E.useEffect(()=>{const S=b.current;S!==h&&console.warn(`${s} is changing from ${S?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),b.current=h},[h,s])}const v=E.useCallback(b=>{if(h){const S=Xw(b)?b(a):b;S!==a&&d.current?.(S)}else f(b)},[h,a,f,d]);return[p,v]}function Gw({defaultProp:a,onChange:l}){const[i,s]=E.useState(a),u=E.useRef(i),f=E.useRef(l);return Yw(()=>{f.current=l},[l]),E.useEffect(()=>{u.current!==i&&(f.current?.(i),u.current=i)},[i,u]),[i,s,f]}function Xw(a){return typeof a=="function"}function Qw(a){const l=E.useRef({value:a,previous:a});return E.useMemo(()=>(l.current.value!==a&&(l.current.previous=l.current.value,l.current.value=a),l.current.previous),[a])}var Yy=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),Kw="VisuallyHidden",Pw=E.forwardRef((a,l)=>g.jsx(pt.span,{...a,ref:l,style:{...Yy,...a.style}}));Pw.displayName=Kw;var Fw=function(a){if(typeof document>"u")return null;var l=Array.isArray(a)?a[0]:a;return l.ownerDocument.body},hr=new WeakMap,lo=new WeakMap,ro={},gf=0,Gy=function(a){return a&&(a.host||Gy(a.parentNode))},$w=function(a,l){return l.map(function(i){if(a.contains(i))return i;var s=Gy(i);return s&&a.contains(s)?s:(console.error("aria-hidden",i,"in not contained inside",a,". Doing nothing"),null)}).filter(function(i){return!!i})},Jw=function(a,l,i,s){var u=$w(l,Array.isArray(a)?a:[a]);ro[i]||(ro[i]=new WeakMap);var f=ro[i],d=[],h=new Set,p=new Set(u),v=function(S){!S||h.has(S)||(h.add(S),v(S.parentNode))};u.forEach(v);var b=function(S){!S||p.has(S)||Array.prototype.forEach.call(S.children,function(T){if(h.has(T))b(T);else try{var z=T.getAttribute(s),L=z!==null&&z!=="false",_=(hr.get(T)||0)+1,N=(f.get(T)||0)+1;hr.set(T,_),f.set(T,N),d.push(T),_===1&&L&&lo.set(T,!0),N===1&&T.setAttribute(i,"true"),L||T.setAttribute(s,"true")}catch(q){console.error("aria-hidden: cannot operate on ",T,q)}})};return b(l),h.clear(),gf++,function(){d.forEach(function(S){var T=hr.get(S)-1,z=f.get(S)-1;hr.set(S,T),f.set(S,z),T||(lo.has(S)||S.removeAttribute(s),lo.delete(S)),z||S.removeAttribute(i)}),gf--,gf||(hr=new WeakMap,hr=new WeakMap,lo=new WeakMap,ro={})}},Ww=function(a,l,i){i===void 0&&(i="data-aria-hidden");var s=Array.from(Array.isArray(a)?a:[a]),u=Fw(a);return u?(s.push.apply(s,Array.from(u.querySelectorAll("[aria-live], script"))),Jw(s,u,i,"aria-hidden")):function(){return null}},Ln=function(){return Ln=Object.assign||function(l){for(var i,s=1,u=arguments.length;s<u;s++){i=arguments[s];for(var f in i)Object.prototype.hasOwnProperty.call(i,f)&&(l[f]=i[f])}return l},Ln.apply(this,arguments)};function Xy(a,l){var i={};for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&l.indexOf(s)<0&&(i[s]=a[s]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var u=0,s=Object.getOwnPropertySymbols(a);u<s.length;u++)l.indexOf(s[u])<0&&Object.prototype.propertyIsEnumerable.call(a,s[u])&&(i[s[u]]=a[s[u]]);return i}function Iw(a,l,i){if(i||arguments.length===2)for(var s=0,u=l.length,f;s<u;s++)(f||!(s in l))&&(f||(f=Array.prototype.slice.call(l,0,s)),f[s]=l[s]);return a.concat(f||Array.prototype.slice.call(l))}var fo="right-scroll-bar-position",ho="width-before-scroll-bar",eE="with-scroll-bars-hidden",tE="--removed-body-scroll-bar-size";function yf(a,l){return typeof a=="function"?a(l):a&&(a.current=l),a}function nE(a,l){var i=E.useState(function(){return{value:a,callback:l,facade:{get current(){return i.value},set current(s){var u=i.value;u!==s&&(i.value=s,i.callback(s,u))}}}})[0];return i.callback=l,i.facade}var aE=typeof window<"u"?E.useLayoutEffect:E.useEffect,vg=new WeakMap;function lE(a,l){var i=nE(null,function(s){return a.forEach(function(u){return yf(u,s)})});return aE(function(){var s=vg.get(i);if(s){var u=new Set(s),f=new Set(a),d=i.current;u.forEach(function(h){f.has(h)||yf(h,null)}),f.forEach(function(h){u.has(h)||yf(h,d)})}vg.set(i,a)},[a]),i}function rE(a){return a}function iE(a,l){l===void 0&&(l=rE);var i=[],s=!1,u={read:function(){if(s)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:a},useMedium:function(f){var d=l(f,s);return i.push(d),function(){i=i.filter(function(h){return h!==d})}},assignSyncMedium:function(f){for(s=!0;i.length;){var d=i;i=[],d.forEach(f)}i={push:function(h){return f(h)},filter:function(){return i}}},assignMedium:function(f){s=!0;var d=[];if(i.length){var h=i;i=[],h.forEach(f),d=i}var p=function(){var b=d;d=[],b.forEach(f)},v=function(){return Promise.resolve().then(p)};v(),i={push:function(b){d.push(b),v()},filter:function(b){return d=d.filter(b),i}}}};return u}function sE(a){a===void 0&&(a={});var l=iE(null);return l.options=Ln({async:!0,ssr:!1},a),l}var Qy=function(a){var l=a.sideCar,i=Xy(a,["sideCar"]);if(!l)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var s=l.read();if(!s)throw new Error("Sidecar medium not found");return E.createElement(s,Ln({},i))};Qy.isSideCarExport=!0;function oE(a,l){return a.useMedium(l),Qy}var Ky=sE(),bf=function(){},Lo=E.forwardRef(function(a,l){var i=E.useRef(null),s=E.useState({onScrollCapture:bf,onWheelCapture:bf,onTouchMoveCapture:bf}),u=s[0],f=s[1],d=a.forwardProps,h=a.children,p=a.className,v=a.removeScrollBar,b=a.enabled,S=a.shards,T=a.sideCar,z=a.noRelative,L=a.noIsolation,_=a.inert,N=a.allowPinchZoom,q=a.as,j=q===void 0?"div":q,H=a.gapMode,P=Xy(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),Z=T,se=lE([i,l]),$=Ln(Ln({},P),u);return E.createElement(E.Fragment,null,b&&E.createElement(Z,{sideCar:Ky,removeScrollBar:v,shards:S,noRelative:z,noIsolation:L,inert:_,setCallbacks:f,allowPinchZoom:!!N,lockRef:i,gapMode:H}),d?E.cloneElement(E.Children.only(h),Ln(Ln({},$),{ref:se})):E.createElement(j,Ln({},$,{className:p,ref:se}),h))});Lo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Lo.classNames={fullWidth:ho,zeroRight:fo};var uE=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function cE(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var l=uE();return l&&a.setAttribute("nonce",l),a}function fE(a,l){a.styleSheet?a.styleSheet.cssText=l:a.appendChild(document.createTextNode(l))}function dE(a){var l=document.head||document.getElementsByTagName("head")[0];l.appendChild(a)}var hE=function(){var a=0,l=null;return{add:function(i){a==0&&(l=cE())&&(fE(l,i),dE(l)),a++},remove:function(){a--,!a&&l&&(l.parentNode&&l.parentNode.removeChild(l),l=null)}}},mE=function(){var a=hE();return function(l,i){E.useEffect(function(){return a.add(l),function(){a.remove()}},[l&&i])}},Py=function(){var a=mE(),l=function(i){var s=i.styles,u=i.dynamic;return a(s,u),null};return l},vE={left:0,top:0,right:0,gap:0},xf=function(a){return parseInt(a||"",10)||0},pE=function(a){var l=window.getComputedStyle(document.body),i=l[a==="padding"?"paddingLeft":"marginLeft"],s=l[a==="padding"?"paddingTop":"marginTop"],u=l[a==="padding"?"paddingRight":"marginRight"];return[xf(i),xf(s),xf(u)]},gE=function(a){if(a===void 0&&(a="margin"),typeof window>"u")return vE;var l=pE(a),i=document.documentElement.clientWidth,s=window.innerWidth;return{left:l[0],top:l[1],right:l[2],gap:Math.max(0,s-i+l[2]-l[0])}},yE=Py(),yr="data-scroll-locked",bE=function(a,l,i,s){var u=a.left,f=a.top,d=a.right,h=a.gap;return i===void 0&&(i="margin"),`
  .`.concat(eE,` {
   overflow: hidden `).concat(s,`;
   padding-right: `).concat(h,"px ").concat(s,`;
  }
  body[`).concat(yr,`] {
    overflow: hidden `).concat(s,`;
    overscroll-behavior: contain;
    `).concat([l&&"position: relative ".concat(s,";"),i==="margin"&&`
    padding-left: `.concat(u,`px;
    padding-top: `).concat(f,`px;
    padding-right: `).concat(d,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(s,`;
    `),i==="padding"&&"padding-right: ".concat(h,"px ").concat(s,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(fo,` {
    right: `).concat(h,"px ").concat(s,`;
  }
  
  .`).concat(ho,` {
    margin-right: `).concat(h,"px ").concat(s,`;
  }
  
  .`).concat(fo," .").concat(fo,` {
    right: 0 `).concat(s,`;
  }
  
  .`).concat(ho," .").concat(ho,` {
    margin-right: 0 `).concat(s,`;
  }
  
  body[`).concat(yr,`] {
    `).concat(tE,": ").concat(h,`px;
  }
`)},pg=function(){var a=parseInt(document.body.getAttribute(yr)||"0",10);return isFinite(a)?a:0},xE=function(){E.useEffect(function(){return document.body.setAttribute(yr,(pg()+1).toString()),function(){var a=pg()-1;a<=0?document.body.removeAttribute(yr):document.body.setAttribute(yr,a.toString())}},[])},SE=function(a){var l=a.noRelative,i=a.noImportant,s=a.gapMode,u=s===void 0?"margin":s;xE();var f=E.useMemo(function(){return gE(u)},[u]);return E.createElement(yE,{styles:bE(f,!l,u,i?"":"!important")})},Vf=!1;if(typeof window<"u")try{var io=Object.defineProperty({},"passive",{get:function(){return Vf=!0,!0}});window.addEventListener("test",io,io),window.removeEventListener("test",io,io)}catch{Vf=!1}var mr=Vf?{passive:!1}:!1,_E=function(a){return a.tagName==="TEXTAREA"},Fy=function(a,l){if(!(a instanceof Element))return!1;var i=window.getComputedStyle(a);return i[l]!=="hidden"&&!(i.overflowY===i.overflowX&&!_E(a)&&i[l]==="visible")},wE=function(a){return Fy(a,"overflowY")},EE=function(a){return Fy(a,"overflowX")},gg=function(a,l){var i=l.ownerDocument,s=l;do{typeof ShadowRoot<"u"&&s instanceof ShadowRoot&&(s=s.host);var u=$y(a,s);if(u){var f=Jy(a,s),d=f[1],h=f[2];if(d>h)return!0}s=s.parentNode}while(s&&s!==i.body);return!1},AE=function(a){var l=a.scrollTop,i=a.scrollHeight,s=a.clientHeight;return[l,i,s]},CE=function(a){var l=a.scrollLeft,i=a.scrollWidth,s=a.clientWidth;return[l,i,s]},$y=function(a,l){return a==="v"?wE(l):EE(l)},Jy=function(a,l){return a==="v"?AE(l):CE(l)},TE=function(a,l){return a==="h"&&l==="rtl"?-1:1},OE=function(a,l,i,s,u){var f=TE(a,window.getComputedStyle(l).direction),d=f*s,h=i.target,p=l.contains(h),v=!1,b=d>0,S=0,T=0;do{if(!h)break;var z=Jy(a,h),L=z[0],_=z[1],N=z[2],q=_-N-f*L;(L||q)&&$y(a,h)&&(S+=q,T+=L);var j=h.parentNode;h=j&&j.nodeType===Node.DOCUMENT_FRAGMENT_NODE?j.host:j}while(!p&&h!==document.body||p&&(l.contains(h)||l===h));return(b&&Math.abs(S)<1||!b&&Math.abs(T)<1)&&(v=!0),v},so=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},yg=function(a){return[a.deltaX,a.deltaY]},bg=function(a){return a&&"current"in a?a.current:a},RE=function(a,l){return a[0]===l[0]&&a[1]===l[1]},NE=function(a){return`
  .block-interactivity-`.concat(a,` {pointer-events: none;}
  .allow-interactivity-`).concat(a,` {pointer-events: all;}
`)},jE=0,vr=[];function ME(a){var l=E.useRef([]),i=E.useRef([0,0]),s=E.useRef(),u=E.useState(jE++)[0],f=E.useState(Py)[0],d=E.useRef(a);E.useEffect(function(){d.current=a},[a]),E.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(u));var _=Iw([a.lockRef.current],(a.shards||[]).map(bg),!0).filter(Boolean);return _.forEach(function(N){return N.classList.add("allow-interactivity-".concat(u))}),function(){document.body.classList.remove("block-interactivity-".concat(u)),_.forEach(function(N){return N.classList.remove("allow-interactivity-".concat(u))})}}},[a.inert,a.lockRef.current,a.shards]);var h=E.useCallback(function(_,N){if("touches"in _&&_.touches.length===2||_.type==="wheel"&&_.ctrlKey)return!d.current.allowPinchZoom;var q=so(_),j=i.current,H="deltaX"in _?_.deltaX:j[0]-q[0],P="deltaY"in _?_.deltaY:j[1]-q[1],Z,se=_.target,$=Math.abs(H)>Math.abs(P)?"h":"v";if("touches"in _&&$==="h"&&se.type==="range")return!1;var J=gg($,se);if(!J)return!0;if(J?Z=$:(Z=$==="v"?"h":"v",J=gg($,se)),!J)return!1;if(!s.current&&"changedTouches"in _&&(H||P)&&(s.current=Z),!Z)return!0;var ge=s.current||Z;return OE(ge,N,_,ge==="h"?H:P)},[]),p=E.useCallback(function(_){var N=_;if(!(!vr.length||vr[vr.length-1]!==f)){var q="deltaY"in N?yg(N):so(N),j=l.current.filter(function(Z){return Z.name===N.type&&(Z.target===N.target||N.target===Z.shadowParent)&&RE(Z.delta,q)})[0];if(j&&j.should){N.cancelable&&N.preventDefault();return}if(!j){var H=(d.current.shards||[]).map(bg).filter(Boolean).filter(function(Z){return Z.contains(N.target)}),P=H.length>0?h(N,H[0]):!d.current.noIsolation;P&&N.cancelable&&N.preventDefault()}}},[]),v=E.useCallback(function(_,N,q,j){var H={name:_,delta:N,target:q,should:j,shadowParent:DE(q)};l.current.push(H),setTimeout(function(){l.current=l.current.filter(function(P){return P!==H})},1)},[]),b=E.useCallback(function(_){i.current=so(_),s.current=void 0},[]),S=E.useCallback(function(_){v(_.type,yg(_),_.target,h(_,a.lockRef.current))},[]),T=E.useCallback(function(_){v(_.type,so(_),_.target,h(_,a.lockRef.current))},[]);E.useEffect(function(){return vr.push(f),a.setCallbacks({onScrollCapture:S,onWheelCapture:S,onTouchMoveCapture:T}),document.addEventListener("wheel",p,mr),document.addEventListener("touchmove",p,mr),document.addEventListener("touchstart",b,mr),function(){vr=vr.filter(function(_){return _!==f}),document.removeEventListener("wheel",p,mr),document.removeEventListener("touchmove",p,mr),document.removeEventListener("touchstart",b,mr)}},[]);var z=a.removeScrollBar,L=a.inert;return E.createElement(E.Fragment,null,L?E.createElement(f,{styles:NE(u)}):null,z?E.createElement(SE,{noRelative:a.noRelative,gapMode:a.gapMode}):null)}function DE(a){for(var l=null;a!==null;)a instanceof ShadowRoot&&(l=a.host,a=a.host),a=a.parentNode;return l}const zE=oE(Ky,ME);var Wy=E.forwardRef(function(a,l){return E.createElement(Lo,Ln({},a,{ref:l,sideCar:zE}))});Wy.classNames=Lo.classNames;var kE=[" ","Enter","ArrowUp","ArrowDown"],UE=[" ","Enter"],wl="Select",[Ho,qo,VE]=W_(wl),[Or,UA]=ed(wl,[VE,Dy]),Zo=Dy(),[BE,$a]=Or(wl),[LE,HE]=Or(wl),Iy=a=>{const{__scopeSelect:l,children:i,open:s,defaultOpen:u,onOpenChange:f,value:d,defaultValue:h,onValueChange:p,dir:v,name:b,autoComplete:S,disabled:T,required:z,form:L}=a,_=Zo(l),[N,q]=E.useState(null),[j,H]=E.useState(null),[P,Z]=E.useState(!1),se=e1(v),[$,J]=mg({prop:s,defaultProp:u??!1,onChange:f,caller:wl}),[ge,Te]=mg({prop:d,defaultProp:h,onChange:p,caller:wl}),Ce=E.useRef(null),ne=N?L||!!N.closest("form"):!0,[ce,fe]=E.useState(new Set),ve=Array.from(ce).map(O=>O.props.value).join(";");return g.jsx(Bw,{..._,children:g.jsxs(BE,{required:z,scope:l,trigger:N,onTriggerChange:q,valueNode:j,onValueNodeChange:H,valueNodeHasChildren:P,onValueNodeHasChildrenChange:Z,contentId:td(),value:ge,onValueChange:Te,open:$,onOpenChange:J,dir:se,triggerPointerDownPosRef:Ce,disabled:T,children:[g.jsx(Ho.Provider,{scope:l,children:g.jsx(LE,{scope:a.__scopeSelect,onNativeOptionAdd:E.useCallback(O=>{fe(G=>new Set(G).add(O))},[]),onNativeOptionRemove:E.useCallback(O=>{fe(G=>{const V=new Set(G);return V.delete(O),V})},[]),children:i})}),ne?g.jsxs(Sb,{"aria-hidden":!0,required:z,tabIndex:-1,name:b,autoComplete:S,value:ge,onChange:O=>Te(O.target.value),disabled:T,form:L,children:[ge===void 0?g.jsx("option",{value:""}):null,Array.from(ce)]},ve):null]})})};Iy.displayName=wl;var eb="SelectTrigger",tb=E.forwardRef((a,l)=>{const{__scopeSelect:i,disabled:s=!1,...u}=a,f=Zo(i),d=$a(eb,i),h=d.disabled||s,p=Ht(l,d.onTriggerChange),v=qo(i),b=E.useRef("touch"),[S,T,z]=wb(_=>{const N=v().filter(H=>!H.disabled),q=N.find(H=>H.value===d.value),j=Eb(N,_,q);j!==void 0&&d.onValueChange(j.value)}),L=_=>{h||(d.onOpenChange(!0),z()),_&&(d.triggerPointerDownPosRef.current={x:Math.round(_.pageX),y:Math.round(_.pageY)})};return g.jsx(Lw,{asChild:!0,...f,children:g.jsx(pt.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:h,"data-disabled":h?"":void 0,"data-placeholder":_b(d.value)?"":void 0,...u,ref:p,onClick:Ct(u.onClick,_=>{_.currentTarget.focus(),b.current!=="mouse"&&L(_)}),onPointerDown:Ct(u.onPointerDown,_=>{b.current=_.pointerType;const N=_.target;N.hasPointerCapture(_.pointerId)&&N.releasePointerCapture(_.pointerId),_.button===0&&_.ctrlKey===!1&&_.pointerType==="mouse"&&(L(_),_.preventDefault())}),onKeyDown:Ct(u.onKeyDown,_=>{const N=S.current!=="";!(_.ctrlKey||_.altKey||_.metaKey)&&_.key.length===1&&T(_.key),!(N&&_.key===" ")&&kE.includes(_.key)&&(L(),_.preventDefault())})})})});tb.displayName=eb;var nb="SelectValue",ab=E.forwardRef((a,l)=>{const{__scopeSelect:i,className:s,style:u,children:f,placeholder:d="",...h}=a,p=$a(nb,i),{onValueNodeHasChildrenChange:v}=p,b=f!==void 0,S=Ht(l,p.onValueNodeChange);return en(()=>{v(b)},[v,b]),g.jsx(pt.span,{...h,ref:S,style:{pointerEvents:"none"},children:_b(p.value)?g.jsx(g.Fragment,{children:d}):f})});ab.displayName=nb;var qE="SelectIcon",lb=E.forwardRef((a,l)=>{const{__scopeSelect:i,children:s,...u}=a;return g.jsx(pt.span,{"aria-hidden":!0,...u,ref:l,children:s||"▼"})});lb.displayName=qE;var ZE="SelectPortal",rb=a=>g.jsx(Zy,{asChild:!0,...a});rb.displayName=ZE;var El="SelectContent",ib=E.forwardRef((a,l)=>{const i=$a(El,a.__scopeSelect),[s,u]=E.useState();if(en(()=>{u(new DocumentFragment)},[]),!i.open){const f=s;return f?qi.createPortal(g.jsx(sb,{scope:a.__scopeSelect,children:g.jsx(Ho.Slot,{scope:a.__scopeSelect,children:g.jsx("div",{children:a.children})})}),f):null}return g.jsx(ob,{...a,ref:l})});ib.displayName=El;var On=10,[sb,Ja]=Or(El),YE="SelectContentImpl",GE=zi("SelectContent.RemoveScroll"),ob=E.forwardRef((a,l)=>{const{__scopeSelect:i,position:s="item-aligned",onCloseAutoFocus:u,onEscapeKeyDown:f,onPointerDownOutside:d,side:h,sideOffset:p,align:v,alignOffset:b,arrowPadding:S,collisionBoundary:T,collisionPadding:z,sticky:L,hideWhenDetached:_,avoidCollisions:N,...q}=a,j=$a(El,i),[H,P]=E.useState(null),[Z,se]=E.useState(null),$=Ht(l,te=>P(te)),[J,ge]=E.useState(null),[Te,Ce]=E.useState(null),ne=qo(i),[ce,fe]=E.useState(!1),ve=E.useRef(!1);E.useEffect(()=>{if(H)return Ww(H)},[H]),u1();const O=E.useCallback(te=>{const[we,...Xe]=ne().map(Ke=>Ke.ref.current),[Ue]=Xe.slice(-1),Ze=document.activeElement;for(const Ke of te)if(Ke===Ze||(Ke?.scrollIntoView({block:"nearest"}),Ke===we&&Z&&(Z.scrollTop=0),Ke===Ue&&Z&&(Z.scrollTop=Z.scrollHeight),Ke?.focus(),document.activeElement!==Ze))return},[ne,Z]),G=E.useCallback(()=>O([J,H]),[O,J,H]);E.useEffect(()=>{ce&&G()},[ce,G]);const{onOpenChange:V,triggerPointerDownPosRef:pe}=j;E.useEffect(()=>{if(H){let te={x:0,y:0};const we=Ue=>{te={x:Math.abs(Math.round(Ue.pageX)-(pe.current?.x??0)),y:Math.abs(Math.round(Ue.pageY)-(pe.current?.y??0))}},Xe=Ue=>{te.x<=10&&te.y<=10?Ue.preventDefault():H.contains(Ue.target)||V(!1),document.removeEventListener("pointermove",we),pe.current=null};return pe.current!==null&&(document.addEventListener("pointermove",we),document.addEventListener("pointerup",Xe,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",we),document.removeEventListener("pointerup",Xe,{capture:!0})}}},[H,V,pe]),E.useEffect(()=>{const te=()=>V(!1);return window.addEventListener("blur",te),window.addEventListener("resize",te),()=>{window.removeEventListener("blur",te),window.removeEventListener("resize",te)}},[V]);const[w,K]=wb(te=>{const we=ne().filter(Ze=>!Ze.disabled),Xe=we.find(Ze=>Ze.ref.current===document.activeElement),Ue=Eb(we,te,Xe);Ue&&setTimeout(()=>Ue.ref.current.focus())}),ae=E.useCallback((te,we,Xe)=>{const Ue=!ve.current&&!Xe;(j.value!==void 0&&j.value===we||Ue)&&(ge(te),Ue&&(ve.current=!0))},[j.value]),W=E.useCallback(()=>H?.focus(),[H]),re=E.useCallback((te,we,Xe)=>{const Ue=!ve.current&&!Xe;(j.value!==void 0&&j.value===we||Ue)&&Ce(te)},[j.value]),Re=s==="popper"?Bf:ub,Se=Re===Bf?{side:h,sideOffset:p,align:v,alignOffset:b,arrowPadding:S,collisionBoundary:T,collisionPadding:z,sticky:L,hideWhenDetached:_,avoidCollisions:N}:{};return g.jsx(sb,{scope:i,content:H,viewport:Z,onViewportChange:se,itemRefCallback:ae,selectedItem:J,onItemLeave:W,itemTextRefCallback:re,focusSelectedItem:G,selectedItemText:Te,position:s,isPositioned:ce,searchRef:w,children:g.jsx(Wy,{as:GE,allowPinchZoom:!0,children:g.jsx(yy,{asChild:!0,trapped:j.open,onMountAutoFocus:te=>{te.preventDefault()},onUnmountAutoFocus:Ct(u,te=>{j.trigger?.focus({preventScroll:!0}),te.preventDefault()}),children:g.jsx(py,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:f,onPointerDownOutside:d,onFocusOutside:te=>te.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:g.jsx(Re,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:te=>te.preventDefault(),...q,...Se,onPlaced:()=>fe(!0),ref:$,style:{display:"flex",flexDirection:"column",outline:"none",...q.style},onKeyDown:Ct(q.onKeyDown,te=>{const we=te.ctrlKey||te.altKey||te.metaKey;if(te.key==="Tab"&&te.preventDefault(),!we&&te.key.length===1&&K(te.key),["ArrowUp","ArrowDown","Home","End"].includes(te.key)){let Ue=ne().filter(Ze=>!Ze.disabled).map(Ze=>Ze.ref.current);if(["ArrowUp","End"].includes(te.key)&&(Ue=Ue.slice().reverse()),["ArrowUp","ArrowDown"].includes(te.key)){const Ze=te.target,Ke=Ue.indexOf(Ze);Ue=Ue.slice(Ke+1)}setTimeout(()=>O(Ue)),te.preventDefault()}})})})})})})});ob.displayName=YE;var XE="SelectItemAlignedPosition",ub=E.forwardRef((a,l)=>{const{__scopeSelect:i,onPlaced:s,...u}=a,f=$a(El,i),d=Ja(El,i),[h,p]=E.useState(null),[v,b]=E.useState(null),S=Ht(l,$=>b($)),T=qo(i),z=E.useRef(!1),L=E.useRef(!0),{viewport:_,selectedItem:N,selectedItemText:q,focusSelectedItem:j}=d,H=E.useCallback(()=>{if(f.trigger&&f.valueNode&&h&&v&&_&&N&&q){const $=f.trigger.getBoundingClientRect(),J=v.getBoundingClientRect(),ge=f.valueNode.getBoundingClientRect(),Te=q.getBoundingClientRect();if(f.dir!=="rtl"){const Ze=Te.left-J.left,Ke=ge.left-Ze,Mt=$.left-Ke,Kt=$.width+Mt,ma=Math.max(Kt,J.width),Wa=window.innerWidth-On,qt=$p(Ke,[On,Math.max(On,Wa-ma)]);h.style.minWidth=Kt+"px",h.style.left=qt+"px"}else{const Ze=J.right-Te.right,Ke=window.innerWidth-ge.right-Ze,Mt=window.innerWidth-$.right-Ke,Kt=$.width+Mt,ma=Math.max(Kt,J.width),Wa=window.innerWidth-On,qt=$p(Ke,[On,Math.max(On,Wa-ma)]);h.style.minWidth=Kt+"px",h.style.right=qt+"px"}const Ce=T(),ne=window.innerHeight-On*2,ce=_.scrollHeight,fe=window.getComputedStyle(v),ve=parseInt(fe.borderTopWidth,10),O=parseInt(fe.paddingTop,10),G=parseInt(fe.borderBottomWidth,10),V=parseInt(fe.paddingBottom,10),pe=ve+O+ce+V+G,w=Math.min(N.offsetHeight*5,pe),K=window.getComputedStyle(_),ae=parseInt(K.paddingTop,10),W=parseInt(K.paddingBottom,10),re=$.top+$.height/2-On,Re=ne-re,Se=N.offsetHeight/2,te=N.offsetTop+Se,we=ve+O+te,Xe=pe-we;if(we<=re){const Ze=Ce.length>0&&N===Ce[Ce.length-1].ref.current;h.style.bottom="0px";const Ke=v.clientHeight-_.offsetTop-_.offsetHeight,Mt=Math.max(Re,Se+(Ze?W:0)+Ke+G),Kt=we+Mt;h.style.height=Kt+"px"}else{const Ze=Ce.length>0&&N===Ce[0].ref.current;h.style.top="0px";const Mt=Math.max(re,ve+_.offsetTop+(Ze?ae:0)+Se)+Xe;h.style.height=Mt+"px",_.scrollTop=we-re+_.offsetTop}h.style.margin=`${On}px 0`,h.style.minHeight=w+"px",h.style.maxHeight=ne+"px",s?.(),requestAnimationFrame(()=>z.current=!0)}},[T,f.trigger,f.valueNode,h,v,_,N,q,f.dir,s]);en(()=>H(),[H]);const[P,Z]=E.useState();en(()=>{v&&Z(window.getComputedStyle(v).zIndex)},[v]);const se=E.useCallback($=>{$&&L.current===!0&&(H(),j?.(),L.current=!1)},[H,j]);return g.jsx(KE,{scope:i,contentWrapper:h,shouldExpandOnScrollRef:z,onScrollButtonChange:se,children:g.jsx("div",{ref:p,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:P},children:g.jsx(pt.div,{...u,ref:S,style:{boxSizing:"border-box",maxHeight:"100%",...u.style}})})})});ub.displayName=XE;var QE="SelectPopperPosition",Bf=E.forwardRef((a,l)=>{const{__scopeSelect:i,align:s="start",collisionPadding:u=On,...f}=a,d=Zo(i);return g.jsx(Hw,{...d,...f,ref:l,align:s,collisionPadding:u,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Bf.displayName=QE;var[KE,fd]=Or(El,{}),Lf="SelectViewport",cb=E.forwardRef((a,l)=>{const{__scopeSelect:i,nonce:s,...u}=a,f=Ja(Lf,i),d=fd(Lf,i),h=Ht(l,f.onViewportChange),p=E.useRef(0);return g.jsxs(g.Fragment,{children:[g.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:s}),g.jsx(Ho.Slot,{scope:i,children:g.jsx(pt.div,{"data-radix-select-viewport":"",role:"presentation",...u,ref:h,style:{position:"relative",flex:1,overflow:"hidden auto",...u.style},onScroll:Ct(u.onScroll,v=>{const b=v.currentTarget,{contentWrapper:S,shouldExpandOnScrollRef:T}=d;if(T?.current&&S){const z=Math.abs(p.current-b.scrollTop);if(z>0){const L=window.innerHeight-On*2,_=parseFloat(S.style.minHeight),N=parseFloat(S.style.height),q=Math.max(_,N);if(q<L){const j=q+z,H=Math.min(L,j),P=j-H;S.style.height=H+"px",S.style.bottom==="0px"&&(b.scrollTop=P>0?P:0,S.style.justifyContent="flex-end")}}}p.current=b.scrollTop})})})]})});cb.displayName=Lf;var fb="SelectGroup",[PE,FE]=Or(fb),$E=E.forwardRef((a,l)=>{const{__scopeSelect:i,...s}=a,u=td();return g.jsx(PE,{scope:i,id:u,children:g.jsx(pt.div,{role:"group","aria-labelledby":u,...s,ref:l})})});$E.displayName=fb;var db="SelectLabel",JE=E.forwardRef((a,l)=>{const{__scopeSelect:i,...s}=a,u=FE(db,i);return g.jsx(pt.div,{id:u.id,...s,ref:l})});JE.displayName=db;var Mo="SelectItem",[WE,hb]=Or(Mo),mb=E.forwardRef((a,l)=>{const{__scopeSelect:i,value:s,disabled:u=!1,textValue:f,...d}=a,h=$a(Mo,i),p=Ja(Mo,i),v=h.value===s,[b,S]=E.useState(f??""),[T,z]=E.useState(!1),L=Ht(l,j=>p.itemRefCallback?.(j,s,u)),_=td(),N=E.useRef("touch"),q=()=>{u||(h.onValueChange(s),h.onOpenChange(!1))};if(s==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return g.jsx(WE,{scope:i,value:s,disabled:u,textId:_,isSelected:v,onItemTextChange:E.useCallback(j=>{S(H=>H||(j?.textContent??"").trim())},[]),children:g.jsx(Ho.ItemSlot,{scope:i,value:s,disabled:u,textValue:b,children:g.jsx(pt.div,{role:"option","aria-labelledby":_,"data-highlighted":T?"":void 0,"aria-selected":v&&T,"data-state":v?"checked":"unchecked","aria-disabled":u||void 0,"data-disabled":u?"":void 0,tabIndex:u?void 0:-1,...d,ref:L,onFocus:Ct(d.onFocus,()=>z(!0)),onBlur:Ct(d.onBlur,()=>z(!1)),onClick:Ct(d.onClick,()=>{N.current!=="mouse"&&q()}),onPointerUp:Ct(d.onPointerUp,()=>{N.current==="mouse"&&q()}),onPointerDown:Ct(d.onPointerDown,j=>{N.current=j.pointerType}),onPointerMove:Ct(d.onPointerMove,j=>{N.current=j.pointerType,u?p.onItemLeave?.():N.current==="mouse"&&j.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Ct(d.onPointerLeave,j=>{j.currentTarget===document.activeElement&&p.onItemLeave?.()}),onKeyDown:Ct(d.onKeyDown,j=>{p.searchRef?.current!==""&&j.key===" "||(UE.includes(j.key)&&q(),j.key===" "&&j.preventDefault())})})})})});mb.displayName=Mo;var ji="SelectItemText",vb=E.forwardRef((a,l)=>{const{__scopeSelect:i,className:s,style:u,...f}=a,d=$a(ji,i),h=Ja(ji,i),p=hb(ji,i),v=HE(ji,i),[b,S]=E.useState(null),T=Ht(l,q=>S(q),p.onItemTextChange,q=>h.itemTextRefCallback?.(q,p.value,p.disabled)),z=b?.textContent,L=E.useMemo(()=>g.jsx("option",{value:p.value,disabled:p.disabled,children:z},p.value),[p.disabled,p.value,z]),{onNativeOptionAdd:_,onNativeOptionRemove:N}=v;return en(()=>(_(L),()=>N(L)),[_,N,L]),g.jsxs(g.Fragment,{children:[g.jsx(pt.span,{id:p.textId,...f,ref:T}),p.isSelected&&d.valueNode&&!d.valueNodeHasChildren?qi.createPortal(f.children,d.valueNode):null]})});vb.displayName=ji;var pb="SelectItemIndicator",gb=E.forwardRef((a,l)=>{const{__scopeSelect:i,...s}=a;return hb(pb,i).isSelected?g.jsx(pt.span,{"aria-hidden":!0,...s,ref:l}):null});gb.displayName=pb;var Hf="SelectScrollUpButton",yb=E.forwardRef((a,l)=>{const i=Ja(Hf,a.__scopeSelect),s=fd(Hf,a.__scopeSelect),[u,f]=E.useState(!1),d=Ht(l,s.onScrollButtonChange);return en(()=>{if(i.viewport&&i.isPositioned){let h=function(){const v=p.scrollTop>0;f(v)};const p=i.viewport;return h(),p.addEventListener("scroll",h),()=>p.removeEventListener("scroll",h)}},[i.viewport,i.isPositioned]),u?g.jsx(xb,{...a,ref:d,onAutoScroll:()=>{const{viewport:h,selectedItem:p}=i;h&&p&&(h.scrollTop=h.scrollTop-p.offsetHeight)}}):null});yb.displayName=Hf;var qf="SelectScrollDownButton",bb=E.forwardRef((a,l)=>{const i=Ja(qf,a.__scopeSelect),s=fd(qf,a.__scopeSelect),[u,f]=E.useState(!1),d=Ht(l,s.onScrollButtonChange);return en(()=>{if(i.viewport&&i.isPositioned){let h=function(){const v=p.scrollHeight-p.clientHeight,b=Math.ceil(p.scrollTop)<v;f(b)};const p=i.viewport;return h(),p.addEventListener("scroll",h),()=>p.removeEventListener("scroll",h)}},[i.viewport,i.isPositioned]),u?g.jsx(xb,{...a,ref:d,onAutoScroll:()=>{const{viewport:h,selectedItem:p}=i;h&&p&&(h.scrollTop=h.scrollTop+p.offsetHeight)}}):null});bb.displayName=qf;var xb=E.forwardRef((a,l)=>{const{__scopeSelect:i,onAutoScroll:s,...u}=a,f=Ja("SelectScrollButton",i),d=E.useRef(null),h=qo(i),p=E.useCallback(()=>{d.current!==null&&(window.clearInterval(d.current),d.current=null)},[]);return E.useEffect(()=>()=>p(),[p]),en(()=>{h().find(b=>b.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[h]),g.jsx(pt.div,{"aria-hidden":!0,...u,ref:l,style:{flexShrink:0,...u.style},onPointerDown:Ct(u.onPointerDown,()=>{d.current===null&&(d.current=window.setInterval(s,50))}),onPointerMove:Ct(u.onPointerMove,()=>{f.onItemLeave?.(),d.current===null&&(d.current=window.setInterval(s,50))}),onPointerLeave:Ct(u.onPointerLeave,()=>{p()})})}),IE="SelectSeparator",eA=E.forwardRef((a,l)=>{const{__scopeSelect:i,...s}=a;return g.jsx(pt.div,{"aria-hidden":!0,...s,ref:l})});eA.displayName=IE;var Zf="SelectArrow",tA=E.forwardRef((a,l)=>{const{__scopeSelect:i,...s}=a,u=Zo(i),f=$a(Zf,i),d=Ja(Zf,i);return f.open&&d.position==="popper"?g.jsx(qw,{...u,...s,ref:l}):null});tA.displayName=Zf;var nA="SelectBubbleInput",Sb=E.forwardRef(({__scopeSelect:a,value:l,...i},s)=>{const u=E.useRef(null),f=Ht(s,u),d=Qw(l);return E.useEffect(()=>{const h=u.current;if(!h)return;const p=window.HTMLSelectElement.prototype,b=Object.getOwnPropertyDescriptor(p,"value").set;if(d!==l&&b){const S=new Event("change",{bubbles:!0});b.call(h,l),h.dispatchEvent(S)}},[d,l]),g.jsx(pt.select,{...i,style:{...Yy,...i.style},ref:f,defaultValue:l})});Sb.displayName=nA;function _b(a){return a===""||a===void 0}function wb(a){const l=Sl(a),i=E.useRef(""),s=E.useRef(0),u=E.useCallback(d=>{const h=i.current+d;l(h),function p(v){i.current=v,window.clearTimeout(s.current),v!==""&&(s.current=window.setTimeout(()=>p(""),1e3))}(h)},[l]),f=E.useCallback(()=>{i.current="",window.clearTimeout(s.current)},[]);return E.useEffect(()=>()=>window.clearTimeout(s.current),[]),[i,u,f]}function Eb(a,l,i){const u=l.length>1&&Array.from(l).every(v=>v===l[0])?l[0]:l,f=i?a.indexOf(i):-1;let d=aA(a,Math.max(f,0));u.length===1&&(d=d.filter(v=>v!==i));const p=d.find(v=>v.textValue.toLowerCase().startsWith(u.toLowerCase()));return p!==i?p:void 0}function aA(a,l){return a.map((i,s)=>a[(l+s)%a.length])}var lA=Iy,rA=tb,iA=ab,sA=lb,oA=rb,uA=ib,cA=cb,fA=mb,dA=vb,hA=gb,mA=yb,vA=bb;/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pA=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),gA=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(l,i,s)=>s?s.toUpperCase():i.toLowerCase()),xg=a=>{const l=gA(a);return l.charAt(0).toUpperCase()+l.slice(1)},Ab=(...a)=>a.filter((l,i,s)=>!!l&&l.trim()!==""&&s.indexOf(l)===i).join(" ").trim(),yA=a=>{for(const l in a)if(l.startsWith("aria-")||l==="role"||l==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var bA={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xA=E.forwardRef(({color:a="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:u="",children:f,iconNode:d,...h},p)=>E.createElement("svg",{ref:p,...bA,width:l,height:l,stroke:a,strokeWidth:s?Number(i)*24/Number(l):i,className:Ab("lucide",u),...!f&&!yA(h)&&{"aria-hidden":"true"},...h},[...d.map(([v,b])=>E.createElement(v,b)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dd=(a,l)=>{const i=E.forwardRef(({className:s,...u},f)=>E.createElement(xA,{ref:f,iconNode:l,className:Ab(`lucide-${pA(xg(a))}`,`lucide-${a}`,s),...u}));return i.displayName=xg(a),i};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const SA=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],_A=dd("check",SA);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wA=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Cb=dd("chevron-down",wA);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const EA=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],AA=dd("chevron-up",EA);function CA({...a}){return g.jsx(lA,{"data-slot":"select",...a})}function TA({...a}){return g.jsx(iA,{"data-slot":"select-value",...a})}function OA({className:a,size:l="default",children:i,...s}){return g.jsxs(rA,{"data-slot":"select-trigger","data-size":l,className:jt("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...s,children:[i,g.jsx(sA,{asChild:!0,children:g.jsx(Cb,{className:"size-4 opacity-50"})})]})}function RA({className:a,children:l,position:i="popper",...s}){return g.jsx(oA,{children:g.jsxs(uA,{"data-slot":"select-content",className:jt("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",i==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...s,children:[g.jsx(NA,{}),g.jsx(cA,{className:jt("p-1",i==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:l}),g.jsx(jA,{})]})})}function Sg({className:a,children:l,...i}){return g.jsxs(fA,{"data-slot":"select-item",className:jt("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...i,children:[g.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:g.jsx(hA,{children:g.jsx(_A,{className:"size-4"})})}),g.jsx(dA,{children:l})]})}function NA({className:a,...l}){return g.jsx(mA,{"data-slot":"select-scroll-up-button",className:jt("flex cursor-default items-center justify-center py-1",a),...l,children:g.jsx(AA,{className:"size-4"})})}function jA({className:a,...l}){return g.jsx(vA,{"data-slot":"select-scroll-down-button",className:jt("flex cursor-default items-center justify-center py-1",a),...l,children:g.jsx(Cb,{className:"size-4"})})}var un=(a=>(a[a.Residential=1]="Residential",a[a.Business=2]="Business",a))(un||{});const MA=oy({customerType:L_(un,{errorMap:()=>({message:"Please select a customer type"})}),name:vt().min(1,"Customer name is required").max(200,"Customer name cannot exceed 200 characters"),bulstatNumber:vt().optional(),vatNumber:vt().max(20,"VAT number cannot exceed 20 characters").optional().or(fn("")),personalId:vt().optional(),address:vt().min(1,"Address is required").max(500,"Address cannot exceed 500 characters"),city:vt().max(100,"City cannot exceed 100 characters").optional().or(fn("")),postalCode:vt().max(10,"Postal code cannot exceed 10 characters").optional().or(fn("")),country:vt().max(100,"Country cannot exceed 100 characters").default("Bulgaria"),phone:vt().max(20,"Phone number cannot exceed 20 characters").optional().or(fn("")),email:vt().email("Invalid email format").max(100,"Email cannot exceed 100 characters").optional().or(fn("")),contactPerson:vt().max(100,"Contact person name cannot exceed 100 characters").optional().or(fn(""))}).refine(a=>a.customerType===un.Business?a.bulstatNumber&&a.bulstatNumber.length>=9&&a.bulstatNumber.length<=13&&/^\d{9,13}$/.test(a.bulstatNumber):a.customerType===un.Residential?a.personalId&&a.personalId.length===10&&/^\d{10}$/.test(a.personalId):!1,{message:"Business customers require a valid ЕИК/Булстат (9-13 digits), residential customers require a valid ЕГН (10 digits)",path:["bulstatNumber"]});function DA({onSuccess:a,onCancel:l}){const[i,s]=E.useState(!1),[u,f]=E.useState(null),d=Jg({resolver:ly(MA),defaultValues:{customerType:un.Business,name:"",bulstatNumber:"",vatNumber:"",personalId:"",address:"",city:"",postalCode:"",country:"Bulgaria",phone:"",email:"",contactPerson:""}}),h=d.watch("customerType"),p=async v=>{s(!0),f(null);try{const b={name:v.name,customerType:v.customerType,address:v.address,country:v.country,bulstatNumber:v.customerType===un.Business?v.bulstatNumber:void 0,personalId:v.customerType===un.Residential?v.personalId:void 0,vatNumber:v.vatNumber||void 0,city:v.city||void 0,postalCode:v.postalCode||void 0,phone:v.phone||void 0,email:v.email||void 0,contactPerson:v.contactPerson||void 0},S=await my.createCustomer(b);a?.(S),d.reset()}catch(b){const S=b;f(S.message),S.errors&&S.errors.forEach(T=>{d.setError(T.field,{type:"server",message:T.message})})}finally{s(!1)}};return g.jsxs(mo,{className:"w-full max-w-2xl mx-auto",children:[g.jsxs(vo,{children:[g.jsx(po,{children:"Create New Customer"}),g.jsx(go,{children:"Add a new customer to your invoicing system. Choose between residential and business customers."})]}),g.jsx(yo,{children:g.jsx(fy,{...d,children:g.jsxs("form",{onSubmit:d.handleSubmit(p),className:"space-y-6",children:[u&&g.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:u}),g.jsx(ot,{control:d.control,name:"customerType",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Customer Type *"}),g.jsxs(CA,{onValueChange:b=>v.onChange(Number(b)),defaultValue:v.value?.toString(),children:[g.jsx(ft,{children:g.jsx(OA,{children:g.jsx(TA,{placeholder:"Select customer type"})})}),g.jsxs(RA,{children:[g.jsx(Sg,{value:un.Business.toString(),children:"Business Customer"}),g.jsx(Sg,{value:un.Residential.toString(),children:"Residential Customer"})]})]}),g.jsx(uo,{children:"Business customers require ЕИК/Булстат, residential customers require ЕГН"}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"name",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Customer Name *"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"Enter customer name",...v})}),g.jsx(dt,{})]})}),g.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[h===un.Business&&g.jsxs(g.Fragment,{children:[g.jsx(ot,{control:d.control,name:"bulstatNumber",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"ЕИК/Булстат Number *"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"*********",...v})}),g.jsx(uo,{children:"9-13 digit Bulgarian business identification number"}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"vatNumber",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"VAT Number"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"BG*********",...v})}),g.jsx(dt,{})]})})]}),h===un.Residential&&g.jsx(ot,{control:d.control,name:"personalId",render:({field:v})=>g.jsxs(ut,{className:"md:col-span-2",children:[g.jsx(ct,{children:"Personal ID (ЕГН) *"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"*********0",...v})}),g.jsx(uo,{children:"10-digit Bulgarian personal identification number"}),g.jsx(dt,{})]})})]}),g.jsx(ot,{control:d.control,name:"address",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Address *"}),g.jsx(ft,{children:g.jsx(uy,{placeholder:"Enter customer address",className:"min-h-[80px]",...v})}),g.jsx(dt,{})]})}),g.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[g.jsx(ot,{control:d.control,name:"city",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"City"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"Sofia",...v})}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"postalCode",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Postal Code"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"1000",...v})}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"country",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Country"}),g.jsx(ft,{children:g.jsx(At,{...v})}),g.jsx(dt,{})]})})]}),g.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[g.jsx(ot,{control:d.control,name:"phone",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Phone"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"+359 2 123 4567",...v})}),g.jsx(dt,{})]})}),g.jsx(ot,{control:d.control,name:"email",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Email"}),g.jsx(ft,{children:g.jsx(At,{type:"email",placeholder:"<EMAIL>",...v})}),g.jsx(dt,{})]})})]}),h===un.Business&&g.jsx(ot,{control:d.control,name:"contactPerson",render:({field:v})=>g.jsxs(ut,{children:[g.jsx(ct,{children:"Contact Person"}),g.jsx(ft,{children:g.jsx(At,{placeholder:"John Doe",...v})}),g.jsx(dt,{})]})}),g.jsxs("div",{className:"flex gap-4 pt-4",children:[g.jsx(ca,{type:"submit",disabled:i,className:"flex-1",children:i?"Creating...":"Create Customer"}),l&&g.jsx(ca,{type:"button",variant:"outline",onClick:l,children:"Cancel"})]})]})})})]})}function zA(){const[a,l]=E.useState(null),i=u=>{l(u)},s=()=>{l(null)};return a?g.jsx("div",{className:"container mx-auto py-8 px-4",children:g.jsxs("div",{className:"max-w-2xl mx-auto",children:[g.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-lg mb-6",children:[g.jsx("h2",{className:"text-lg font-semibold mb-2",children:"✅ Customer Created Successfully!"}),g.jsxs("p",{className:"mb-4",children:[g.jsx("strong",{children:a.name})," has been added to your system."]}),g.jsxs("div",{className:"space-y-1 text-sm",children:[g.jsxs("p",{children:[g.jsx("strong",{children:"Type:"})," ",a.customerType===un.Business?"Business Customer":"Residential Customer"]}),a.bulstatNumber&&g.jsxs("p",{children:[g.jsx("strong",{children:"ЕИК/Булстат:"})," ",a.bulstatNumber]}),a.personalId&&g.jsxs("p",{children:[g.jsx("strong",{children:"Personal ID (ЕГН):"})," ",a.personalId]}),a.vatNumber&&g.jsxs("p",{children:[g.jsx("strong",{children:"VAT Number:"})," ",a.vatNumber]}),g.jsxs("p",{children:[g.jsx("strong",{children:"Address:"})," ",a.address]}),a.city&&g.jsxs("p",{children:[g.jsx("strong",{children:"City:"})," ",a.city,", ",a.country]}),a.phone&&g.jsxs("p",{children:[g.jsx("strong",{children:"Phone:"})," ",a.phone]}),a.email&&g.jsxs("p",{children:[g.jsx("strong",{children:"Email:"})," ",a.email]}),a.contactPerson&&g.jsxs("p",{children:[g.jsx("strong",{children:"Contact Person:"})," ",a.contactPerson]})]})]}),g.jsxs("div",{className:"flex gap-4 justify-center",children:[g.jsx("button",{onClick:s,className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md transition-colors",children:"Create Another Customer"}),g.jsx("button",{onClick:()=>{console.log("Navigate to customers list")},className:"bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md transition-colors",children:"View All Customers"})]})]})}):g.jsxs("div",{className:"container mx-auto py-8 px-4",children:[g.jsxs("div",{className:"mb-8 text-center",children:[g.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Create New Customer"}),g.jsx("p",{className:"text-gray-600",children:"Add a new customer to your invoicing system. Choose between residential and business customers."})]}),g.jsx(DA,{onSuccess:i,onCancel:()=>{console.log("Cancel customer creation")}})]})}function kA(){const[a,l]=E.useState("home"),i=u=>{l(u)},s=()=>{switch(a){case"home":return g.jsx(xp,{onNavigate:i});case"create-company":return g.jsx($_,{});case"create-customer":return g.jsx(zA,{});default:return g.jsx(xp,{onNavigate:i})}};return g.jsxs("div",{className:"min-h-screen bg-gray-50",children:[g.jsx(US,{currentPage:a,onNavigate:i}),g.jsx("main",{children:s()})]})}Px.createRoot(document.getElementById("root")).render(g.jsx(E.StrictMode,{children:g.jsx(kA,{})}));
