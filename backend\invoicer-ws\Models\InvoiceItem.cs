using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace invoicer_ws.Models
{
    /// <summary>
    /// Represents an item/line in an invoice
    /// </summary>
    public class InvoiceItem
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "Invoice is required")]
        public int InvoiceId { get; set; }

        [Required(ErrorMessage = "Description is required")]
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "Quantity is required")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Quantity must be greater than 0")]
        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        [StringLength(20, ErrorMessage = "Unit cannot exceed 20 characters")]
        public string Unit { get; set; } = "бр."; // Default Bulgarian unit

        [Required(ErrorMessage = "Unit price is required")]
        [Range(0, double.MaxValue, ErrorMessage = "Unit price must be non-negative")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Range(0, 100, ErrorMessage = "VAT rate must be between 0 and 100")]
        [Column(TypeName = "decimal(5,2)")]
        public decimal VatRate { get; set; } = 20.0m; // Default Bulgarian VAT rate

        // Calculated fields
        [Column(TypeName = "decimal(18,2)")]
        public decimal LineTotal { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal VatAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal LineTotalWithVat { get; set; }

        public int SortOrder { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("InvoiceId")]
        public virtual Invoice Invoice { get; set; } = null!;

        /// <summary>
        /// Calculates the line totals including VAT
        /// </summary>
        public void CalculateLineTotals()
        {
            LineTotal = Quantity * UnitPrice;
            VatAmount = LineTotal * (VatRate / 100);
            LineTotalWithVat = LineTotal + VatAmount;
        }
    }
}
