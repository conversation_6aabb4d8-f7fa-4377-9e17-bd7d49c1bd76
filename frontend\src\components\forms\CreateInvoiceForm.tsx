import React, { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { Plus, Trash2, Calculator } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";

import { apiService } from "@/services/api";
import { Company, Customer, CreateInvoiceRequest, InvoiceStatus } from "@/types";

// Validation schema
const invoiceItemSchema = z.object({
  description: z.string().min(1, "Description is required").max(500, "Description cannot exceed 500 characters"),
  quantity: z.number().min(0.001, "Quantity must be greater than 0").max(999999, "Quantity too large"),
  unit: z.string().min(1, "Unit is required").max(20, "Unit cannot exceed 20 characters"),
  unitPrice: z.number().min(0, "Unit price cannot be negative").max(999999, "Unit price too large"),
  vatRate: z.number().min(0, "VAT rate cannot be negative").max(100, "VAT rate cannot exceed 100%"),
  sortOrder: z.number().min(0),
});

const invoiceSchema = z.object({
  invoiceNumber: z.string().min(1, "Invoice number is required").max(50, "Invoice number cannot exceed 50 characters"),
  companyId: z.number().min(1, "Company is required"),
  customerId: z.number().min(1, "Customer is required"),
  invoiceDate: z.date({ required_error: "Invoice date is required" }),
  dueDate: z.date({ required_error: "Due date is required" }),
  status: z.nativeEnum(InvoiceStatus).optional(),
  notes: z.string().max(1000, "Notes cannot exceed 1000 characters").optional(),
  terms: z.string().max(1000, "Terms cannot exceed 1000 characters").optional(),
  currency: z.string().min(1, "Currency is required").max(3, "Currency code cannot exceed 3 characters"),
  invoiceItems: z.array(invoiceItemSchema).min(1, "At least one invoice item is required"),
});

type InvoiceFormData = z.infer<typeof invoiceSchema>;

interface CreateInvoiceFormProps {
  onSuccess?: (invoice: any) => void;
  onError?: (error: string) => void;
}

export function CreateInvoiceForm({ onSuccess, onError }: CreateInvoiceFormProps) {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);

  const form = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceSchema),
    defaultValues: {
      invoiceNumber: "",
      companyId: 0,
      customerId: 0,
      invoiceDate: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      status: InvoiceStatus.Draft,
      notes: "",
      terms: "",
      currency: "BGN",
      invoiceItems: [
        {
          description: "",
          quantity: 1,
          unit: "бр.",
          unitPrice: 0,
          vatRate: 20,
          sortOrder: 0,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "invoiceItems",
  });

  // Load companies and customers
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoadingData(true);
        const [companiesData, customersData] = await Promise.all([apiService.getCompanies(), apiService.getCustomers()]);
        setCompanies(companiesData);
        setCustomers(customersData);
      } catch (error) {
        console.error("Failed to load data:", error);
        onError?.("Failed to load companies and customers");
      } finally {
        setIsLoadingData(false);
      }
    };

    loadData();
  }, [onError]);

  // Calculate totals for each item and overall totals
  const watchedItems = form.watch("invoiceItems");
  const calculatedTotals = React.useMemo(() => {
    let subTotal = 0;
    let vatAmount = 0;

    const itemsWithTotals = watchedItems.map((item) => {
      const lineTotal = item.quantity * item.unitPrice;
      const itemVatAmount = lineTotal * (item.vatRate / 100);
      const lineTotalWithVat = lineTotal + itemVatAmount;

      subTotal += lineTotal;
      vatAmount += itemVatAmount;

      return {
        lineTotal,
        vatAmount: itemVatAmount,
        lineTotalWithVat,
      };
    });

    const totalAmount = subTotal + vatAmount;

    return {
      items: itemsWithTotals,
      subTotal,
      vatAmount,
      totalAmount,
    };
  }, [watchedItems]);

  const addInvoiceItem = () => {
    append({
      description: "",
      quantity: 1,
      unit: "бр.",
      unitPrice: 0,
      vatRate: 20,
      sortOrder: fields.length,
    });
  };

  const removeInvoiceItem = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const onSubmit = async (data: InvoiceFormData) => {
    try {
      setIsLoading(true);

      // Prepare the request data
      const invoiceRequest: CreateInvoiceRequest = {
        invoiceNumber: data.invoiceNumber,
        companyId: data.companyId,
        customerId: data.customerId,
        invoiceDate: format(data.invoiceDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        dueDate: format(data.dueDate, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        status: data.status || InvoiceStatus.Draft,
        notes: data.notes,
        terms: data.terms,
        currency: data.currency,
        invoiceItems: data.invoiceItems.map((item, index) => ({
          description: item.description,
          quantity: item.quantity,
          unit: item.unit,
          unitPrice: item.unitPrice,
          vatRate: item.vatRate,
          sortOrder: index,
        })),
      };

      const result = await apiService.createInvoice(invoiceRequest);
      onSuccess?.(result);
    } catch (error: any) {
      console.error("Failed to create invoice:", error);
      onError?.(error.message || "Failed to create invoice");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="text-muted-foreground">Loading companies and customers...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          Create New Invoice
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Invoice Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="invoiceNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Invoice Number *</FormLabel>
                    <FormControl>
                      <Input placeholder="INV-2024-001" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Currency *</FormLabel>
                    <FormControl>
                      <Input placeholder="BGN" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Company and Customer Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="companyId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company *</FormLabel>
                    <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a company" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {companies.map((company) => (
                          <SelectItem key={company.id} value={company.id.toString()}>
                            {company.name} ({company.bulstatNumber})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="customerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer *</FormLabel>
                    <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a customer" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {customers.map((customer) => (
                          <SelectItem key={customer.id} value={customer.id.toString()}>
                            {customer.name} {customer.bulstatNumber && `(${customer.bulstatNumber})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Dates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="invoiceDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Invoice Date *</FormLabel>
                    <FormControl>
                      <DatePicker date={field.value} onDateChange={field.onChange} placeholder="Select invoice date" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Due Date *</FormLabel>
                    <FormControl>
                      <DatePicker date={field.value} onDateChange={field.onChange} placeholder="Select due date" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Status */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                    <FormControl>
                      <SelectTrigger className="w-full md:w-[200px]">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={InvoiceStatus.Draft.toString()}>Draft</SelectItem>
                      <SelectItem value={InvoiceStatus.Sent.toString()}>Sent</SelectItem>
                      <SelectItem value={InvoiceStatus.Paid.toString()}>Paid</SelectItem>
                      <SelectItem value={InvoiceStatus.Overdue.toString()}>Overdue</SelectItem>
                      <SelectItem value={InvoiceStatus.Cancelled.toString()}>Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Invoice Items */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Invoice Items</h3>
                <Button type="button" onClick={addInvoiceItem} size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>

              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">Description</TableHead>
                      <TableHead className="w-[100px]">Quantity</TableHead>
                      <TableHead className="w-[80px]">Unit</TableHead>
                      <TableHead className="w-[120px]">Unit Price</TableHead>
                      <TableHead className="w-[100px]">VAT Rate (%)</TableHead>
                      <TableHead className="w-[120px]">Line Total</TableHead>
                      <TableHead className="w-[120px]">VAT Amount</TableHead>
                      <TableHead className="w-[120px]">Total with VAT</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {fields.map((field, index) => (
                      <TableRow key={field.id}>
                        <TableCell>
                          <FormField
                            control={form.control}
                            name={`invoiceItems.${index}.description`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input placeholder="Item description" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <FormField
                            control={form.control}
                            name={`invoiceItems.${index}.quantity`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input type="number" step="0.001" min="0" {...field} onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <FormField
                            control={form.control}
                            name={`invoiceItems.${index}.unit`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input placeholder="бр." {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <FormField
                            control={form.control}
                            name={`invoiceItems.${index}.unitPrice`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input type="number" step="0.01" min="0" {...field} onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <FormField
                            control={form.control}
                            name={`invoiceItems.${index}.vatRate`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    max="100"
                                    {...field}
                                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </TableCell>
                        <TableCell className="text-right">{calculatedTotals.items[index]?.lineTotal.toFixed(2) || "0.00"} BGN</TableCell>
                        <TableCell className="text-right">{calculatedTotals.items[index]?.vatAmount.toFixed(2) || "0.00"} BGN</TableCell>
                        <TableCell className="text-right">{calculatedTotals.items[index]?.lineTotalWithVat.toFixed(2) || "0.00"} BGN</TableCell>
                        <TableCell>
                          {fields.length > 1 && (
                            <Button type="button" variant="ghost" size="sm" onClick={() => removeInvoiceItem(index)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Totals Summary */}
              <div className="mt-4 flex justify-end">
                <div className="w-full max-w-sm space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span className="font-medium">{calculatedTotals.subTotal.toFixed(2)} BGN</span>
                  </div>
                  <div className="flex justify-between">
                    <span>VAT Amount:</span>
                    <span className="font-medium">{calculatedTotals.vatAmount.toFixed(2)} BGN</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total Amount:</span>
                    <span>{calculatedTotals.totalAmount.toFixed(2)} BGN</span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Notes and Terms */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Additional notes for this invoice..." className="min-h-[100px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="terms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Terms & Conditions</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Payment terms and conditions..." className="min-h-[100px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-2">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Creating..." : "Create Invoice"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
